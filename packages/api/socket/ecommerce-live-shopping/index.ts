import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { <PERSON><PERSON>and<PERSON> } from "../general/message.handler";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { ApplicationType } from "@prisma/client";

export const setupEcommerceLiveShoppingApp = (ecommerce: Namespace) => {
  ecommerce.use(async (socket: IGlobalMiddlewareSocket, next) => {
    const appInstance = await AppInstanceMiddleware(
      ApplicationType.Ecommerce_Live_Shopping,
      null,
      socket.user.companyId,
    );
    if (!appInstance.success) {
      socket.emit('error', { message: 'Either Ecommerce App has not been setup or you are not authorized to use it', success: false, statusCode: 400 });
      return;
    }
    socket.app = { id: appInstance.data.id, name: appInstance.data.name };
    next();
  });
  
  ecommerce.on('connection', (socket: IGlobalMiddlewareSocket) => {
      socket.on('sendMessage', async (data) => {
        await MessageHandler.sendMessage(socket, ecommerce.server, { ...data, productId: data.productId });
      });
    });

    ecommerce.on('orderPlaced', (socket: IGlobalMiddlewareSocket) => {
      socket.emit('ecommerce:orderPlaced', { message: 'Order placed' });
    });

    ecommerce.on('orderCancelled', (socket: IGlobalMiddlewareSocket) => {
      socket.emit('ecommerce:orderCancelled', { message: 'Order cancelled' });
    });

    ecommerce.on('orderCompleted', (socket: IGlobalMiddlewareSocket) => {
      socket.emit('ecommerce:orderCompleted', { message: 'Order completed' });
    });

    ecommerce.on('orderRefunded', (socket: IGlobalMiddlewareSocket) => {
      socket.emit('ecommerce:orderRefunded', { message: 'Order refunded' });
    });

    ecommerce.on('productUpdated', (socket: IGlobalMiddlewareSocket) => {
      socket.emit('ecommerce:productUpdated', { message: 'Product updated' });
    });
  };