import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { AuthService, serviceLocator } from "../../utils";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { ApplicationType } from "@prisma/client";


export const setupHealthcareTeleconsultationApp = (healthcare: Namespace) => {
  healthcare.use(async (socket: IGlobalMiddlewareSocket, next) => {
    const messageService = await serviceLocator.getMessageService();
    const appInstance = await AppInstanceMiddleware(
      ApplicationType.Healthcare_Teleconsultation,
      null,
      socket.user.companyId,
    );
    if (!appInstance.success) {
      socket.emit('error', { message: 'Invalid application for this HealthcareTeleconsultation', success: false, statusCode: 400 });
      return;
    }
    socket.app = { id: appInstance.data.id, name: appInstance.data.name };
    next();
    socket.on('healthcare:sendMessage', async (data) => {
        const encryptedText = await AuthService.hashPassword(data.text);
        const message = await messageService.createMessage({
          text: encryptedText,
          to: data.roomId,
          senderId: socket.user.id,
          isEncrypted: true,
        });
        healthcare.to(data.roomId).emit('healthcare:newMessage', { data: message });
      });
    });
};