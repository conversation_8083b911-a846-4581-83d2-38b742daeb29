import { Server } from 'socket.io';
import { prisma } from '../../utils/prismaClient';
import { RoomHandler } from '../general/room.handler';
import { MessageHandler } from '../general/message.handler';
import { IGlobalMiddlewareSocket } from '../globalSecurity';
import { ICreateMessage } from '../../models';

export const setupCoreSocket = (io: Server) => {
  io.on('connection', (socket: IGlobalMiddlewareSocket) => {
    console.log(`New connection established with ID: ${socket.id}`);
    socket.emit('connected', { message: 'Connected to core namespace' });

    socket.on('joinRoom', async (roomId: string) => {
      await RoomHandler.joinRoom(socket.user.id, roomId, socket, io);
      socket.user.currentRoomId = roomId;
      socket.emit('roomJoined', { roomId });
    });
    socket.on('leaveRoom', async (roomId: string) => {
      await <PERSON><PERSON><PERSON><PERSON>.leaveRoom(socket.user.id, roomId, socket, io);
    });
    socket.on('sendMessage', async (data: ICreateMessage) => {
      await MessageHandler.sendMessage(socket, io, data);
    });
    socket.on('markMessageRead', async (messageId: string) => {
      const message = await prisma.message.update({
        where: { id: messageId },
        data: { read: true },
        select: { to: true },
      });
      io.to(message.to).emit('messageRead', { messageId, userId: socket.user.id });
    });
  });
  return io;
};