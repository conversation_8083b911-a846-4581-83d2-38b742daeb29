import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { RoomHandler } from "../general/room.handler"; 
import { prisma } from "../../utils/prismaClient";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { ApplicationType } from "@prisma/client";

export const setupCommunityApp = (communityNs: Namespace<IGlobalMiddlewareSocket>) => {
    communityNs.use(async (socket: IGlobalMiddlewareSocket, next) => {
      const appInstance = await AppInstanceMiddleware(
        ApplicationType.Community,
        null,
        socket.user.companyId,
      );
      if (!appInstance.success) {
        socket.emit('error', { message: 'Either Community App has not been setup or you are not authorized to use it', success: false, statusCode: 400 });
        return;
      }
      socket.data.app = {
        id: appInstance.data.id,
        name: appInstance.data.name
      };
      next();
    });
    communityNs.on('connection', (socket: IGlobalMiddlewareSocket) => {
      socket.on('joinRoom', async (roomId) => {
        const room = await prisma.room.findUnique({ where: { id: roomId } });
        if (room?.bannedGuestIds.includes(socket.user.id)) {
          socket.emit('error', { message: 'You are banned' });
          return;
        }
        await RoomHandler.joinRoom(socket.user.id, roomId, socket, communityNs.server);
      });
    });
  };