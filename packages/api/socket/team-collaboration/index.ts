import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity/types";
import { prisma } from "../../utils/prismaClient";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { ApplicationType } from "@prisma/client";
import { MessageHandler } from "../general/message.handler";


export const setupTeamCollaborationApp = (teamNs: Namespace) => {
  teamNs.use(async (socket: IGlobalMiddlewareSocket, next) => {
    const appInstance = await AppInstanceMiddleware(
      ApplicationType.Team_Collaboration,
      null,
      socket.user.companyId,
    );
    if (!appInstance.success) {
      socket.emit('error', { message: 'Invalid application for this TeamApplication', success: false, statusCode: 400 });
      return;
    }
    socket.app = { id: appInstance.data.id, name: appInstance.data.name };
    next();
  });
  teamNs.on('connection', (socket: IGlobalMiddlewareSocket) => {
    socket.on('sendMessage', async (data: any) => {
      const role = await prisma.userRole.findFirst({
        where: { userId: socket.user.id, companyId: socket.user.companyId },
        include: { role: true },
      });
      if (!role?.role) {
        socket.emit('error', { message: 'Permission denied' });
        return;
      }
      await MessageHandler.sendMessage(socket, teamNs.server, data);
    });
  });
};