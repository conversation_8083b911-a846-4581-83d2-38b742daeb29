import { io, Socket } from 'socket.io-client';
import { EventEmitter } from 'events';

/**
 * Socket event types for type safety
 */
export enum SocketEvent {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
  RECONNECT_ERROR = 'reconnect_error',
  RECONNECT_FAILED = 'reconnect_failed',

  // Authentication events
  AUTHENTICATED = 'authenticated',
  AUTH_ERROR = 'auth_error',

  // Room events
  JOIN_ROOM = 'joinRoom',
  LEAVE_ROOM = 'leaveRoom',
  ROOM_JOINED = 'roomJoined',
  ROOM_LEFT = 'roomLeft',
  CREATE_ROOM = 'createRoom',
  ROOM_CREATED = 'roomCreated',
  LIST_OF_GUEST_ROOMS = 'listOfGuestRooms',
  USER_ONLINE = 'userOnline',

  // Message events
  SEND_MESSAGE = 'sendMessage',
  NEW_MESSAGE = 'newMessage',
  MESSAGE_READ = 'messageRead',
  MARK_MESSAGE_READ = 'markMessageRead',

  // User events
  USER_JOINED = 'userJoined',
  USER_LEFT = 'userLeft',
  USER_STATUS_CHANGED = 'userStatusChanged',
  SET_USER_STATUS = 'setUserStatus',

  // Error events
  ERROR = 'error',

  // Typing events
  TYPING = 'typing',
  STOP_TYPING = 'stopTyping',
  USER_TYPING = 'userTyping',
  USER_STOPPED_TYPING = 'userStoppedTyping',
}

/**
 * Room types
 */
export enum RoomType {
  DM = 'dm',
  GROUP = 'group',
  SELF = 'self',
  ANONYMOUS = 'anonymous',
}

/**
 * User status types
 */
export enum UserStatus {
  ONLINE = 'online',
  AWAY = 'away',
  OFFLINE = 'offline',
}

/**
 * Message interface
 */
export interface IMessage {
  id?: string;
  text?: string;
  from?: {
    id: string;
    type: string;
  };
  to?: string;
  roomId: string;
  files?: string[];
  createdAt?: Date;
  read?: boolean;
  isPinned?: boolean;
  isAnswered?: boolean;
  isEncrypted?: boolean;
}

/**
 * Room interface
 */
export interface IRoom {
  id: string;
  name: string;
  description?: string;
  type: RoomType;
  membersCount?: number;
  onlineMembersCount?: number;
  messages?: IMessage[];
}

/**
 * User interface
 */
export interface IUser {
  id: string;
  type: 'user' | 'agent' | 'guest' | 'anonymous';
  username?: string;
  status?: UserStatus;
}

/**
 * Socket client options
 */
export interface SocketClientOptions {
  url: string;
  path?: string;
  namespace?: string;
  token?: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  reconnectionDelayMax?: number;
  timeout?: number;
  debug?: boolean;
}

/**
 * Enhanced Socket.IO client wrapper
 */
export class SocketClient extends EventEmitter {
  private socket: Socket;
  private options: SocketClientOptions;
  private messageQueue: IMessage[] = [];
  private connected: boolean = false;
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private debug: boolean = false;
  private eventHistory: Array<{event: string, data: any, timestamp: Date}> = [];

  /**
   * Creates a new SocketClient instance
   * @param options Client options
   */
  constructor(options: SocketClientOptions) {
    super();
    this.options = {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 10,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000,
      debug: false,
      ...options,
    };

    this.debug = this.options.debug;

    const url = this.options.namespace
      ? `${this.options.url}/${this.options.namespace}`
      : this.options.url;

    this.socket = io(url, {
      path: this.options.path,
      autoConnect: this.options.autoConnect,
      reconnection: this.options.reconnection,
      reconnectionAttempts: this.options.reconnectionAttempts,
      reconnectionDelay: this.options.reconnectionDelay,
      reconnectionDelayMax: this.options.reconnectionDelayMax,
      timeout: this.options.timeout,
      auth: this.options.token ? { token: this.options.token } : undefined,
      withCredentials: true,
    });

    this.setupEventListeners();
  }

  /**
   * Set up default event listeners
   */
  private setupEventListeners(): void {
    // Connection events
    this.socket.on(SocketEvent.CONNECT, () => {
      this.connected = true;
      this.log('Connected to server');
      this.emit(SocketEvent.CONNECT);
      this.processMessageQueue();
    });

    this.socket.on(SocketEvent.DISCONNECT, (reason: any) => {
      this.connected = false;
      this.log(`Disconnected from server: ${reason}`);
      this.emit(SocketEvent.DISCONNECT, reason);
    });

    this.socket.on(SocketEvent.CONNECT_ERROR, (error:  any) => {
      this.log(`Connection error: ${error.message}`);
      this.emit(SocketEvent.CONNECT_ERROR, error);
    });

    this.socket.on(SocketEvent.RECONNECT_ATTEMPT, (attempt: any) => {
      this.log(`Reconnection attempt ${attempt}`);
      this.emit(SocketEvent.RECONNECT_ATTEMPT, attempt);
    });

    this.socket.on(SocketEvent.RECONNECT, (attempt: any) => {
      this.connected = true;
      this.log(`Reconnected after ${attempt} attempts`);
      this.emit(SocketEvent.RECONNECT, attempt);
      this.processMessageQueue();
    });

    this.socket.on(SocketEvent.RECONNECT_ERROR, (error: any) => {
      this.log(`Reconnection error: ${error.message}`);
      this.emit(SocketEvent.RECONNECT_ERROR, error);
    });

    this.socket.on(SocketEvent.RECONNECT_FAILED, () => {
      this.log('Failed to reconnect');
      this.emit(SocketEvent.RECONNECT_FAILED);
    });

    // Authentication events
    this.socket.on(SocketEvent.AUTHENTICATED, (data: any) => {
      this.log('Authenticated', data);
      this.emit(SocketEvent.AUTHENTICATED, data);
    });

    // Room events
    this.socket.on(SocketEvent.ROOM_JOINED, (data: any) => {
      this.log('Room joined', data);
      this.emit(SocketEvent.ROOM_JOINED, data);
    });

    this.socket.on(SocketEvent.LIST_OF_GUEST_ROOMS, (data: any) => {
      this.log('List of guest rooms', data);
      this.emit(SocketEvent.LIST_OF_GUEST_ROOMS, data);
    });

    this.socket.on(SocketEvent.USER_ONLINE, (data: any) => {
      this.log('User online', data);
      this.emit(SocketEvent.USER_ONLINE, data);
    });

    this.socket.on(SocketEvent.ROOM_LEFT, (data: any) => {
      this.log('Room left', data);
      this.emit(SocketEvent.ROOM_LEFT, data);
    });

    this.socket.on(SocketEvent.ROOM_CREATED, (data: any) => {
      this.log('Room created', data);
      this.emit(SocketEvent.ROOM_CREATED, data);
    });

    this.socket.on(SocketEvent.LIST_OF_GUEST_ROOMS, (data: any) => {
      this.log('List of guest rooms', data);
      this.emit(SocketEvent.LIST_OF_GUEST_ROOMS, data);
    });

    // Message events
    this.socket.on(SocketEvent.NEW_MESSAGE, (data: any) => {
      this.log('New message received', data);
      this.emit(SocketEvent.NEW_MESSAGE, data);
    });

    this.socket.on(SocketEvent.MESSAGE_READ, (data: any) => {
      this.log('Message read', data);
      this.emit(SocketEvent.MESSAGE_READ, data);
    });

    // User events
    this.socket.on(SocketEvent.USER_JOINED, (data: any) => {
      this.log('User joined', data);
      this.emit(SocketEvent.USER_JOINED, data);
    });

    this.socket.on(SocketEvent.USER_LEFT, (data: any) => {
      this.log('User left', data);
      this.emit(SocketEvent.USER_LEFT, data);
    });

    this.socket.on(SocketEvent.USER_STATUS_CHANGED, (data: any) => {
      this.log('User status changed', data);
      this.emit(SocketEvent.USER_STATUS_CHANGED, data);
    });

    // Typing events
    this.socket.on(SocketEvent.USER_TYPING, (data: any) => {
      this.log('User typing', data);
      this.emit(SocketEvent.USER_TYPING, data);
    });

    this.socket.on(SocketEvent.USER_STOPPED_TYPING, (data: any) => {
      this.log('User stopped typing', data);
      this.emit(SocketEvent.USER_STOPPED_TYPING, data);
    });

    // Error events
    this.socket.on(SocketEvent.ERROR, (error: any) => {
      this.log('Error', error);
      this.emit(SocketEvent.ERROR, error);
    });
  }

  /**
   * Connect to the server
   */
  public connect(): void {
    this.socket.connect();
  }

  /**
   * Disconnect from the server
   */
  public disconnect(): void {
    this.socket.disconnect();
  }

  /**
   * Check if connected to the server
   */
  public isConnected(): boolean {
    return this.connected;
  }

  /**
   * Set authentication token
   * @param token JWT token
   */
  public setToken(token: string): void {
    this.options.token = token;
    // Reconnect with new auth token
    this.socket.disconnect();
    this.socket = io(this.options.url, {
      path: this.options.path,
      auth: { token },
      // Preserve other options
      autoConnect: true,
      reconnection: this.options.reconnection,
      reconnectionAttempts: this.options.reconnectionAttempts,
      reconnectionDelay: this.options.reconnectionDelay,
      reconnectionDelayMax: this.options.reconnectionDelayMax,
      timeout: this.options.timeout,
      withCredentials: true,
    });
    this.setupEventListeners();
    this.socket.connect();
  }

  /**
   * Join a room
   * @param roomId Room ID
   */
  public joinRoom(roomId: string): void {
    this.log(`Joining room ${roomId}`);
    this.socket.emit(SocketEvent.JOIN_ROOM, roomId);
  }

  /**
   * Leave a room
   * @param roomId Room ID
   */
  public leaveRoom(roomId: string): void {
    this.log(`Leaving room ${roomId}`);
    this.socket.emit(SocketEvent.LEAVE_ROOM, roomId);
  }

  /**
   * Create a new room
   * @param data Room data
   */
  public createRoom(data: {
    name: string;
    type: RoomType;
    membersId: string[];
    applicationId?: string;
  }): void {
    this.log('Creating room', data);
    this.socket.emit(SocketEvent.CREATE_ROOM, data);
  }

  /**
   * Send a message
   * @param message Message data
   */
  public sendMessage(message: IMessage): void {
    if (!this.connected) {
      this.log('Not connected, queueing message', message);
      this.messageQueue.push(message);
      return;
    }

    this.log('Sending message', message);
    this.socket.emit(SocketEvent.SEND_MESSAGE, message);
  }

  /**
   * Mark a message as read
   * @param messageId Message ID
   */
  public markMessageRead(messageId: string): void {
    this.log(`Marking message ${messageId} as read`);
    this.socket.emit(SocketEvent.MARK_MESSAGE_READ, messageId);
  }

  /**
   * Set user status
   * @param status User status
   */
  public setUserStatus(status: UserStatus): void {
    this.log(`Setting user status to ${status}`);
    this.socket.emit(SocketEvent.SET_USER_STATUS, status);
  }

  /**
   * Send typing indicator
   * @param roomId Room ID
   */
  public sendTypingIndicator(roomId: string): void {
    this.log(`Sending typing indicator for room ${roomId}`);
    this.socket.emit(SocketEvent.TYPING, { roomId });

    // Clear existing timeout if any
    if (this.typingTimeouts.has(roomId)) {
      clearTimeout(this.typingTimeouts.get(roomId));
    }

    // Set timeout to automatically stop typing after 3 seconds
    const timeout = setTimeout(() => {
      this.sendStopTypingIndicator(roomId);
    }, 3000);

    this.typingTimeouts.set(roomId, timeout);
  }

  /**
   * Send stop typing indicator
   * @param roomId Room ID
   */
  public sendStopTypingIndicator(roomId: string): void {
    this.log(`Sending stop typing indicator for room ${roomId}`);
    this.socket.emit(SocketEvent.STOP_TYPING, { roomId });

    // Clear timeout if exists
    if (this.typingTimeouts.has(roomId)) {
      clearTimeout(this.typingTimeouts.get(roomId));
      this.typingTimeouts.delete(roomId);
    }
  }

  /**
   * Process queued messages
   */
  private processMessageQueue(): void {
    if (this.messageQueue.length === 0) {
      return;
    }

    this.log(`Processing ${this.messageQueue.length} queued messages`);

    // Process messages in batches of 10
    const batchSize = 10;
    const batches = Math.ceil(this.messageQueue.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const batch = this.messageQueue.splice(0, batchSize);

      batch.forEach((message) => {
        this.sendMessage(message);
      });
    }
  }

  /**
   * Log debug messages
   * @param message Log message
   * @param data Optional data
   */
  private log(message: string, data?: any): void {
    if (!this.debug) {
      return;
    }

    if (data) {
      console.log(`[SocketClient] ${message}:`, data);
    } else {
      console.log(`[SocketClient] ${message}`);
    }
  }

  /**
   * Add custom event listener
   * @param event Event name
   * @param listener Event listener
   */
  public on(event: string | symbol, listener: (...args: any[]) => void): this {
    return super.on(event, listener);
  }

  /**
   * Remove custom event listener
   * @param event Event name
   * @param listener Event listener
   */
  public off(event: string | symbol, listener: (...args: any[]) => void): this {
    return super.off(event, listener);
  }

  /**
   * Add one-time event listener
   * @param event Event name
   * @param listener Event listener
   */
  public once(event: string | symbol, listener: (...args: any[]) => void): this {
    return super.once(event, listener);
  }

  /**
   * Emit custom event
   * @param event Event name
   * @param args Event arguments
   */
  public emit(event: string | symbol, ...args: any[]): boolean {
    // Record event in history
    if (typeof event === 'string') {
      this.eventHistory.push({
        event,
        data: args.length > 0 ? args[0] : null,
        timestamp: new Date()
      });
    }
    return super.emit(event, ...args);
  }

  /**
   * Get event history
   * @returns Array of events with timestamps
   */
  public getEventHistory(): Array<{event: string, data: any, timestamp: Date}> {
    return this.eventHistory;
  }
}
