import { IResponse } from "../../utils";
import { getServices, ModelTypes } from "./index";


export async function AppInstanceMiddleware(
    type: ModelTypes.IApplicationType,
    applicationId: string | null,
    companyId: string | null
): Promise<IResponse<ModelTypes.IApplication | null>> {

    if (!type) {
        return {
            success: false,
            message: "Missing type parameter",
            data: null,
            statusCode: 400
        };
    }

    const { applicationService } = getServices();
    let appInstance: IResponse<ModelTypes.IApplication | null>;

    if(applicationId) {
        appInstance = await applicationService.getApplicationByIdAndType(applicationId, type);
    } else if(companyId) {
        appInstance = await applicationService.getApplicationByCompanyIdAndType(companyId, type);
    } else {
        return {
            success: false,
            message: "No applicationId or companyId provided",
            data: null,
            statusCode: 400
        };
    }

    return appInstance;
}
