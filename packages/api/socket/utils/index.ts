import { getLogger } from "../../utils/logger";
import { serviceLocator } from "../../utils/serviceLocator";
import * as Models from "../../models";
import { MessageSenderType } from "@prisma/client";

export const SocketLog = getLogger('socket');

// Function to get services when needed
export const getServices = async () => {
  return await serviceLocator.getSocketServices();
};

export namespace ModelTypes {
    // Application
  export type IApplication = Models.IApplication;
  export type IApplicationType = Models.ApplicationType;

    // Room
  export type IRoom = Models.IRoom;
  export type ICreateRoom = Models.ICreateRoom;
  export type IUpdateRoom = Models.IUpdateRoom;
  export type IRoomType = Models.RoomType;

  // Message
  export type IMessageSenderType = MessageSenderType;
}

export namespace SocketEvent {
  export type SocketEvent = string;
}
