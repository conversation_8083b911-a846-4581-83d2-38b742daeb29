import { getLogger } from "../../utils/logger";
import { container } from "../../v1/container";
import * as Models from "../../models";
import * as Services from "../../v1/services";
import { MessageSenderType } from "@prisma/client";

export const SocketLog = getLogger('socket');

// Function to get services when needed
export const getServices = () => ({
  applicationService: container.resolve<Services.ApplicationService>('applicationService'),
  guestService: container.resolve<Services.GuestService>('guestService'),
  roomService: container.resolve<Services.RoomService>('roomService'),
  messageService: container.resolve<Services.MessageService>('messageService')
});

export namespace ModelTypes {
    // Application
  export type IApplication = Models.IApplication;
  export type IApplicationType = Models.ApplicationType;

    // Room
  export type IRoom = Models.IRoom;
  export type ICreateRoom = Models.ICreateRoom;
  export type IUpdateRoom = Models.IUpdateRoom;
  export type IRoomType = Models.RoomType;

  // Message
  export type IMessageSenderType = MessageSenderType;
}

export namespace SocketEvent {
  export type SocketEvent = string;
}
