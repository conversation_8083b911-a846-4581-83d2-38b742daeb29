/** 
 * Client-Side SocketEvent
 * 
 * The client-side enum includes: 
 * - Events the client emits to the server (e.g., JOIN_ROOM, SEND_MESSAGE).
 * 
 * - Events the client listens for from the server (e.g., NEW_MESSAGE, USER_JOINED).
 * 
 * - Connection-related events (e.g., CONNECT, DISCONNECT).
*/
export enum SocketEvent {
  // Connection events
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
  RECONNECT_ERROR = 'reconnect_error',
  RECONNECT_FAILED = 'reconnect_failed',

  // Authentication events
  AUTHENTICATED = 'authenticated',
  ERROR = 'error',
  DISCONNECTED = 'disconnected',

  // Room events (emitted by client)
  JOIN_ROOM = 'joinRoom',
  LEAVE_ROOM = 'leaveRoom',
  SWITCH_ROOM = 'switchRoom',
  CREATE_ROOM = 'createRoom',
  REMOVE_USER_FROM_GROUP = 'removeUserFromGroup',
  GET_LIST_OF_GUEST_ROOMS = 'getListOfGuestRooms',

  // Room events (received from server)
  ROOM_JOINED = 'roomJoined',
  ROOM_LEFT = 'roomLeft',
  ROOM_SWITCHED = 'roomSwitched',
  ROOM_CREATED = 'roomCreated',
  USER_JOINED = 'userJoined',
  USER_LEFT = 'userLeft',
  USER_ONLINE = 'userOnline',
  LIST_OF_GUEST_ROOMS = 'listOfGuestRooms',
  

  // Message events (emitted by client)
  SEND_MESSAGE = 'sendMessage',
  MARK_MESSAGE_READ = 'markMessageRead',

  // Message events (received from server)
  NEW_MESSAGE = 'newMessage',
  MESSAGE_READ = 'messageRead',

  // User status events (emitted by client)
  SET_USER_STATUS = 'setUserStatus',
  TYPING = 'typing',
  STOP_TYPING = 'stopTyping',

  // User status events (received from server)
  USER_STATUS_CHANGED = 'userStatusChanged',
  USER_TYPING = 'userTyping',
  USER_STOPPED_TYPING = 'userStoppedTyping'
}
