/**
 * Server-Side SocketEvent
 * 
 * The server-side enum includes:
 * 
 * - Events the server emits to clients (e.g., NEW_MESSAGE, ROOM_CREATED).
 * - Events the server listens for from clients (e.g., JOIN_ROOM, SEND_MESSAGE).
 * - Connection-related events (e.g., CONNECTION, DISCONNECT).
 */
export enum SocketEvent {
  // Connection events
  CONNECTION = 'connection',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',


  // Authentication events
  AUTHENTICATED = 'authenticated',
  ERROR = 'error',
  DISCONNECTED = 'disconnected',

  // Room events (listened from client)
  JOIN_ROOM = 'joinRoom',
  LEAVE_ROOM = 'leaveRoom',
  SWITCH_ROOM = 'switchRoom',
  CREATE_ROOM = 'createRoom',
  REMOVE_USER_FROM_GROUP = 'removeUserFromGroup',
  GET_LIST_OF_GUEST_ROOMS = 'getListOfGuestRooms',
  GET_ROOM_DATA_BY_ID = 'getRoomDataById',
  GET_ROOM_Media = 'getRoomMedia',
  GET_ROOM_MESSAGES = 'getRoomMessages',


  // Room events (emitted to client)
  ROOM_JOINED = 'roomJoined',
  ROOM_LEFT = 'roomLeft',
  LIST_OF_GUEST_ROOMS = 'listOfGuestRooms',
  ROOM_SWITCHED = 'roomSwitched',
  ROOM_CREATED = 'roomCreated',
  USER_JOINED = 'userJoined',
  USER_LEFT = 'userLeft',
  USER_ONLINE = 'userOnline',
  ROOM_DATA = 'roomData',
 ROOM_MEDIA = 'roomMedia',
 ROOM_MESSAGES = 'roomMessages',


  // Message events (listened from client)
  SEND_MESSAGE = 'sendMessage',
  MARK_MESSAGE_READ = 'markMessageRead',
  EDIT_MESSAGE = 'editMessage',
  DELETE_MESSAGE = 'deleteMessage',

  // Message events (emitted to client)
  NEW_MESSAGE = 'newMessage',
  MESSAGE_READ = 'messageRead',
  MESSAGE_EDITED = 'messageEdited',
  MESSAGE_DELETED = 'messageDeleted',

  // User status events (listened from client)
  SET_USER_STATUS = 'setUserStatus',
  TYPING = 'typing',
  STOP_TYPING = 'stopTyping',

  // User status events (emitted to client)
  USER_STATUS_CHANGED = 'userStatusChanged',
  USER_TYPING = 'userTyping',
  USER_STOPPED_TYPING = 'userStoppedTyping'
}
