import { IGlobalMiddlewareSocket } from "./types";
import { SocketLog } from "../utils";
import { SocketEvent } from "../types/server.events";

/**
 * Rate limiting configuration
 */
const RATE_LIMITS = {
    messages: {
      windowMs: 60 * 1000, // 1 minute
      max: 60, // 60 messages per minute
    },
    rooms: {
      windowMs: 60 * 1000, // 1 minute
      max: 10, // 10 room operations per minute
    },
  };
  
/**
 * Rate limiting middleware for socket events
 * @param socket Socket instance
 * @param eventType Event type (messages or rooms)
 * @returns Boolean indicating if the request is allowed
 */
export function checkRateLimit(socket: IGlobalMiddlewareSocket, eventType: 'messages' | 'rooms'): boolean {
    if (!socket.rateLimits) {
      return true;
    }
    
    const limit = socket.rateLimits[eventType];
    const config = RATE_LIMITS[eventType];
    
    // Reset counter if window has passed
    const now = new Date();
    if (now.getTime() - limit.lastReset.getTime() > config.windowMs) {
      limit.count = 0;
      limit.lastReset = now;
    }
    
    // Check if limit exceeded
    if (limit.count >= config.max) {
      SocketLog.warn(`Rate limit exceeded for ${eventType} by user ${socket.user.id} (${socket.user.type})`);
      socket.emit(SocketEvent.ERROR, { 
        message: `Rate limit exceeded for ${eventType}. Please try again later.`,
        success: false,
        statusCode: 429
      });
      return false;
    }
    
    // Increment counter
    limit.count++;
    return true;
  }
  