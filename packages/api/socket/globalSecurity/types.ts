import { Socket } from "socket.io";

export interface IGlobalMiddlewareSocket extends Socket {
    user: {
      id: string;
      type: 'user' | 'agent' | 'guest' | 'anonymous';
      role?: string;
      username?: string;
      companyId?: string;
      currentRoomId?: string | null;
      lastActivity?: Date;
      connectionTime?: Date;
      ipAddress?: string;
      userAgent?: string;
    };
    app?: {
      id: string;
      name: string;
      type?: string;
    };
    rateLimits?: {
      messages: {
        count: number;
        lastReset: Date;
      };
      rooms: {
        count: number;
        lastReset: Date;
      };
    };
  }