import { Namespace } from "socket.io/dist/namespace";
import { IGlobalMiddlewareSocket } from "../globalSecurity/types";
import { ApplicationType } from "@prisma/client";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { serviceLocator } from "../../utils/serviceLocator";
import { MessageHandler } from "../general/message.handler";

export const setupCustomerSupportApp = (customerSupport: Namespace) => {
   customerSupport.use(async (socket: IGlobalMiddlewareSocket, next) => {
    const appInstance = await AppInstanceMiddleware(
      ApplicationType.Customer_Support,
      null,
      socket.user.companyId,
    );
    if (!appInstance.success) {
      socket.emit('error', { message: 'Either Customer Support App has not been setup or you are not authorized to use it', success: false, statusCode: 400 });
      return;
    }
    socket.app = { id: appInstance.data.id, name: appInstance.data.name };
    next();
  });
  customerSupport.on('connection', (socket: IGlobalMiddlewareSocket) => {
      socket.on('support:sendMessage', async (data: any) => {
        const messageService = await serviceLocator.getMessageService();
        await MessageHandler.sendMessage(socket, customerSupport.server, data);
        if (data.text?.includes('help')) {
          const botMessage = await messageService.createMessage({
            text: 'How can I assist?',
            to: data.roomId,
            senderId: data.id
          });
          customerSupport.to(data.roomId).emit('support:newMessage', { data: botMessage });
        }
      });
    });
};
