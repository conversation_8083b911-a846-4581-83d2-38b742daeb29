import { Namespace } from "socket.io/dist/namespace";
import { IGlobalMiddlewareSocket } from "../globalSecurity/types";
import { prisma } from "../../utils/prismaClient";
import { ApplicationType } from "@prisma/client";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { messageService } from "../utils";

export const setupCustomerSupportApp = (customerSupport: Namespace) => {
   customerSupport.use(async (socket: IGlobalMiddlewareSocket, next) => {
    const appInstance = await AppInstanceMiddleware(
      ApplicationType.Customer_Support,
      null,
      socket.user.companyId,
    );
    if (!appInstance.success) {
      socket.emit('error', { message: 'Either Customer Support App has not been setup or you are not authorized to use it', success: false, statusCode: 400 });
      return;
    }
    socket.app = { id: appInstance.data.id, name: appInstance.data.name };
    next();
  });
  customerSupport.on('connection', (socket: IGlobalMiddlewareSocket) => {
      socket.on('support:sendMessage', async (data: any) => {
        // Import the MessageHandler
        const { MessageHandler } = require('../general/message.handler');
        // Use the MessageHandler to send the message
        await MessageHandler.sendMessage(socket, customerSupport.server, data);
        if (data.text?.includes('help')) {
          const botMessage = await messageService.createMessage({
            text: 'How can I assist?',
            to: data.roomId,
            senderId: data.id
          });
          customerSupport.to(data.roomId).emit('support:newMessage', { data: botMessage });
        }
      });
    });
};
