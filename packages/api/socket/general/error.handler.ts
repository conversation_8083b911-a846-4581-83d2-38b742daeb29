import { IGlobalMiddlewareSocket } from "../globalSecurity/types";
import { IResponse } from "../../utils";
import { SocketEvent } from "../types/server.events";
import { SocketLog as logger } from "../utils";


export class <PERSON>rror<PERSON>andler {
  public static handle<T>(
    socket: IGlobalMiddlewareSocket,
    error: any,
    context: string,
    defaultMessage: string
  ): IResponse<T> {
    const message = error.message || defaultMessage;
    const statusCode = error.statusCode || 500;

    logger.error(`${context}: ${message}`, error);

    socket.emit(SocketEvent.ERROR, {
      message,
      success: false,
      statusCode
    });

    return {
      success: false,
      message,
      statusCode,
      data: null
    };
  }

  public static validateInput<T>(
    socket: IGlobalMiddlewareSocket,
    context: string,
    ...inputs: any[]
  ): boolean {
    if (inputs.some(input => !input)) {
      const response = {
        success: false,
        message: "Invalid parameters",
        statusCode: 400
      };
      socket.emit(SocketEvent.ERROR, response);
      logger.warn(`${context}: Invalid parameters`);
      return false;
    }
    return true;
  }
}
