import { Server } from "socket.io";
import { prisma } from "../../utils/prismaClient";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { GeneralAppMiddleware } from "./generalApp.middleware";
import { <PERSON><PERSON>and<PERSON> } from "./room.handler";
import { MessageHandler } from "./message.handler";
import { StatusHandler } from "./status.handler";
import { serviceLocator } from "../../utils/serviceLocator";
import { SocketLog as logger } from "../utils";
import {  GuestStatus } from "./general.types";
import { SocketEvent } from "../types/server.events";
import { ICreateMessage, ICreateRoom } from "../../models";


export const setupGeneralApp = (generalServer: Server) => {
  generalServer.use(GeneralAppMiddleware);

  generalServer.on(SocketEvent.CONNECTION, async (socket: IGlobalMiddlewareSocket) => {
    logger.info(`New connection established with ID: ${socket.id} for user ${socket.user?.id} (${socket.user?.type})`);

    if (!socket.user) {
      logger.warn(`No user data found for socket ${socket.id}, disconnecting`);
      socket.disconnect();
      return;
    }

    try {
      const roomService = await serviceLocator.getRoomService();
      const guestRoomData = await roomService.getRoomsByGuestIdAndAppIdAndCompanyId(
        socket.user.id,
        socket.app.id,
        socket.user.companyId
      );

      await RoomHandler.JoinGuestToExistingRoomsOnLogin(socket.user.id, guestRoomData.data || [], socket, generalServer);

      socket.emit(SocketEvent.AUTHENTICATED, {
        user: {
          id: socket.user.id,
          type: socket.user.type,
          username: socket.user.username,
          currentRoomId: socket.user.currentRoomId
        },
        status: "Connected",
        applicationName: socket.app.name || "General App instance",
      });

      logger.info(`User ${socket.user.id} (${socket.user.type}) authenticated successfully`);


      /************** ROOM MANAGEMENT SECTION ***********************/

      socket.on(SocketEvent.GET_LIST_OF_GUEST_ROOMS, async () => {
        await RoomHandler.getListOfGuestRooms(socket.user.id, socket);
      });

      socket.on(SocketEvent.GET_ROOM_DATA_BY_ID, async (data: {roomId: string}) => {
        await RoomHandler.getRoomDataById(socket.user.id, data.roomId, socket);
      });

      socket.on(SocketEvent.JOIN_ROOM, async (data: {roomId: string}) => {
        // const currentRoomId = socket.user.currentRoomId;
        // if (currentRoomId && currentRoomId !== roomId) {
        //   await roomService.updateOnlineMembersCount(currentRoomId, -1);
        // }
        await RoomHandler.joinRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.SWITCH_ROOM, async (data: {roomId: string}) => {
        await RoomHandler.switchRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.REMOVE_USER_FROM_GROUP, async (data: {roomId: string, userId: string}) => {
        logger.info(`User ${socket.user.id} attempted to remove user ${data.userId} from room ${data.roomId}`);
        // Implement permission checks and removal logic as needed
      });

      socket.on(SocketEvent.LEAVE_ROOM, async (data: {roomId: string}) => {
        await RoomHandler.leaveRoom(socket.user.id, data.roomId, socket, generalServer);
      });

      socket.on(SocketEvent.CREATE_ROOM, async (data: Omit<ICreateRoom, 'applicationId, creatorId'>) => {
        await RoomHandler.createRoom(socket, generalServer, { ...data, applicationId: socket.app.id });
      });

      socket.on(SocketEvent.GET_ROOM_Media, async (data: { roomId: string}) => {
        const result = await RoomHandler.getRoomMedia (data.roomId, socket);
      });

      socket.on(SocketEvent.GET_ROOM_MESSAGES, async (data: { roomId: string, limit?: number, cursor?: string }) => {
        const result = await RoomHandler.getRoomMessages(data.roomId, socket, data.limit, data.cursor);
      });


      /************** MESSAGE MANAGEMENT SECTION ***********************/
      socket.on(SocketEvent.SEND_MESSAGE, async (data: Omit<ICreateMessage, 'senderId'>) => {
        await MessageHandler.sendMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.EDIT_MESSAGE, async (data: { messageId: string; text: string; roomId: string }) => {
        await MessageHandler.editMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.DELETE_MESSAGE, async (data: { messageId: string; roomId: string }) => {
        await MessageHandler.deleteMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.DELETE_MESSAGE, async (data: { messageId: string; roomId: string }) => {
        await MessageHandler.deleteMessage(socket, generalServer, data);
      });

      socket.on(SocketEvent.MARK_MESSAGE_READ, async (data: {messageIds: string[]}) => {
        await MessageHandler.markMessageRead(socket, generalServer, data);
      });

      socket.on(SocketEvent.SET_USER_STATUS, async (data: {status: GuestStatus}) => {
        await StatusHandler.setUserStatus(socket, generalServer, data.status);
      });

      socket.on(SocketEvent.TYPING, (data: { roomId: string }) => {
        StatusHandler.handleTyping(socket, generalServer, data.roomId);
      });

      socket.on(SocketEvent.STOP_TYPING, (data: { roomId: string }) => {
        StatusHandler.handleStopTyping(socket, generalServer, data.roomId);
      });

      socket.on(SocketEvent.DISCONNECT, async (reason) => {
        logger.info(`User ${socket.user.id} (${socket.user.type}) disconnected: ${reason}`);

        try {
          let lastSeenAt: Date | undefined;
          if (socket.user.type === 'guest') {
            const updateData = await prisma.guest.update({
              where: { id: socket.user.id },
              data: { lastSeenAt: new Date() }
            });
            lastSeenAt = updateData.lastSeenAt;
          } else if (socket.user.type === 'anonymous') {
            const updateData = await prisma.anonymous.update({
              where: { id: socket.user.id },
              data: { lastSeenAt: new Date() }
            });
            lastSeenAt = updateData.lastSeenAt;
          }

          socket.emit(SocketEvent.DISCONNECTED, {
            message: "Disconnected from server",
            lastSeenAt: lastSeenAt || new Date(),
            success: true,
            statusCode: 200
          });

          if (socket.user.currentRoomId) {
            const roomService = await serviceLocator.getRoomService();
            await roomService.updateOnlineMembersCount(socket.user.currentRoomId, -1);
            generalServer.to(socket.user.currentRoomId).emit(SocketEvent.USER_LEFT, {
              userId: socket.user.id,
              username: socket.user.username,
              userType: socket.user.type,
              roomId: socket.user.currentRoomId,
              reason: "disconnected",
              timestamp: new Date()
            });
          }
        } catch (error: any) {
          logger.error(`Error handling disconnect: ${error.message}`, error);
        }
      });
    } catch (error: any) {
      logger.error(`Error in connection handler: ${error.message}`, error);
      socket.emit(SocketEvent.ERROR, {
        message: "Internal server error",
        success: false,
        statusCode: 500
      });
    }
  });
};
