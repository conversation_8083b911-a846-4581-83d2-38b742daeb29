import { IGlobalMiddlewareSocket } from "../globalSecurity/types";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { <PERSON>rror<PERSON>andler } from "./error.handler";
import { ApplicationType } from "./general.types";
import { SocketLog as logger} from "../utils";
import { SocketEvent } from "../types/server.events";

export async function GeneralAppMiddleware(socket: IGlobalMiddlewareSocket, next: (err?: any) => void) {
  try {
    logger.debug(`Verifying General application for user ${socket.user?.id} (${socket.user?.type})`);

    const appInstance = await AppInstanceMiddleware(
      ApplicationType.General,
      null,
      socket.user.companyId
    );

    if (!appInstance.success || !appInstance.data) {
      logger.warn(`App instance check failed for user ${socket.user?.id}: ${appInstance.message}`);
      socket.emit(SocketEvent.ERROR, {
        message: appInstance.message || "Either General App has not been setup or you are not authorized to use it",
        success: false,
        statusCode: appInstance.statusCode || 400
      });
      return;
    }

    const { id, name } = appInstance.data;
    socket.app = { id, name, type: ApplicationType.General };

    logger.debug(`General application verification successful for user ${socket.user?.id}`);
    next();
  } catch (error: any) {
    ErrorHandler.handle(socket, error, "General App middleware", "Internal server error in General App middleware");
  }
}
