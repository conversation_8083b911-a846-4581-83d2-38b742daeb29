import { Server } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { SocketLog as logger } from "../utils";
import { SocketEvent } from "../types/server.events";

export class TypingManager {
  private typingUsers: Map<string, Map<string, number>> = new Map();
  private readonly TYPING_TIMEOUT = 5000; // 5 seconds

  constructor() {
    // Start cleanup interval
    setInterval(this.cleanup.bind(this), this.TYPING_TIMEOUT);
  }

  private cleanup(): void {
    const now = Date.now();
    this.typingUsers.forEach((users, roomId) => {
      users.forEach((timestamp, userId) => {
        if (now - timestamp > this.TYPING_TIMEOUT) {
          users.delete(userId);
        }
      });
      if (users.size === 0) {
        this.typingUsers.delete(roomId);
      }
    });
  }

  public handleTyping(socket: IGlobalMiddlewareSocket, io: Server, roomId: string): void {
    try {
      if (!roomId) {
        logger.warn(`Invalid roomId for typing event from user ${socket.user.id}`);
        return;
      }

      if (!this.typingUsers.has(roomId)) {
        this.typingUsers.set(roomId, new Map());
      }

      const roomTyping = this.typingUsers.get(roomId)!;
      roomTyping.set(socket.user.id, Date.now());

      socket.to(roomId).emit(SocketEvent.USER_TYPING, {
        userId: socket.user.id,
        username: socket.user.username,
        userType: socket.user.type,
        roomId,
        timestamp: new Date()
      });

      logger.debug(`User ${socket.user.id} started typing in room ${roomId}`);
    } catch (error: any) {
      logger.error(`Error handling typing: ${error.message}`, error);
    }
  }

  public handleStopTyping(socket: IGlobalMiddlewareSocket, io: Server, roomId: string): void {
    try {
      if (!roomId || !this.typingUsers.has(roomId)) {
        return;
      }

      const roomTyping = this.typingUsers.get(roomId)!;
      if (roomTyping.has(socket.user.id)) {
        roomTyping.delete(socket.user.id);

        socket.to(roomId).emit(SocketEvent.USER_STOPPED_TYPING, {
          userId: socket.user.id,
          username: socket.user.username,
          userType: socket.user.type,
          roomId,
          timestamp: new Date()
        });

        logger.debug(`User ${socket.user.id} stopped typing in room ${roomId}`);
      }
    } catch (error: any) {
      logger.error(`Error handling stop typing: ${error.message}`, error);
    }
  }
}

export const typingManager = new TypingManager();
