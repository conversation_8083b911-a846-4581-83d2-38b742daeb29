import { Server } from "socket.io";
import { prisma } from "../../utils/prismaClient";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { <PERSON>rror<PERSON>and<PERSON> } from "./error.handler";
import { typingManager } from "./typing.manager";
import { getServices, SocketLog as logger } from "../utils";
import { SocketEvent } from "../types/server.events";
import { GuestStatus } from "./general.types";

export class StatusHandler {
  public static async setUserStatus(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    status: GuestStatus
  ): Promise<void> {
    try {
      const { guestService }  = await getServices();
      if (socket.user.type === 'guest') {
        await guestService.updateGuest(socket.user.id, {
          data: {
            awayModeEnabled: status === GuestStatus.Away,
            ...(status === GuestStatus.Online ? { lastSeenAt: new Date() } : {})
          }
        });
      }

      if (socket.user.currentRoomId) {
        io.to(socket.user.currentRoomId).emit(SocketEvent.USER_STATUS_CHANGED, {
          userId: socket.user.id,
          username: socket.user.username,
          status,
          timestamp: new Date()
        });
      }

      logger.debug(`User ${socket.user.id} status updated to ${status}`);
    } catch (error: any) {
      ErrorHandler.handle(socket, error, "Set user status", "Failed to update user status");
    }
  }

  public static handleTyping(socket: IGlobalMiddlewareSocket, io: Server, roomId: string): void {
    typingManager.handleTyping(socket, io, roomId);
  }

  public static handleStopTyping(socket: IGlobalMiddlewareSocket, io: Server, roomId: string): void {
    typingManager.handleStopTyping(socket, io, roomId);
  }
}
