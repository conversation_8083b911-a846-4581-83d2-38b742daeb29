import { Server } from "socket.io";
import { IGlobalMiddlewareSocket, checkRateLimit } from "../globalSecurity";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./error.handler";
import { IResponse, RoomType, GuestStatus } from "./general.types";
import { roomService,  SocketLog as logger, guestService } from "../utils";
import { SocketEvent } from "../types/server.events";
import { ICreateRoom, IRoom, IRoomMedia, IMessage } from "../../models";



export class RoomHandler {
  public static async JoinGuestToExistingRoomsOnLogin(
    id: string,
    listOfRooms: IRoom[],
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<IRoom[] | null>> {
    try {

      const roomIds = listOfRooms?.map(room => room.id) || [];
      socket.join(roomIds);
      
      socket.emit(SocketEvent.LIST_OF_GUEST_ROOMS, listOfRooms);

      socket.user.lastActivity = new Date();
      io.to(roomIds).emit(SocketEvent.USER_ONLINE , {
        userId: id,
        username: socket.user.username,
        userType: socket.user.type,
        roomId: roomIds,
        timestamp: new Date()
      });

      await Promise.allSettled(roomIds.map(roomId => roomService.updateOnlineMembersCount(roomId, 1)));
      await guestService.updateGuest(id, { currentRoomId: roomIds[0], status: GuestStatus.Online });

      return { success: true, message: "Guest joined rooms successfully", statusCode: 200, data: listOfRooms };
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Join room", "Failed to join room");
    }
  }

  public static async getListOfGuestRooms(
    id: string,
    socket: IGlobalMiddlewareSocket,
  ): Promise<IResponse<IRoom[] | null>> {
    try {
      const guestRoomData = await roomService.getRoomsByGuestIdAndAppIdAndCompanyId(
        id,
        socket.app.id,
        socket.user.companyId
      );
  
      socket.emit(SocketEvent.LIST_OF_GUEST_ROOMS, guestRoomData?.data || []);
  
      return guestRoomData;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Get list of guest rooms", "Failed to get list of guest rooms");
    }
  }

  public static async getRoomDataById(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
  ): Promise<IResponse<IRoom | null>> {
    try {
      const isMember = await roomService.isGuestMemberOfRoom(id, roomId);
      if (!isMember) {
        socket.emit(SocketEvent.ERROR, { 
          success: false, 
          message: "User is not a member of this room", 
          statusCode: 403 
        });
        return { 
          success: false, 
          message: "User is not a member of this room", 
          statusCode: 403, 
          data: null 
        };
      }
      const roomData = await roomService.getRoomFullDetails(roomId);
      if (!roomData.success) {
        socket.emit(SocketEvent.ERROR, { ...roomData });
        return roomData;
      }
      socket.emit(SocketEvent.ROOM_DATA, roomData.data);
      return roomData;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Get room data by id", "Failed to get room data");
    }
  }

  public static async switchRoom(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<IRoom | null>> {
    try {
      // Check if the user is a member of the room
      const isMember = await roomService.isGuestMemberOfRoom(id, roomId);
      
      if (!isMember) {
        socket.emit(SocketEvent.ERROR, { 
          success: false, 
          message: "User is not a member of this room", 
          statusCode: 403 
        });
        return { 
          success: false, 
          message: "User is not a member of this room", 
          statusCode: 403, 
          data: null 
        };
      }
      
      await socket.join(roomId);
      socket.user.currentRoomId = roomId;
      socket.user.lastActivity = new Date();
      
      await guestService.updateGuest(id, { currentRoomId: roomId });

      socket.emit(SocketEvent.ROOM_SWITCHED, { roomId });
      
      logger.info(`User ${id} (${socket.user.type}) switched to room ${roomId}`);
      
      return { success: true, message: "Room switched successfully", statusCode: 200, data: {id: roomId} as any };
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Switch room", "Failed to switch room");
    }
  }

  public static async joinRoom(
    id: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<number>> {
    try {
      if (!checkRateLimit(socket, 'rooms')) {
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Join room", "Rate limit exceeded");
      }

      let roomData: IResponse<number>;
      if (socket.user.type === 'guest') {
        roomData = await roomService.addGuestToRoom(id, roomId);
      } else {
        roomData = { success: false, message: "User type not supported", statusCode: 400, data: null };
      }

      if (!roomData.success) {
        socket.emit(SocketEvent.ERROR, { ...roomData });
        return roomData;
      }

      await socket.join(roomId);
      socket.user.currentRoomId = roomId;
      socket.user.lastActivity = new Date();
      await roomService.updateOnlineMembersCount(roomId, 1);

      io.to(roomId).emit(SocketEvent.USER_JOINED, {
        userId: id,
        username: socket.user.username,
        roomId,
        memberCount: roomData.data,
        timestamp: new Date()
      });

      socket.emit(SocketEvent.ROOM_JOINED, { room: roomData.data });
      logger.info(`User ${id} (${socket.user.type}) joined room ${roomId}`);

      return roomData;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Join room", "Failed to join room");
    }
  }

  public static async leaveRoom(
    userId: string,
    roomId: string,
    socket: IGlobalMiddlewareSocket,
    io: Server
  ): Promise<IResponse<number>> {
    try {
      if (!checkRateLimit(socket, 'rooms')) {
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Leave room", "Rate limit exceeded");
      }

      let leaveRoomResponse: IResponse<number>;
      if (socket.user.type === 'guest') {
        leaveRoomResponse = await roomService.removeGuestFromRoom(userId, roomId);
      } else {
        leaveRoomResponse = { success: false, message: "User type not supported", statusCode: 400, data: null };
      }

      if (!leaveRoomResponse.success) {
        socket.emit(SocketEvent.ERROR, { ...leaveRoomResponse });
        return leaveRoomResponse;
      }

      await socket.leave(roomId);
      await roomService.updateOnlineMembersCount(roomId, -1);

      if (socket.user.currentRoomId === roomId) {
        socket.user.currentRoomId = null;
      }

      socket.user.lastActivity = new Date();

      io.to(roomId).emit(SocketEvent.USER_LEFT, {
        userId,
        userType: socket.user.type,
        username: socket.user.username,
        roomId,
        memberCount: leaveRoomResponse.data,
        timestamp: new Date()
      });

      socket.emit(SocketEvent.ROOM_LEFT, { roomId });
      logger.info(`User ${userId} (${socket.user.type}) left room ${roomId}`);

      return leaveRoomResponse;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Leave room", "Failed to leave room");
    }
  }

  public static async createRoom(
    socket: IGlobalMiddlewareSocket,
    io: Server,
    data: ICreateRoom,
  ): Promise<IResponse<IRoom | null>> {
    try {
      if (!checkRateLimit(socket, 'rooms')) {
        return ErrorHandler.handle(socket, new Error("Rate limit exceeded"), "Create room", "Rate limit exceeded");
      }


      if (data.type === RoomType.dm && data.guestIds.length !== 1) {
        return ErrorHandler.handle(socket, new Error("DM chats require exactly one recipient"), "Create room", "DM chats require exactly one recipient");
      }

      // so, group is the default when created through socket 
      if(!data.type || data.type === RoomType.group) {
        data.creatorId = socket.user.id;
        data.type = RoomType.dm; 
      }
      const newRoom = await roomService.createRoom(data);

      if (!newRoom.success) {
        socket.emit(SocketEvent.ERROR, { ...newRoom });
        return newRoom;
      }

      await this.joinRoom(socket.user.id, newRoom.data!.id, socket, io);
      socket.user.lastActivity = new Date();

      socket.emit(SocketEvent.ROOM_CREATED, newRoom.data);


      logger.info(`Room ${newRoom.data!.id} created by user ${socket.user.id} (${socket.user.type})`);

      return newRoom;
    } catch (error: any) {
      return ErrorHandler.handle(socket, error, "Create room", "Failed to create room");
    }
  }

  public static async getRoomMedia(roomId: string, socket: IGlobalMiddlewareSocket): Promise<IResponse<IRoomMedia[] | null>>{
    try {
        const isMember = await roomService.isGuestMemberOfRoom(socket.user.id, roomId);
        const errorData: IResponse<null> =  {  success: false, message: "User is not a member of this room", statusCode: 403, data: null} 
        
        if (!isMember.success) {
          socket.emit(SocketEvent.ERROR, errorData);
          return errorData;
        }
        
        const roomMedia = await roomService.getRoomMedia(roomId);
        if (!roomMedia.success) {
          socket.emit(SocketEvent.ERROR, roomMedia);
          return roomMedia;
        }
        socket.emit(SocketEvent.ROOM_MEDIA, roomMedia.data);
        return roomMedia;
    } catch (error: any) {
        logger.error(`Error getting room media: ${error.message}`, error);
        return ErrorHandler.handle(socket, error, "Get room data by id", "Failed to get room data");
    } 
  }

  public static async getRoomMessages(roomId: string, socket: IGlobalMiddlewareSocket, limit?: number, cursor?: string) {
    try {
      const messages = await roomService.getRoomMessages(roomId, limit, cursor);
      if (!messages.success) {
        socket.emit(SocketEvent.ERROR, messages);
        return messages;
      }
      socket.emit(SocketEvent.ROOM_MESSAGES, messages.data);
      return messages;
    } catch (error: any) {
      logger.error(`Error getting room messages: ${error.message}`, error);
      return ErrorHandler.handle(socket, error, "Get room messages", "Failed to get room messages");
    }
  }
}
