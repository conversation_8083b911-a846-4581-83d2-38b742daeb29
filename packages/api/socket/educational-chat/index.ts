import { Namespace } from "socket.io";
import { IGlobalMiddlewareSocket } from "../globalSecurity";
import { MessageHandler } from "../general/message.handler";
import { prisma } from "../../utils/prismaClient";
import { AppInstanceMiddleware } from "../utils/verifyAppInstance";
import { ApplicationType } from "@prisma/client";
import { RoomHandler } from "../general/room.handler";

export const setupEducationalApp = (education: Namespace) => {
    education.use(async (socket: IGlobalMiddlewareSocket, next) => {
      const appInstance = await AppInstanceMiddleware(
        ApplicationType.Education,
        null,
        socket.user.companyId,
      );
      if (!appInstance.success) {
        socket.emit('error', { message: 'Either Educational App has not been setup or you are not authorized to use it', success: false, statusCode: 400 });
        return;
      }
      socket.app = { id: appInstance.data.id, name: appInstance.data.name };
      next();
    });
    education.on('connection', (socket: IGlobalMiddlewareSocket) => {
      socket.on('createRoom', async (data) => {
        const roomData = { ...data, expiresAt: new Date(data.expiresAt) };
        await RoomHandler.createRoom(socket, education.server, roomData);
      });
      socket.on('sendMessage', async (data) => {
        if (data.isPinned || data.isAnswered) {
          const message = await prisma.message.create({
            data: {
              ...data,
              isPinned: data.isPinned || false,
              isAnswered: data.isAnswered || false,
              from: { id: socket.user.id, type: socket.user.type },
              analyticId: (await prisma.analytic.create({ data: {} })).id,
            },
          });
          education.to(data.roomId).emit('education:newMessage', { data: message });
        } else {
          await MessageHandler.sendMessage(socket, education.server, data);
        }
      });
    });
};