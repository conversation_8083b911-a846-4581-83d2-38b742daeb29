import { NextFunction, Request, Response } from 'express';
import { CompanyController } from '../../v1/controllers';
import { CompanyService } from '../../v1/services';
import { ControllerHelper, IResponse, UserRequestPayload } from '../../utils';
import { Company, ICompany, ICreateCompanyData, AccessKey, CloudStorageProvider } from '../../models';
import { ApiError } from '../../utils';
import * as ACCOUNT from '../../__mocks__/accountMock';

jest.mock('../../v1/services/company.service');
jest.mock('../../utils/controllerHelpers');



describe('CompanyController', () => {
  let companyController: CompanyController;
  let mockCompanyService: jest.Mocked<CompanyService>;
  let mockControllerHelper: jest.Mocked<ControllerHelper>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock<NextFunction>;

  beforeEach(() => {
    jest.clearAllMocks();

    const controllerHelper = new ControllerHelper();
    const companyService = new CompanyService(new Company(new AccessKey()), new CloudStorageProvider());

    mockCompanyService = {
      Create: jest.fn(companyService.Create),
      Update: jest.fn(companyService.Update),
    } as any;

    mockControllerHelper = {
      validateRequestBody: jest.fn(controllerHelper.validateRequestBody),
      handleServiceResult: jest.fn(controllerHelper.handleServiceResult),
      validateUser: jest.fn(controllerHelper.validateUser),
    } as any;

    companyController = new CompanyController(
      mockCompanyService as CompanyService,
      mockControllerHelper as ControllerHelper
    );

    mockRequest = {
      params: {},
      query: {},
      user: {},
      body: {},
    };

    mockResponse = {
      statusCode: 200,
      json: jest.fn(),
    };

    mockNext = jest.fn();
  });

  describe('.Create', () => {
    const data = { ...ACCOUNT.MockCreateAccountData.companies[0]};
    let mockRequestDotUserObject: UserRequestPayload = { id: ACCOUNT.MockCreateAccountData.ownerId, email: '<EMAIL>' };
    const mockCreateCompanyData: ICreateCompanyData =  {
        name: data.name,
        website: data.website,
        industry: data.industry,
        accountId: data.accountId
    };
    mockRequest = { ...mockRequest, body: mockCreateCompanyData, user: mockRequestDotUserObject };
    const resultData: ICompany  = {
        ...mockCreateCompanyData,
        id: data.id,
        applicationsCount: 0,
        usersCount: 1,
        guestsCount: 0,
        monthlySpend: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        location: undefined,
        tagIds: [],
        userIds: [mockRequestDotUserObject.id],
        guestIds: [],
        anonymousIds: []
    };

    const resultFromCompanyService: IResponse<ICompany> = { success: true, data: resultData, message: 'Company created successfully', statusCode: 201 };

    it('should create company successfully', async () => {
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockCompanyService.Create.mockResolvedValue(resultFromCompanyService);

      await companyController.Create(
        mockRequest as Request,
        mockResponse as Response,
        mockNext as NextFunction
      );
      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Create company controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Create company controller');
      expect(mockCompanyService.Create).toHaveBeenCalledWith(
        mockRequest.body,
        mockRequestDotUserObject
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromCompanyService, mockResponse, 'Create company controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle errors during company creation e.g user is not an account owner', async () => {
      mockRequestDotUserObject = { id: '65cfd5e8a8f3b20d4c9b1230', email: '<EMAIL>' };
      mockRequest = { ...mockRequest, user: mockRequestDotUserObject };
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockCompanyService.Create.mockResolvedValue({
        success: false,
        data: null,
        message: 'Access denied: Only an account owner can create a company under this account',
        statusCode: 400,
      });

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await companyController.Create(
        mockRequest as Request,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Create company controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Create company controller');
      expect(mockCompanyService.Create).toHaveBeenCalledWith(
        mockRequest.body,
        mockRequestDotUserObject
      );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          data: null,
          message: 'Access denied: Only an account owner can create a company under this account',
          statusCode: 400,
        }),
        mockResponse,
        'Create company controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(
        new ApiError('Access denied: Only an account owner can create a company under this account', 400, 'Create company controller')
    );
    });
  });

  describe('.Update', () => {
    const data = { ...ACCOUNT.MockCreateAccountData.companies[0]};
    let mockRequestDotUserObject: UserRequestPayload = { id: ACCOUNT.MockCreateAccountData.ownerId, email: '<EMAIL>' };
    const mockUpdateCompanyData: Partial<ICompany> =  {
        name: 'Updated Company Name',
    };



    const resultData: ICompany  = {
        ...data,
        ...mockUpdateCompanyData,
        applicationsCount: 0,
        usersCount: 1,
        guestsCount: 0,
        monthlySpend: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        location: undefined,
        tagIds: [],
        userIds: [mockRequestDotUserObject.id],
        guestIds: [],
        anonymousIds: []
    };

    const resultFromCompanyService: IResponse<ICompany> = { success: true, data: resultData, message: 'Company updated successfully', statusCode: 201 };

    it('should update company successfully', async () => {
     mockRequest = {
            ...mockRequest,
            params: { companyId: data.id },
            body: { ...mockUpdateCompanyData, roleName: 'Admin'},
            user: mockRequestDotUserObject
      };
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockCompanyService.Update.mockResolvedValue(resultFromCompanyService);

      await companyController.Update(
        mockRequest as Request,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Update company controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Update company controller');
      expect(mockCompanyService.Update).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockUpdateCompanyData, id: mockRequest.params.companyId }),
        mockRequestDotUserObject,
        'Admin'
    );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(resultFromCompanyService, mockResponse, 'Update company controller');
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle missing role name during company update', async () => {
      mockRequest = { ...mockRequest, body: { ...mockUpdateCompanyData }, params: { companyId: data.id } };
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await companyController.Update(
        mockRequest as Request,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockControllerHelper.validateUser).toHaveBeenCalledWith(mockRequest, 'Update company controller');
      expect(mockControllerHelper.validateRequestBody).toHaveBeenCalledWith(mockRequest, 'Update company controller')
      expect(mockCompanyService.Update).not.toHaveBeenCalled();
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        {success: false, statusCode: 400, data: null, message: 'Role name is required'},
        mockResponse,
        'Update company controller'
      );
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(
        new ApiError('Role name is required', 400, 'Update company controller')
    );
    });

    it('should handle validation errors during company update', async () => {
      mockRequest = {
        ...mockRequest,
        body: {...mockUpdateCompanyData, roleName: 'Admin'},
        params: { companyId: 'invalid company Id' },
        user: mockRequestDotUserObject
    };
      mockControllerHelper.validateUser.mockReturnValue(mockRequestDotUserObject);
      mockControllerHelper.validateRequestBody.mockReturnValue(mockRequest.body);
      mockCompanyService.Update.mockResolvedValue({
        success: false,
        data: null,
        message: 'Validation failed, Invalid Id',
        statusCode: 400,
      });

      mockControllerHelper.handleServiceResult.mockImplementation((result, response, controllerName) => {
        if (!result.success) {
          throw new ApiError(result.message, result.statusCode, controllerName);
        }
        response.status(result.statusCode).json(result);
      });

      await companyController.Update(
        mockRequest as Request,
        mockResponse as Response,
        mockNext as NextFunction
      );

      expect(mockCompanyService.Update).toHaveBeenCalledWith(
        expect.objectContaining({ ...mockUpdateCompanyData, id: mockRequest.params.companyId }),
        mockRequestDotUserObject,
        'Admin'
     );
      expect(mockControllerHelper.handleServiceResult).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          data: null,
          message: 'Validation failed, Invalid Id',
          statusCode: 400,
        }),
        mockResponse,
        'Update company controller'
      );

      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ApiError);
      expect(mockNext).toHaveBeenCalledWith(new ApiError('Validation failed, Invalid Id', 400, 'Update company controller'));
    });
  });
});