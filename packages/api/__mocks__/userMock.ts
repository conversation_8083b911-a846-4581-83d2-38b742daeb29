import { IAvatar, ILocation, IOwnerRole, ICreateUser, IUpdateUser, IUserGeneralData, IUserLoginData, IDelete, IUserDataWithPassword } from "../models";
import { $Enums } from "@prisma/client";

// Dummy Avatar
const MockAvatar: IAvatar = {
  filename: "profile.jpg",
  fileUrl: "https://example.com/profile.jpg",
};


const MockFiles = [{ originalname: 'profile.jpg', location: 'https://example.com/profile.jpg', } as any];

const MockHeader = {
  'x-forwarded-for': '***********',
  'user-agent':
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'accept-language': 'en-US,en;q=0.9',
};

const MockIp =  '***********';
const MockSocket =  { remoteAddress: '***********'};

// Dummy Location
const MockLocation: ILocation = {
  country: "USA",
  region: "California",
  city: "Los Angeles",
  longitude: "-118.2437",
  latitude: "34.0522",
};

const MockAnalytic = {
  language: 'en-US',
  browser: 'Chrome',
  browserVersion: '91.0.4472.124',
  os: 'Windows',
  ipAddress: '***********'
};

const MockRole: IOwnerRole = $Enums.OwnerRole.Owner;


// Dummy Update User Request
const MockUpdateUser: IUpdateUser = {
  id: "65cfd5e8a8f3b20d4c9b1234",
  displayName: "John D.",
  username: "johndoe123",
  email: "<EMAIL>",
  awayModeEnabled: true,
  avatar: MockAvatar,
  location: MockLocation,
  role: MockRole,
  companyIds: ["55d8b2f7e3a4b6c91f4e0a15"],
  roomIds: ["75d8b2f7e3a4b6c91f4e0a15"],
  analyticId: "85d8b2f7e3a4b6c91f4e0a15",
  createdAt: new Date(),
};

// Dummy General User Data
const MockUserGeneralData: IUserGeneralData = {
  id: "65cfd5e8a8f3b20d4c9b1234",
  displayName: "John Doe",
  name: "John",
  username: "johndoe",
  email: "<EMAIL>",
  awayModeEnabled: false,
  avatar: MockAvatar,
  location: MockLocation,
  role: MockRole,
  accountId: "45d8b2f7e3a4b6c91f4e0a12",
  companyIds: ["55d8b2f7e3a4b6c91f4e0a17", "55d8b2f7e3a4b6c91f4e0a18"],
  roomIds: ["75d8b2f7e3a4b6c91f4e0a17", "75d8b2f7e3a4b6c91f4e0a18"],
  analyticId: "85d8b2f7e3a4b6c91f4e0a17",
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Dummy User Login Data
const MockUserLoginData: IUserLoginData = {
  email: "<EMAIL>",
  password: "SecurePass123!",
};

// Dummy User Data with Password
const MockUserDataWithPassword: IUserDataWithPassword = {
  ...MockUserGeneralData,
  password: "SecurePass123!",
};

// Dummy Delete Response
const MockDeleteResponse: IDelete = {
  data: null,
};


const MockCreateUserData: Partial<ICreateUser> = {
  name: 'John',
  username: 'johndoe',
  email: '<EMAIL>',
  password: 'SecurePass123!',
  displayName: 'John Doe'
};


export {
  MockAvatar,
  MockLocation,
  MockRole,
  MockCreateUserData,
  MockAnalytic,
  MockUpdateUser,
  MockUserGeneralData,
  MockUserLoginData,
  MockUserDataWithPassword,
  MockDeleteResponse,
  MockHeader,
  MockIp,
  MockSocket,
  MockFiles
};
