import { IAccount, ICreateAccountData, IUpdateAccountData, IGenerateAccountData } from "../models";

const MockGeneralAccountDataEmpty: IGenerateAccountData = {
  id: "45d8b2f7e3a4b6c91f4e0a11",
  name: "My Account",
  companiesCount: 0,
  applicationsCount: 0,
  usersCount: 0,
  guestsCount: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  ownerId: "",
  companies: [],
  accountSubscriptions: [],
  accessKeys: [],
}

const MockGeneralAccountData: IGenerateAccountData = {
  id: "45d8b2f7e3a4b6c91f4e0a14",
  name: "My Account",
  companiesCount: 2,
  applicationsCount: 5,
  usersCount: 10,
  guestsCount: 3,
  createdAt: new Date(),
  updatedAt: new Date(),
  ownerId: "65cfd5e8a8f3b20d4c9b1234",
  companies: [
    {
      id: "55d8b2f7e3a4b6c91f4e0a14",
      name: "TechCorp",
      website: "https://techcorp.com",
      industry: "Software",
      applicationsCount: 3,
      usersCount: 7,
      guestsCount: 2,
      monthlySpend: 1000,
      createdAt: new Date(),
      updatedAt: new Date(),
      accountId: "45d8b2f7e3a4b6c91f4e0a14",
    },
    {
      id: "55d8b2f7e3a4b6c91f4e0a45",
      name: "FinanceInc",
      website: "https://financeinc.com",
      industry: "Finance",
      applicationsCount: 2,
      usersCount: 3,
      guestsCount: 1,
      monthlySpend: 500,
      createdAt: new Date(),
      updatedAt: new Date(),
      accountId: "45d8b2f7e3a4b6c91f4e0a14",
    },
  ],
  accountSubscriptions: [
    {
      id: "35d8b2f7e3a4b6c91f4e0a14",
      createdAt: new Date(),
      updatedAt: new Date(),
      endedAt: null,
      active: true,
      accountId: "45d8b2f7e3a4b6c91f4e0a14",
      subscriptionId: "sub_plan_premium",
    },
  ],
  accessKeys: [
    {
      id: "25d8b2f7e3a4b6c91f4e0a14",
      name: "Primary API Key",
      key: "sk_live_abcdefg",
      status: "Active",
      expiresAt: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      lastRequestAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
      creatorId: "65cfd5e8a8f3b20d4c9b1234",
      companyId: "55d8b2f7e3a4b6c91f4e0a14",
    },
  ],
};

const MockCreateAccountData: ICreateAccountData = {
  name: "My Account",
  ownerId: "65cfd5e8a8f3b20d4c9b1233",
  companiesCount: 1,
  applicationsCount: 0,
  usersCount: 1,
  guestsCount: 0,
  companies: [
    {
      id: "55d8b2f7e3a4b6c91f4e0a13",
      name: "Taxdone",
      website: "https://taxdone.ch",
      industry: "Finance",
      applicationsCount: 0,
      usersCount: 1,
      guestsCount: 0,
      monthlySpend: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      accountId: "45d8b2f7e3a4b6c91f4e0a13",
    },
  ],
  accountSubscriptions: [],
  accessKeys: [],
};

const MockUpdateAccountData: IUpdateAccountData = {
  id: "45d8b2f7e3a4b6c91f4e0a12",
  name: "My Account Updated",
  ownerId: "user_001",
  usersCount: 15,
  guestsCount: 5,
  companies: [
    {
      id: "55d8b2f7e3a4b6c91f4e0a12",
      name: "TechCorp Updated",
      website: "https://techcorp-updated.com",
      industry: "Software",
      applicationsCount: 4,
      usersCount: 10,
      guestsCount: 2,
      monthlySpend: 1200,
      createdAt: new Date(),
      updatedAt: new Date(),
      accountId: "45d8b2f7e3a4b6c91f4e0a12",
    },
  ],
};

export { 
  MockGeneralAccountData,
  MockCreateAccountData,
  MockUpdateAccountData,
  MockGeneralAccountDataEmpty,
 };
