import { NextFunction, Request, Response } from 'express'
import { GuestService, RoomService } from '../services'
import { AuthService, ControllerHelper } from '../../utils'
import { ICreateRoom } from '../../models';


export class GuestController {
    constructor(private readonly gtService: GuestService, private readonly rmService: RoomService, private readonly controllerHelper: ControllerHelper) {
    }

    async upsertGuest(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const body = this.controllerHelper.validateRequestBody<{metaData: any, apiKey?: string, apiKeySecret?: string}>(request, 'UpsertGuest controller');
            const apiKey = request.headers['x-api-key']  || request.headers['X-API-Key'] || request.body?.apiKey;
            const apiKeySecret = request.headers['x-api-secret']  || request.headers['X-API-Secret'] || request.body?.apiKeySecret;
            const appInstance = await AuthService.getAppTypeFromApiKeySecret(apiKey, apiKeySecret, 'General')
            if(!appInstance) {
                return this.controllerHelper.handleServiceResult({ success: false, statusCode: 400, data: null, message: 'Invalid API key or secret' }, response, 'UpsertGuest controller');
            }
            const metaData = {...body?.metaData, applicationId: appInstance.id};
            const { externalId } = metaData;
            const result = await this.gtService.upsert(externalId, appInstance.companyId, metaData);
            this.controllerHelper.handleServiceResult(result, response, 'UpsertGuest controller');
        } catch (error: any) {
            next(error);
        }
    }

    async loginGuest(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const body = this.controllerHelper.validateRequestBody<{id: string, apiKey?: string}>(request, 'LoginGuest controller');
            const apiKey = request.headers['x-api-key']  || request.headers['X-API-Key'] || request.body?.apiKey;
            const result = await this.gtService.loginGuest(body.id, apiKey as string);
            if(result.success && request.headers['RestClient'] && request.headers['RestClient'] === 'true') {
                return this.controllerHelper.handleServiceResult(result, response, 'LoginGuest controller');
            }
            this.controllerHelper.handleServiceResultWithCookie(result, response, true, 'LoginGuest controller');
        } catch (error: any) {
            next(error);
        }
    }

    async logoutGuest(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            this.controllerHelper.clearCookie(response, true);
            this.controllerHelper.handleServiceResult({ success: true, statusCode: 200, data: null, message: 'Logged out successfully' }, response, 'Logout guest controller');
        } catch (error: any) {
            next(error);
        }
    };

    async CreateRoom(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'CreateRoom controller');
            const body = this.controllerHelper.validateRequestBody<ICreateRoom>(request, 'CreateRoom controller');
            const result =  await this.rmService.createRoom({...body, guestIds: body.guestIds ? [...body.guestIds, guest.id] : [guest.id]});
            this.controllerHelper.handleServiceResult(result, response, "CreateRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async UpdateRoom(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'UpdateRoom controller');
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody(request, 'UpdateRoom controller');
            const result =  await this.rmService.updateRoom(roomId, body);
            this.controllerHelper.handleServiceResult(result, response, "UpdateRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRooms(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'GetGuestRooms controller');
            const result =  await this.rmService.getRoomsByGuestId(guest.id);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestRooms controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRoomsByApplicationId(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'GetGuestRoomsByApplicationId controller');
            const applicationId = request.params?.applicationId || request.body?.applicationId;
            const result =  await this.rmService.getRoomsByGuestIdAndApplicationId(guest.id, applicationId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestRoomsByApplicationId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestRoomsByApplicationIdAndCompanyId(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'getGuestRoomsByApplicationIdAndCompanyId controller');
            const applicationId = request.params?.applicationId || request.body?.applicationId;
            const result =  await this.rmService.getRoomsByGuestIdAndAppIdAndCompanyId(guest.id, applicationId, guest.companyId);
            this.controllerHelper.handleServiceResult(result, response, "getGuestRoomsByApplicationIdAndCompanyId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async isGuestMemberOfRoom(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'IsGuestMemberOfRoom controller'); 
            const roomId = request.params?.roomId || request.body?.roomId;
            const result =  await this.rmService.isGuestMemberOfRoom(guest.id, roomId);
            this.controllerHelper.handleServiceResult(result, response, "IsGuestMemberOfRoom controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestByExternalIdAndCompanyId(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'GetGuestByExternalIdAndCompanyId controller');
            const externalId = request.params?.externalId || request.body?.externalId;
            const result =  await this.gtService.getGuestByExternalIdAndCompanyId(externalId, guest.companyId);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestByExternalIdAndCompanyId controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async getGuestById(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const guest = this.controllerHelper.validateUser(request, 'GetGuestById controller');
            const result =  await this.gtService.getGuestById(guest.id);
            this.controllerHelper.handleServiceResult(result, response, "GetGuestById controller"); 
        } catch(error: any){
            next(error);
        }
    }
}
