import { NextFunction, Request, Response } from 'express'
import { UserService } from '../services'
import { UAParser } from 'ua-parser-js';
import { ControllerHelper } from '../../utils';
import { ICreateUser, IUserLoginData, IUpdateUser, IDelete } from '../../models';

interface IMulterS3File extends Express.Multer.File {
    bucket: string;
    key: string;
    location: string;
    etag: string;
  }
export class UserController  {

    constructor(
        private readonly userService: UserService,
        private readonly controllerHelper: ControllerHelper
    ) {}

    private extractAnalyticAndAvatarData(req: Request) {
        const ipAddress = req.ip || req.headers['x-forwarded-for'] || req.socket.remoteAddress || '';
        const userAgent = req.headers['user-agent'] || '';
        const parser = new UAParser(userAgent);
        const userAgentData = parser.getResult();
        const acceptLanguage = req.headers['accept-language'] || '';
        const language = acceptLanguage.split(',')[0] || undefined;

        let avatar = { filename: '', fileUrl: '' };

        const files = req.files ? (req.files as IMulterS3File[]) : [];
        if (files.length > 0) {
            avatar = { filename: files[0].originalname, fileUrl: files[0].location };
        }

        const analytic = {
            language,
            browser: userAgentData.browser.name,
            browserVersion: userAgentData.browser.version,
            os: userAgentData.os.name,
            ipAddress: Array.isArray(ipAddress) ? ipAddress[0] : ipAddress,
        };

        return { analytic, avatar };
    }

    async Create(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const body = this.controllerHelper.validateRequestBody<ICreateUser>(request, 'Create user controller');
            const {analytic, avatar }  = this.extractAnalyticAndAvatarData(request); 
            const data = {...body, avatar};
            const result = await this.userService.Create(data, analytic);
            this.controllerHelper.handleServiceResult(result, response, 'Create user controller');
        } catch (error: any) {
             next(error);
        }
    }


    async  Login(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            this.controllerHelper.validateRequestBody<IUserLoginData>(request, 'Login user controller');
            const result = await this.userService.Login(request.body);
            this.controllerHelper.handleServiceResultWithCookie(result, response, false, "Login user controller");
        } catch (error: any) {
            next(error);
        }
    }

    async Logout(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            this.controllerHelper.validateUser(request, 'Logout user controller');
            this.controllerHelper.clearCookie(response, false);
            this.controllerHelper.handleServiceResult({ success: true, statusCode: 200, data: null, message: 'Logged out successfully' }, response, 'Logout user controller');
        } catch (error: any) {
            next(error);
        }
    }

    async Update(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            // Early termination if password is included in the request body
            if(request?.body?.password) {
                this.controllerHelper.handleServiceResult<IDelete>({
                    success: false,
                    statusCode: 400,
                    data: null,
                    message: 'Password update is not allowed on this endpoint, please use the reset password endpoint'
                }, 
                response, 
                'Update user controller');
                return;
            }
            const body = this.controllerHelper.validateRequestBody<IUpdateUser>(request, 'Update user controller');
            const user = this.controllerHelper.validateUser(request, 'Update user controller');
            const { avatar }  = this.extractAnalyticAndAvatarData(request); 
            const data = {...body, id: user.id, avatar};
            const result = await this.userService.Update(data);
            this.controllerHelper.handleServiceResult(result, response, 'Update user controller');
        } catch (error: any) {
            next(error);
        }
    }

    async ChangePassword(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const user = this.controllerHelper.validateUser(request, 'Change password controller');
            const body = this.controllerHelper.validateRequestBody<{oldPassword: string, newPassword: string }>(request, 'Change password controller');
            const result = await this.userService.ChangePassword({...body, email: user.email});
            this.controllerHelper.handleServiceResult(result, response, 'Change password controller');
        } catch (error: any) {
            next(error);
        }
    }

    async Delete(request: Request, response: Response, next: NextFunction): Promise<void> {
        try {
            const user = this.controllerHelper.validateUser(request, 'Delete user controller');
            const result = await this.userService.Delete({id: user.id});
            if(result.success) this.controllerHelper.clearCookie(response);
            this.controllerHelper.handleServiceResult(result, response, 'Delete user controller');
        } catch (error: any) {
            next(error);
        }
    }
}

   

   

   

 