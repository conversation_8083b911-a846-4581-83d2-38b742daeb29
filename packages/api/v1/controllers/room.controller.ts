import { NextFunction, Request, Response } from 'express'
import { RoomService } from '../services'
import { AuthService, ControllerHelper } from '../../utils'
import { ICreateRoom } from '../../models'

export class RoomController {
    constructor(private readonly rmService: RoomService, private readonly controllerHelper: ControllerHelper) {
    }

    async Create(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            let body = this.controllerHelper.validateRequestBody<ICreateRoom>(request, 'Create room controller');
            if(request.headers['RestClient'] && request.headers['RestClient'] === 'true' && !body?.applicationId){
                const appInstance = await AuthService.getAppTypeFromApiKeySecret(request.headers['X-API-KEY'] as string, request.headers['X-API-SECRET'] as string, 'General');
                body = { ...body, applicationId: appInstance.id };
            } 
            if(!body?.creatorId){
                // RestClient must definitely have a creatorId
                const user = this.controllerHelper.validateUser(request, 'Create room controller');
                body = { ...body, creatorId: user.id };
            }
            const result =  await this.rmService.createRoom(body);
            this.controllerHelper.handleServiceResult(result, response, "Create room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async Update(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody(request, 'Update room controller');
            const result =  await this.rmService.updateRoom(roomId, body);
            this.controllerHelper.handleServiceResult(result, response, "Update room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async AddGuest(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody<{guestId: string}>(request, 'AddGuest to room controller');
            const result =  await this.rmService.addGuestToRoom(body.guestId, roomId);
            this.controllerHelper.handleServiceResult(result, response, "AddGuest to room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async AddListOfGuests(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody<{guestsId: string[]}>(request, 'AddListOfGuests to room controller');
            const result =  await this.rmService.addListOfGuestsToRoom(body.guestsId, roomId);
            this.controllerHelper.handleServiceResult(result, response, "AddListOfGuests to room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetAllGuests(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const result =  await this.rmService.getRoomGuests(roomId);
            this.controllerHelper.handleServiceResult(result, response, "GetAllMembers of room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async RemoveGuest(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody<{guestId: string}>(request, 'RemoveGuest from room controller');
            const result =  await this.rmService.removeGuestFromRoom(body.guestId, roomId);
            this.controllerHelper.handleServiceResult(result, response, "RemoveGuest from room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async RemoveListOfGuests(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const body = this.controllerHelper.validateRequestBody<{guestsId: string[]}>(request, 'RemoveListOfGuests from room controller');
            const result =  await this.rmService.removeListOfGuestsFromRoom(body.guestsId, roomId);
            this.controllerHelper.handleServiceResult(result, response, "RemoveListOfGuests from room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async FindRooms(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<{query: any}>(request, 'FindRooms in room controller');
            const result =  await this.rmService.findManyRecord(body.query);
            this.controllerHelper.handleServiceResult(result, response, "FindRooms in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async FindRoom(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<{query: any}>(request, 'FindRoom in room controller');
            const result =  await this.rmService.findFirstRecord(body.query);
            this.controllerHelper.handleServiceResult(result, response, "FindRoom in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetRoomFullDetails(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const result =  await this.rmService.getRoomFullDetails(roomId);
            this.controllerHelper.handleServiceResult(result, response, "GetRoomFullDetails in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetRoomMedia(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const result =  await this.rmService.getRoomMedia(roomId);
            this.controllerHelper.handleServiceResult(result, response, "GetRoomMedia in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetRoomMessages(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const result =  await this.rmService.getRoomMessages(roomId);
            this.controllerHelper.handleServiceResult(result, response, "GetRoomMessages in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async UpdateRoomMetaData(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const user = this.controllerHelper.validateUser(request, 'UpdateRoomMetaData in room controller');
            const body = this.controllerHelper.validateRequestBody<{metaData: any}>(request, 'UpdateRoomMetaData in room controller');
            const result =  await this.rmService.updateRoomMetaData(roomId, body.metaData, user.id);
            this.controllerHelper.handleServiceResult(result, response, "UpdateRoomMetaData in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetRoomMetaData(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const result =  await this.rmService.getRoomMetaData(roomId);
            this.controllerHelper.handleServiceResult(result, response, "GetRoomMetaData in room controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async DeleteRoom(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const roomId = request.params.roomId;
            const deleter = this.controllerHelper.validateUser(request, 'DeleteRoom in room controller');
            const result =  await this.rmService.deleteRoom(roomId);
            this.controllerHelper.handleServiceResult(result, response, "DeleteRoom in room controller"); 
        } catch(error: any){
            next(error);
        }
    }
}