import { Request, Response, NextFunction } from 'express';
import { ApplicationService } from '../services/application.service';
import { ControllerHelper } from "../../utils";

export class ApplicationController {
  constructor(
    private readonly appService: ApplicationService,
    private readonly controllerHelper: ControllerHelper,
  ) {}

  async createApplication(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const body = this.controllerHelper.validateRequestBody(request, 'CreateApplication controller');
      const result = await this.appService.createApplication(body);
      this.controllerHelper.handleServiceResult(result, response, 'CreateApplication controller');
    } catch (error: any) {
      next(error);
    }
  }

  async getApplicationById(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const result = await this.appService.getApplicationById(id);
      this.controllerHelper.handleServiceResult(result, response, 'GetApplicationById controller');
    } catch (error: any) {
      next(error);
    }
  }

  async getApplicationsByCompanyId(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const { companyId } = request.params;
      const result = await this.appService.getApplicationsByCompanyId(companyId);
      this.controllerHelper.handleServiceResult(result, response, 'GetApplicationsByCompanyId controller');
    } catch (error: any) {
      next(error);
    }
  }

  async updateApplication(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const body = this.controllerHelper.validateRequestBody(request, 'UpdateApplication controller');
      const result = await this.appService.updateApplication(id, body);
      this.controllerHelper.handleServiceResult(result, response, 'UpdateApplication controller');
    } catch (error: any) {
      next(error);
    }
  }

  async deleteApplication(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = request.params;
      const result = await this.appService.deleteApplication(id);
      this.controllerHelper.handleServiceResult(result, response, 'DeleteApplication controller');
    } catch (error: any) {
      next(error);
    }
  }
}