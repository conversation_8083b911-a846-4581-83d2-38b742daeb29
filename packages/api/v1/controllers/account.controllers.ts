import { NextFunction, Request, Response } from 'express'
import { AccountService } from '../services'
import { ControllerHelper } from '../../utils';
import { ICreateAccountData, IUpdateAccountData } from '../../models';


export class AccountController {
    constructor(private readonly accountService: AccountService, private readonly controllerHelper: ControllerHelper) {
    }

    async Create(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, "Create account controller");
            const body = this.controllerHelper.validateRequestBody<Omit<ICreateAccountData, 'ownerId'>>(request, "Create account controller");
            const data = { ...body, ownerId: user.id } as ICreateAccountData;
            const result =  await this.accountService.Create(data); 
            this.controllerHelper.handleServiceResult(result, response, "Create account controller");
        } catch(error: any){
            next(error);
        }
    }

    async Update(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const { accountId } =  request.params;
            const user = this.controllerHelper.validateUser(request, "Update account controller");
            const body = this.controllerHelper.validateRequestBody<IUpdateAccountData>(request, "Update account controller");
            const result =  await this.accountService.Update({...body, id: accountId, ownerId: user.id});
            this.controllerHelper.handleServiceResult(result, response, "Update account controller");
        } catch(error: any){
            next(error);
        }
    }

    async Delete(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const user = this.controllerHelper.validateUser(request, 'Delete account controller');
            const accountId = request.params.accountId;
            const result =  await this.accountService.Delete({id: accountId, ownerId: user.id});
            this.controllerHelper.handleServiceResult(result, response, "Delete account controller");
        } catch(error: any){
            next(error);
        }
    }
}

