
import { MessageService } from "../services";
import { ControllerHelper, AuthPayload } from "../../utils";
import { Request, Response, NextFunction } from "express";
import { ICreateMessage } from "../../models";



export class MessageController {
    constructor(private readonly msgService: MessageService, private readonly controllerHelper: ControllerHelper) {
    }

    async Create(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            let sender: AuthPayload;
            const body = this.controllerHelper.validateRequestBody<ICreateMessage>(request, 'Create message controller');
            
            if(!body?.senderId) {
                sender = this.controllerHelper.validateUser(request, 'Create message controller');
                body.senderId = sender.id;
            }
            const result =  await this.msgService.createMessage(body);
            this.controllerHelper.handleServiceResult(result, response, "Create message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async Edit(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            const body = this.controllerHelper.validateRequestBody<{text: string}>(request, 'Edit message controller');
            const sender = this.controllerHelper.validateUser(request, 'Edit message controller');
            const result =  await this.msgService.editMessageText(messageId, body?.text, sender.id);
            this.controllerHelper.handleServiceResult(result, response, "Edit message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async Delete(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            const deleter = this.controllerHelper.validateUser(request, 'Delete message controller');
            const result =  await this.msgService.deleteMessage(messageId, deleter.id);
            this.controllerHelper.handleServiceResult(result, response, "Delete message controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async MarkAsRead(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<{messageIds: string[]}>(request, 'Mark message as read controller');
            const ids = body.messageIds;
            const result =  await this.msgService.markMessageRead(ids);
            this.controllerHelper.handleServiceResult(result, response, "Mark message as read controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async CreateThread(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const body = this.controllerHelper.validateRequestBody<ICreateMessage>(request, 'Create thread controller');
            const parentId = request.params.parentId;
            const result =  await this.msgService.createThread(parentId, body);
            this.controllerHelper.handleServiceResult(result, response, "Create thread controller"); 
        } catch(error: any){
            next(error);
        }
    }

    async GetThread(request: Request, response: Response, next: NextFunction ): Promise<void>{
        try{
            const messageId = request.params.messageId;
            const result =  await this.msgService.getThread(messageId);
            this.controllerHelper.handleServiceResult(result, response, "Get thread controller"); 
        } catch(error: any){
            next(error);
        }
    }
}