import { asC<PERSON>, AwilixContainer, createContainer } from "awilix";
import { ControllerHelper } from "../../utils";
import { getLogger } from "../../utils/logger";

import { 
  Company, AccessKey, Account, 
  User, Token, Guest,
  Application, Room, Message,
  File, RoomPermission, CloudStorageProvider
} from "../../models";
import { 
  CompanyService, AccountService, UserService,
  GuestService, ApplicationService, RoomService,
  MessageService, FileService, RoomPermissionService, 
  CloudStorageProviderService
 } from "../services";
import { 
  CompanyController, AccountController, UserController,
  GuestController, ApplicationController, RoomController,
  MessageController, FileController
} from "../controllers";


const logger = getLogger('generals');

// Create the DI container with CLASSIC injection mode
function initializeContainer(): AwilixContainer<any> {
  try {
    const container = createContainer({ injectionMode: 'CLASSIC' });
    return container;
  } catch (error) {
    logger.error("🚨 Failed to create container, operationContext: initializeContainer", error);
    process.exit(1);
  }
}

export const container = initializeContainer();

// Register dependencies in the DI container
container.register({
  /**
   * Utility Helper Dependencies
   */
  controllerHelper: asClass<ControllerHelper>(ControllerHelper).singleton(),
  accessKeyModel: asClass<AccessKey>(AccessKey).singleton(),
  tokenModel: asClass<Token>(Token).singleton(),
  cloudStorageProviderModel: asClass<CloudStorageProvider>(CloudStorageProvider).singleton(),
  cloudStorageProviderService: asClass<CloudStorageProviderService>(CloudStorageProviderService).singleton(),

  /**
   * Company Module Dependencies
   */
  companyModel: asClass<Company>(Company).singleton(),
  companyService: asClass<CompanyService>(CompanyService)
  .inject(() => ({
    companyModel: container.resolve('companyModel'),
    StorageModel: container.resolve('cloudStorageProviderModel')
  }))
  .singleton(),
  companyController: asClass<CompanyController>(CompanyController).singleton(),

  /**
   * Account Module Dependencies
   */
  accountModel: asClass<Account>(Account).singleton(),
  accountService: asClass<AccountService>(AccountService).singleton(),
  accountController: asClass<AccountController>(AccountController).singleton(),

  /**
   *   Application Module Dependencies
   */
  applicationModel: asClass<Application>(Application).singleton(),
  applicationService: asClass<ApplicationService>(ApplicationService).singleton(),
  // Use asClass with explicit injection to avoid parameter renaming issues during render build and start.
  applicationController: asClass(ApplicationController)
    .inject(() => ({
      appService: container.resolve('applicationService'),
      controllerHelper: container.resolve('controllerHelper')
    }))
    .singleton(),

  /**
   * Room Permission Module Dependencies
   */
    roomPermissionModel: asClass<RoomPermission>(RoomPermission).singleton(),
    roomPermissionService: asClass<RoomPermissionService>(RoomPermissionService).singleton(),

  /**
   * Room Module Dependencies
   */
  roomModel: asClass<Room>(Room).singleton(),
  roomService: asClass<RoomService>(RoomService)
  .inject(() => ({
    rmPermissionService: container.resolve('roomPermissionService'),
  }))
  .singleton(),
  roomController: asClass<RoomController>(RoomController)
  .inject(() => ({    
      rmService: container.resolve('roomService'),
      controllerHelper: container.resolve('controllerHelper')
    })).singleton(),


    
  /**
   * User Module Dependencies
   */
  userModel: asClass<User>(User).singleton(),
  userService: asClass<UserService>(UserService).singleton(),
  userController: asClass<UserController>(UserController).singleton(),

  /**
   * Guest Module Dependencies
   */
  guestModel: asClass<Guest>(Guest).singleton(),
  guestService: asClass<GuestService>(GuestService).singleton(),
  // Fix: renamed from GuestController to guestController to match the resolution in guest.route.ts
  guestController: asClass(GuestController)
    .inject(() => ({
      gtService: container.resolve('guestService'),
      rmService: container.resolve('roomService'),
      controllerHelper: container.resolve('controllerHelper')
    }))
    .singleton(),

  
  /**
   * Message Module Dependencies
   */
  messageModel: asClass<Message>(Message).singleton(),
  messageService: asClass<MessageService>(MessageService)
    .inject(() => ({
      messageModel: container.resolve('messageModel'),
      rmService: container.resolve('roomService')
    }))
    .singleton(),
  messageController: asClass<MessageController>(MessageController)
    .inject(() => ({
      msgService: container.resolve('messageService'),
      controllerHelper: container.resolve('controllerHelper')
    }))
    .singleton(),

  /**
   * File Module Dependencies
   */
  fileModel: asClass<File>(File).singleton(),
  fileService: asClass<FileService>(FileService).singleton(),
  fileController: asClass<FileController>(FileController)
    .inject(() => ({
      fileService: container.resolve('fileService'),
      controllerHelper: container.resolve('controllerHelper')
    }))
    .singleton(),

});
