import { Router } from "express";
import { RoomController } from "../controllers";
import { container } from "../container";


class RoomRouter {
    private router: Router;
    constructor(private readonly guestController: RoomController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.guestController.Create(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId", (req, res, next)=> { this.guestController.Update(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/addGuest", (req, res, next)=> { this.guestController.AddGuest(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/addGuests", (req, res, next)=> { this.guestController.AddListOfGuests(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/guests", (req, res, next)=> { this.guestController.GetAllGuests(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/findFirst", (req, res, next)=> { this.guestController.FindRoom(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/findMany", (req, res, next)=> { this.guestController.FindRooms(req, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId/guest", (req, res, next)=> { this.guestController.RemoveGuest(req, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId/guests", (req, res, next)=> { this.guestController.RemoveListOfGuests(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/fullDetails", (req, res, next)=> { this.guestController.GetRoomFullDetails(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/media", (req, res, next)=> { this.guestController.GetRoomMedia(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/messages", (req, res, next)=> { this.guestController.GetRoomMessages(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:roomId/metaData", (req, res, next)=> { this.guestController.UpdateRoomMetaData(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/:roomId/metaData", (req, res, next)=> { this.guestController.GetRoomMetaData(req, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:roomId", (req, res, next)=> { this.guestController.DeleteRoom(req, res, next) });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject company dependencies */
const roomController = container.resolve<RoomController>("roomController");

export const roomRoute: RoomRouter =  new RoomRouter(roomController);