import { Router } from 'express';
import { ApplicationController } from '../controllers/application.controller';
import { container } from '../container';

class ApplicationRouter {
  private router: Router;
  
  constructor(private readonly applicationController: ApplicationController) {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    this.router.post('/', (req, res, next) => this.applicationController.createApplication(req, res, next));
    this.router.get('/:id', (req, res, next) => this.applicationController.getApplicationById(req, res, next));
    this.router.get('/company/:companyId', (req, res, next) => this.applicationController.getApplicationsByCompanyId(req, res, next));
    this.router.put('/:id', (req, res, next) => this.applicationController.updateApplication(req, res, next));
    this.router.delete('/:id', (req, res, next) => this.applicationController.deleteApplication(req, res, next));
  }

  public getRoutes(): Router {
    return this.router;
  }
}

// inject application dependencies
const applicationController = container.resolve<ApplicationController>('applicationController');
export const applicationRoute: ApplicationRouter = new ApplicationRouter(applicationController);

