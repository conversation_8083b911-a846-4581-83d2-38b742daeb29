import { Router, Request, Response } from "express";

class HealthRouter {
    private router: Router;

    constructor() {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        this.router.get("/", (req: Request, res: Response) => {
            res.status(200).json({ message: "Server is up and running", success: true, statusCode: 200 });
        });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

export const healthRoute: HealthRouter = new HealthRouter();