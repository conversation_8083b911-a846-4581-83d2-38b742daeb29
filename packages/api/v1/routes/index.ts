import { Application } from 'express';
import { userRoute } from "./user.route";
import { accountRoute } from './account.route';
import { companyRoute } from './company.route';
import { guestRoute } from './guest.route';
import { applicationRoute } from './application.route';
import { healthRoute } from "./health.route"; 
import { roomRoute } from "./room.route";
import { messageRoute } from './message.route';
import { fileRoute } from './file.route';

export function appRoutes(app: Application, baseApiPath: string): void {
    app.use(`${baseApiPath}/health`, healthRoute.getRoutes());
    app.use(`${baseApiPath}/users`, userRoute.getRoutes());
    app.use(`${baseApiPath}/accounts`, accountRoute.getRoutes());
    app.use(`${baseApiPath}/companies`, companyRoute.getRoutes());
    app.use(`${baseApiPath}/guests`, guestRoute.getRoutes());
    app.use(`${baseApiPath}/applications`, applicationRoute.getRoutes());
    app.use(`${baseApiPath}/rooms`, roomRoute.getRoutes());
    app.use(`${baseApiPath}/messages`, messageRoute.getRoutes());
    app.use(`${baseApiPath}/files`, fileRoute.getRoutes());
};
