import { Router } from "express";
import { FileController } from "../controllers";
import { container } from "../container";
import { FileViewer } from "../../utils";

class FileRouter {
    private router: Router;

    constructor(private readonly fileController: FileController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.fileController.createFile(req as any, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:id", (req, res, next)=> { this.fileController.updateFile(req, res, next) });

        /**
         * @swagger
         */
        this.router.post("/guests", (req, res, next)=> { this.fileController.uploadGuestFile(req as any, res, next) });

        /**
         * @swagger
         */
        this.router.get("/view/:fileId", FileViewer);
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject file dependencies */
const fileController = container.resolve<FileController>("fileController");

export const fileRoute: FileRouter =  new FileRouter(fileController);