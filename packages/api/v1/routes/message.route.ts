import { Router } from "express";
import { container } from "../container";
import { MessageController } from "../controllers";

class MessageRouter {
    private router: Router;

    constructor(private readonly messageController: MessageController) {
        this.router = Router();
        this.initializeRoutes();
    }

    private initializeRoutes(): void {
        /**
         * @swagger
         */
        this.router.post("/", (req, res, next)=> { this.messageController.Create(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/:messageId", (req, res, next)=> { this.messageController.Edit(req, res, next) });

        /**
         * @swagger
         */
        this.router.delete("/:messageId", (req, res, next)=> { this.messageController.Delete(req, res, next) });

        /**
         * @swagger
         */
        this.router.put("/markAsRead", (req, res, next)=> { this.messageController.MarkAsRead(req, res, next) });

        /**
         * @swagger
         */
        this.router.post("/thread/:parentId", (req, res, next)=> { this.messageController.CreateThread(req, res, next) });

        /**
         * @swagger
         */
        this.router.get("/thread/:messageId", (req, res, next)=> { this.messageController.GetThread(req, res, next) });
    }

    public getRoutes(): Router {
        return this.router;
    }
}

/* Inject message dependencies */
const messageController = container.resolve<MessageController>("messageController");

export const messageRoute: MessageRouter =  new MessageRouter(messageController);