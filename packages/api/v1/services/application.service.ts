import { IResponse } from '../../utils';
import { Application, IApplication, ApplicationType } from '../../models';
import { z } from 'zod';

export class ApplicationService {
  constructor(private readonly applicationModel: Application) {}

  async createApplication(data: z.infer<typeof this.applicationModel.createSchema>): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.createApplication(data);
      return this.applicationModel.formatResponse({
        ...this.applicationModel.createdParams,
        data: result,
        message: 'Application created successfully'
      });
    } catch (error: any) {
      if (error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to create application'
      });
    }
  }

  async getApplicationById(id: string): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.getApplicationById(id);
      return this.applicationModel.formatResponse({
        ...this.applicationModel.successParams,
        data: result,
        message: result ? 'Application retrieved successfully' : 'Application not found'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve application'
      });
    }
  }

  async getApplicationsByCompanyId(companyId: string): Promise<IResponse<IApplication[] | null>> {
    try {
      const result = await this.applicationModel.getApplicationsByCompanyId(companyId);
      return this.applicationModel.formatResponse({
        ...this.applicationModel.successParams,
        data: result,
        message: 'Applications retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve applications'
      });
    }
  }

  async getApplicationByCompanyIdAndName(companyId: string, name: string): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.getApplicationByCompanyIdAndName(companyId, name);
      return this.applicationModel.formatResponse({
        ...( result ? this.applicationModel.successParams : this.applicationModel.notFoundParams), 
        data: result,
        message: result ? 'Application retrieved successfully' : 'Application not found'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve application'
      });
    }
  }

  async getApplicationByIdAndName(id: string, name: string): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.getApplicationByIdAndName(id, name);
      return this.applicationModel.formatResponse({
        ...( result ? this.applicationModel.successParams : this.applicationModel.notFoundParams), 
        data: result,
        message: result ? 'Application retrieved successfully' : 'Application not found'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve application'
      });
    }
  }

  async getApplicationByIdAndType(id: string, type: ApplicationType): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.getApplicationByIdAndType(id, type);
      return this.applicationModel.formatResponse({
        ...( result ? this.applicationModel.successParams : this.applicationModel.notFoundParams), 
        data: result,
        message: result ? 'Application retrieved successfully' : 'Application not found'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve application'
      });
    }
  }

  async getApplicationByCompanyIdAndType(companyId: string, type: ApplicationType): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.getApplicationByCompanyIdAndType(companyId, type);
      return this.applicationModel.formatResponse({
        ...( result ? this.applicationModel.successParams : this.applicationModel.notFoundParams), 
        data: result,
        message: result ? 'Application retrieved successfully' : 'Application not found'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve application'
      });
    }
  }

  async updateApplication(id: string, data: z.infer<typeof this.applicationModel.updateSchema>): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.updateApplication(id, data);
      return this.applicationModel.formatResponse({
        ...this.applicationModel.successParams,
        data: result,
        message: 'Application updated successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to update application'
      });
    }
  }

  async deleteApplication(id: string): Promise<IResponse<IApplication | null>> {
    try {
      const result = await this.applicationModel.deleteApplication(id);
      return this.applicationModel.formatResponse({
        ...this.applicationModel.successParams,
        data: result,
        message: 'Application deleted successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.applicationModel.formatResponse<null>({
          ...this.applicationModel.badRequestParams,
          message: error.message
        });
      }
      return this.applicationModel.formatResponse<null>({
        ...this.applicationModel.internalServerErrorParams,
        message: error.message || 'Failed to delete application'
      });
    }
  }
}