import { Account, IAccount, ICreateAccountData, IDelete, IGenerateAccountData, IUpdateAccountData } from "../../models/";
import { IResponse } from "../../utils";
export class AccountService {
    
    constructor(private readonly accountModel: Account) {
    }

  /** Create a new account*/
  async Create(data: ICreateAccountData): Promise<IResponse<IGenerateAccountData | null>> {
    try {
        const result = await this.accountModel.createAccount(data);
        return this.accountModel.formatResponse({...this.accountModel.createdParams, data: result, message: 'Account created successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('You are already'))) {
            return this.accountModel.formatResponse<null>({...this.accountModel.badRequestParams, message: error.message});
        }
        return this.accountModel.formatResponse<null>({...this.accountModel.internalServerErrorParams});
    }
  }

  async Update(data: IUpdateAccountData): Promise<IResponse<IAccount | null>> {
    try {
        
        const result  = await this.accountModel.updateAccount(data);
        return this.accountModel.formatResponse({...this.accountModel.successParams, data: result, message: 'Account updated successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update account')) {
            return this.accountModel.formatResponse<null>({...this.accountModel.notFoundParams, message: error.message});
        }
        if(error.message && error.message.includes('Validation failed')) {
            return this.accountModel.formatResponse<null>({...this.accountModel.badRequestParams, message: error.message});
        }
        return this.accountModel.formatResponse<null>({...this.accountModel.internalServerErrorParams});
    }
  }

    async Delete(data: {id: string, ownerId: string}): Promise<IResponse<IDelete>> {
        try {
            const result = await this.accountModel.deleteAccount(data);
            return this.accountModel.formatResponse<null>({...this.accountModel.successParams, message: 'Account deleted successfully'});
        } catch (error: any) {
            if(error.message && error.message.includes('Unable to delete account')) {
                return this.accountModel.formatResponse<IDelete>({...this.accountModel.notFoundParams, data: null, message: error.message});
            }
            if(error.message && error.message.includes('Validation failed')) {
                return this.accountModel.formatResponse<IDelete>({...this.accountModel.badRequestParams, data: null, message: error.message});
            }
            return this.accountModel.formatResponse<IDelete>({...this.accountModel.internalServerErrorParams, data: null});
        }
    }
}