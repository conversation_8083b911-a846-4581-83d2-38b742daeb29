import { RoomPermission, IRoomPermissionType, IPermissionEntityType, IRoomPermission } from "../../models";
import {  IResponse } from "../../utils/database";

export class RoomPermissionService {
    constructor(private readonly roomPermissionModel: RoomPermission) {
    }

    async createDefaultPermissions(roomId: string, creatorId: string): Promise<IResponse<boolean>> {
        try {
            const result = await this.roomPermissionModel.createDefaultPermissions(roomId, creatorId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams, 
                data: result,
                message: result ? 'Room creator and default membership permissions set' : 'Failed to set up room creator and default membership permissions'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to set up room creator and default membership permissions'
            });
        }
    }

    async checkRoomPermission(roomId: string, entityId: string, entityType: IPermissionEntityType, permission: IRoomPermissionType): Promise<IResponse<boolean>> {
        try {
            const result = await this.roomPermissionModel.checkRoomPermission(roomId, entityId, entityType, permission);
            return this.roomPermissionModel.formatResponse({
                ...(result ? this.roomPermissionModel.successParams : this.roomPermissionModel.notFoundParams), 
                data: result,
                message: result ? 'Permission granted' : 'Permission denied'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            } else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            } else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to check permission'
            });
        }
    }

    async checkCallerPermission(roomId: string, callerId: string, permission: IRoomPermissionType): Promise<IResponse<boolean>> {
        try {
            const result = await this.roomPermissionModel.checkCallerPermission(roomId, callerId, permission);
            return this.roomPermissionModel.formatResponse({
                ...(result ? this.roomPermissionModel.successParams : this.roomPermissionModel.notFoundParams), 
                data: result,
                message: result ? 'Permission granted' : 'Permission denied'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            } else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            } else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to check permission'
            });
        }
    }

    async makeMemberAnAdmin(roomId: string, memberId: string, callerId?: string): Promise<IResponse<IRoomPermission[]>> {
        try {
            const result = await this.roomPermissionModel.makeMemberAnAdmin(roomId, memberId, callerId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams, 
                data: result,
                message: 'Member made an admin'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to make member an admin'
            });
        }
    }

    async makeMemberAModerator(roomId: string, memberId: string, callerId?: string): Promise<IResponse<IRoomPermission[]>> {
        try {
            const result = await this.roomPermissionModel.makeMemberAModerator(roomId, memberId, callerId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams, 
                data: result,
                message: 'Member made a moderator'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to make member a moderator'
            });
        }
    }

    async revokeRoomPermissions(roomId: string, entityId: string, entityType: IPermissionEntityType, permissions?: IRoomPermissionType[], callerId?: string): Promise<IResponse<IRoomPermission[]>> {
        try {
            const result = await this.roomPermissionModel.revokeRoomPermissions(roomId, entityId, entityType, permissions, callerId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams, 
                data: result,
                message: 'Permissions revoked'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to revoke permissions'
            });
        }
    }

    async reinstateRoomPermissions(roomId: string, entityId: string, entityType: IPermissionEntityType, permissions?: IRoomPermissionType[], callerId?: string): Promise<IResponse<IRoomPermission[]>> {
        try {
            const result = await this.roomPermissionModel.reinstateRoomPermissions(roomId, entityId, entityType, permissions, callerId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams,  
                data: result,
                message: 'Permissions reinstated'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to reinstate permissions'
            });
        }
    }

    async deleteRoomPermissions(roomId: string, entityId: string, entityType: IPermissionEntityType, permissions?: IRoomPermissionType[], callerId?: string): Promise<IResponse<IRoomPermission[]>> {
        try {
            const result = await this.roomPermissionModel.deleteRoomPermissions(roomId, entityId, entityType, permissions, callerId);
            return this.roomPermissionModel.formatResponse({
                ...this.roomPermissionModel.successParams, 
                data: result,
                message: 'Permissions deleted'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.badRequestParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Forbidden')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.forbiddenParams,
                    message: error.message
                });
            }else if(error.message && error.message.includes('Not Found')) {
                return this.roomPermissionModel.formatResponse<null>({
                    ...this.roomPermissionModel.notFoundParams,
                    message: error.message
                });
            }
            return this.roomPermissionModel.formatResponse<null>({
                ...this.roomPermissionModel.internalServerErrorParams,
                message: error.message || 'Failed to delete permissions'
            });
        }
    }
}