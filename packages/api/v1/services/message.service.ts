import { Message, IMessage, ICreateMessage, IUpdateMessage, IRoomPermissionType, } from "../../models";
import { IResponse } from "../../utils";
import { RoomService } from "./room.service";

export class MessageService {
    constructor(private readonly messageModel: Message, private readonly rmService: RoomService) {
    }

    async createMessage(data: ICreateMessage): Promise<IResponse<IMessage | null>> {
        try {
            const result = await this.messageModel.createMessage(data);
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message created successfully'
            });
        } catch (error) {
            if(error.message && error.message.includes('Validation failed')) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: error.message
                });
            }
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to create message'
            });
        }
    }

    async editMessageText(id: string, text: string, updatorId: string): Promise<IResponse<IMessage | null>> {
        try {
            const message = await this.messageModel.getMessageById(id);
            if(!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }
            if(message.sender.id !== updatorId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.forbiddenParams,
                    message: 'Cannot edit another member\'s message.'
                });
            }
            const result = await this.messageModel.editMessage(id, text );
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message edited successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to update message'
            });
        }
    }

    async deleteMessage(id: string, deleterId: string): Promise<IResponse<IMessage | null>> {
        try {
            const message = await this.messageModel.getMessageById(id);
            if(!message) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.notFoundParams,
                    message: 'Message not found'
                });
            }
            if(message.sender.id !== deleterId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.forbiddenParams,
                    message: 'Cannot delete another member\'s message.'
                });
            }
            await this.messageModel.deleteMessage(id);
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: message,
                message: 'Message deleted successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to delete message'
            });
        }
    }


    async markMessageRead(ids: string[]): Promise<IResponse<boolean>> {
        try {
            const result = await this.messageModel.markMessageRead(ids);
            return this.messageModel.formatResponse({
                ...this.messageModel.successParams,
                data: result,
                message: 'Message marked as read successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to mark message as read'
            });
        }
    }

    async createThread(parentId: string, data: ICreateMessage): Promise<IResponse<IMessage | null>> {
        try {
            
            if(!data.senderId || !parentId) {
                return this.messageModel.formatResponse<null>({
                    ...this.messageModel.badRequestParams,
                    message: 'Missing SenderId or parentId'
                });
            }
 
            const result = await this.messageModel.createThread(parentId, data);
            return this.messageModel.formatResponse<IMessage>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Thread created successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<null>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to create thread'
            });
        }
    }

    async getThread(id: string): Promise<IResponse<IMessage[]>> {
        try {
            const result = await this.messageModel.getThread(id);
            return this.messageModel.formatResponse<IMessage[]>({
                ...this.messageModel.successParams,
                data: result,
                message: 'Thread retrieved successfully'
            });
        } catch (error) {
            return this.messageModel.formatResponse<[]>({
                ...this.messageModel.internalServerErrorParams,
                message: error.message || 'Failed to retrieve thread'
            });
        }
    }
}