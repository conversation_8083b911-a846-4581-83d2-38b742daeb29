import { User, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>User<PERSON><PERSON>al<PERSON><PERSON>, IUpdate<PERSON>ser, <PERSON>elete } from "../../models";
import { IResponse } from "../../utils";

export class UserService  {
    
  constructor(private readonly userModel: User) {
}


/**
 * Creates a new user with optionally analytic record .
 *
 * @template IUser - The user data type.
 * @param {ICreateUser} data - The user data to create.
 * @param {IAnalytic} [analytic] - Optional analytic data to associate with the user.
 * @returns {Promise<IResponse<IUserGeneralData | null>>} - A promise that resolves to the response object containing the created user data or an error message.
 */
  async Create(data: ICreateUser, analytic?:  any): Promise<IResponse<IUserGeneralData | null>> {
    try {
        const result = await this.userModel.createUser({details: data, analytic});
        return this.userModel.formatResponse({...this.userModel.createdParams, data: result, message: "User created successfully"});
    } catch (error: any) {
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('User with this email already exist'))) {
            return this.userModel.formatResponse<null>({...this.userModel.badRequestParams, data: null, message: error.message});
        }
        return this.userModel.formatResponse<null>({...this.userModel.internalServerErrorParams, data: null});
    }
  }

  async Login(data: {email: string, password: string}): Promise<IResponse<{token: string, data: IUserGeneralData} | null>> {
    try {
        const result = await this.userModel.loginUser(data);
        return this.userModel.formatResponse<{token: string, data: IUserGeneralData}>({...this.userModel.successParams, data: result, message: "Logged in successfully"});
    } catch (error: any) {
      if(error.message && error.message.includes('Invalid email or password')) {
        return this.userModel.formatResponse<null>({...this.userModel.badRequestParams, message: error.message});
      }
        return this.userModel.formatResponse<null>(this.userModel.internalServerErrorParams);
    }
  }
  
    /**  Update a user data by id or username or email */
  async Update(data: IUpdateUser): Promise<IResponse<IUserGeneralData | null>> {
    try {
        const result = await this.userModel.updateUser(data, true);
        return this.userModel.formatResponse<IUserGeneralData>({...this.userModel.successParams, data: result, message: "Data updated successfully"});
    } catch (error: any) {
        if(error.message && error.message.includes('Validation failed') || error.message.includes('unable to update user')) {
            return this.userModel.formatResponse<null>({...this.userModel.badRequestParams, message: error.message});
        }
        if(error.message && error.message.includes('The Provided user')) {
            return this.userModel.formatResponse<null>({...this.userModel.notFoundParams, message: error.message});
        }
        return this.userModel.formatResponse<null>({...this.userModel.internalServerErrorParams});
    }
  }

  async ChangePassword(data: {email: string, oldPassword: string, newPassword: string}): Promise<IResponse<null>>
  {
    try {
        await this.userModel.changePassword(data);
        return this.userModel.formatResponse<null>({...this.userModel.successParams, message: "Password changed successfully"});
    } catch (error: any) {
        if(error.message && error.message.includes('The Provided user email')) {
          return this.userModel.formatResponse<null>({...this.userModel.notFoundParams, message: error.message});
       }
  
        if(error.message && (error.message.includes('Invalid email or password') || error.message.includes('Invalid old password') || error.message.includes('New password cannot be'))) {
            return this.userModel.formatResponse<null>({...this.userModel.badRequestParams, message: error.message});
        }
        return this.userModel.formatResponse<null>({...this.userModel.internalServerErrorParams});
    }
  }

  /**  Delete a user by id or email */
  async Delete(data: {id: string} | {email: string}): Promise<IResponse<IDelete>> {
    try {
        await this.userModel.deleteUser(data, true);
        return this.userModel.formatResponse<IDelete>({...this.userModel.successParams, data: null, message: "User deleted successfully"});
    } catch (error: any) {
        if(error.message && (error.message.includes('The Provided user') || error.message.includes('Validation failed'))) {
            return this.userModel.formatResponse<null>({...this.userModel.notFoundParams, message: error.message});
        }
        return this.userModel.formatResponse<null>({...this.userModel.internalServerErrorParams});
    }
  }

}

