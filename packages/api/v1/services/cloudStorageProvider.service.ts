import { CloudStorageProvider,  ICloudStorageProvider, ICreateCloudStorageProvider, IFileCloudStorageProvider, IUpdateCloudStorageProvider } from '../../models';
import { IResponse } from '../../utils/database';

export class CloudStorageProviderService {
  constructor(private readonly cloudStorageProviderModel: CloudStorageProvider) {
  }

  async createCloudStorageProvider(data: ICreateCloudStorageProvider): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.createCloudStorageProvider(data);
      return this.cloudStorageProviderModel.formatResponse<ICloudStorageProvider>({
        ...this.cloudStorageProviderModel.createdParams,
        data: result,
        message: 'CloudStorageProvider created successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to create CloudStorageProvider'
      });
    }
  }

  async getCloudStorageProvider(id: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.getCloudStorageProvider(id);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.notFoundParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async updateCloudStorageProvider(id: string, data: IUpdateCloudStorageProvider): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.updateCloudStorageProvider(id, data);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProvider updated successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.notFoundParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to update CloudStorageProvider'
      });
    }
  }

  async getCloudStorageProviderByCompanyId(companyId: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.getCloudStorageProviderByCompanyId(companyId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.notFoundParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async getPrimaryCloudStorageProvider(companyId: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.getPrimaryCloudStorageProvider(companyId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.notFoundParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async getActiveCloudStorageProviders(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.cloudStorageProviderModel.getActiveCloudStorageProviders(companyId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

  async getRevokedCloudStorageProviders(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.cloudStorageProviderModel.getRevokedCloudStorageProviders(companyId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

  async getCloudStorageProvidersByCompanyId(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.cloudStorageProviderModel.getCloudStorageProvidersByCompanyId(companyId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

  async getFileCloudStorageProvider(fileId: string): Promise<IResponse<IFileCloudStorageProvider | null>> {
    try {
      const result = await this.cloudStorageProviderModel.getFileCloudStorageProvider(fileId);
      return this.cloudStorageProviderModel.formatResponse({
        ...this.cloudStorageProviderModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found') || error.message.includes('Not Available')) {
        return this.cloudStorageProviderModel.formatResponse<null>({
          ...this.cloudStorageProviderModel.notFoundParams,
          message: error.message
        });
      }
      return this.cloudStorageProviderModel.formatResponse<null>({
        ...this.cloudStorageProviderModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

}