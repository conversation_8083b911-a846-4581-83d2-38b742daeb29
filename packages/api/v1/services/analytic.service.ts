// import { Analytic } from "../../models/analytic/analytic.model";
// import { IAnalytic } from "../../models/analytic/analytic.types";
// import { IResponse } from "../../utils";

// export class AnalyticService extends Analytic {
    
//     constructor() {
//     super();
//   }

//   /** Create a new analytic*/
//   async Create(data: IAnalytic): Promise<IResponse<any>> {
//     try {
//         const result = await this.createAnalytic(data);
//         return this.formatResponse<IAnalytic>({...this.createdParams, data: result, message: 'Analytic created successfully'});
//     } catch (error: any) {
//         if(error.message && error.message.includes('Validation failed')) {
//             return this.formatResponse({...this.badRequestParams, message: error.message});
//         }
//         return this.formatResponse({...this.internalServerErrorParams});
//     }
//   }

//   async Update(data: IAnalytic): Promise<IResponse<any>> {
//     try {

//         const result = await this.updateAnalytic({data, where: {id: data.id}});
//         return this.formatResponse({...this.successParams, data: result, message: 'Analytic updated successfully'});
//     } catch (error: any) {
//         if(error.message && error.message.includes('Validation failed')) {
//             return this.formatResponse({...this.badRequestParams, message: error.message});
//         }
//         return this.formatResponse({...this.internalServerErrorParams});
//     }
//   }
// }