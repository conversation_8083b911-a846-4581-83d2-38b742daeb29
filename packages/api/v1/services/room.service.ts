import { permission } from "process";
import { 
  Room, ICreateRoom, <PERSON>Room, 
  IUpdateRoom, IRoomGuests,
  IRoomMedia, RoomType, IRoomPermissionType, IPermissionEntityType,
  IMessage
 } from "../../models/";
import { DBLogger, IResponse } from "../../utils/database";
import { RoomPermissionService } from "./roomPermissionService";

export class RoomService {

  constructor(private readonly roomModel: Room, private readonly rmPermissionService: RoomPermissionService) {
  }

  async createRoom(data: ICreateRoom): Promise<IResponse<IRoom | null>> {
    try {
      if(!data.creatorId && data.type === RoomType.group) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: 'CreatorId is required for group room creation'
        });
      }
      const result = await this.roomModel.createRoom(data, async (roomId: string, creatorId: string) => {
        await this.rmPermissionService.createDefaultPermissions(roomId, creatorId);
      });
      return this.roomModel.formatResponse({
        ...this.roomModel.createdParams,
        data: result,
        message: 'Room created successfully'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to create room'
      });
    }
  }

  async updateRoom(id: string, data: IUpdateRoom): Promise<IResponse<string>> {
    try {
      const result =  await this.roomModel.updateRoom(id, data);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room updated successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Not Found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Forbidden')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.forbiddenParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to update room'
      });
    }
  }

  async getRoomById(id: string): Promise<IResponse<IRoom | null>> {
    try {
      const result = await this.roomModel.getRoomById(id);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room retrieved successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve room'
      });
    }
  }


  async deleteRoom(id: string): Promise<IResponse<string | null>> {
    try {
      await this.roomModel.deleteRoom(id);
      return this.roomModel.formatResponse({
        ...(this.roomModel.successParams),
        data: null,
        message: 'Room deleted successfully'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Not Found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Forbidden')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.forbiddenParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to delete room'
      });
    }
  }

  async addUserToRoom(userId: string, roomId: string): Promise<IResponse<number>> {
    try {
      const result = await this.roomModel.addUserToRoom(userId, roomId);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'User added to room successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Not Found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }

      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to add user to room'
      });
    }
  }

  async addGuestToRoom(guestId: string, roomId: string): Promise<IResponse<number>> {
    try {
      const result = await this.roomModel.addGuestToRoom(guestId, roomId);
      return this.roomModel.formatResponse({
        ...(this.roomModel.successParams), 
        data: result,
        message: 'Guest added to room successfully'
      });
    } catch (error) {
      if(error.message && (error.message.includes('Validation failed') || error.message.includes('Guest already in room'))) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }else if(error.message && error.message.includes('Room not found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to add guest to room'
      });
    }
  }

  async addListOfGuestsToRoom(guestsId: string[], roomId: string): Promise<IResponse<number>> {
    try {
      const result = await this.roomModel.addListOfGuestsToRoom(guestsId, roomId);
      return this.roomModel.formatResponse({
        ...(this.roomModel.successParams), 
        data: result,
        message: 'Guests added to room successfully'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to add guests to room'
      });
    }
  }

  async removeListOfGuestsFromRoom(guestsId: string[], roomId: string): Promise<IResponse<null>> {
    try {
      await this.roomModel.removeListOfGuestsFromRoom(guestsId, roomId);
      return this.roomModel.formatResponse({
        ...(this.roomModel.successParams), 
        data: null,
        message: 'Guests removed from room successfully'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to remove guests from room'
      });
    }
  }

  async getRoomGuests(roomId: string): Promise<IResponse<IRoomGuests[] | null>> {
    try {
      const result = await this.roomModel.getRoomGuests(roomId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Guests retrieved successfully' : 'Guests not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve guests'
      });
    }
  }

  async removeUserFromRoom(userId: string, roomId: string): Promise<IResponse<null>> {
    try {
      await this.roomModel.removeUserFromRoom(userId, roomId);
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.successParams, 
        data: null,
        message: 'User removed from room successfully'});
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      } else if(error.message && (error.message.includes('User not found in room') || error.message.includes('Room not found'))) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to remove user from room'
      });
    }
  }

  async removeGuestFromRoom(guestId: string, roomId: string): Promise<IResponse<number>> {
    try {
      const result = await this.roomModel.removeGuestFromRoom(guestId, roomId);
      return this.roomModel.formatResponse({
        ...this.roomModel.successParams, 
        data: result,
        message: 'Guest removed from room successfully'});
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      } else if(error.message && (error.message.includes('Guest not found in room') || error.message.includes('Room not found'))) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to remove guest from room'
      });
    }
  }

  async updateOnlineMembersCount(roomId: string, count: number): Promise<IResponse<number>> {
    try {
      const result = await this.roomModel.updateOnlineMembersCount(roomId, count);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Online members count updated successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed') || error.message.includes('Online members count cannot be negative')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      } else if (error.message && error.message.includes('Room not found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to update online members count'
      });
    }
  }

  async getRoomsByApplicationId(applicationId: string): Promise<IResponse<IRoom[] | null>> {
    try {
      const result = await this.roomModel.getRoomsByApplicationId(applicationId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Rooms retrieved successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve rooms'
      });
    }
  }

  async getRoomsByUserId(userId: string): Promise<IRoom[]> {
    try {
      return await this.roomModel.getRoomsByUserId(userId);
    } catch (error) {
      DBLogger.error('RoomService: Error getting rooms by user ID:', error);
      throw error;
    }
  }

  async getRoomsByGuestId(guestId: string): Promise<IResponse<IRoom[] | null>> {
    try {
      const result = await this.roomModel.getRoomsByGuestId(guestId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Rooms retrieved successfully' : 'No Rooms found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve room'
      });
    }
  }

  async getRoomsByGuestIdAndApplicationId(guestId: string, applicationId: string): Promise<IResponse<IRoom[] | null>> {
    try {
      const result = await this.roomModel.getRoomsByGuestIdAndApplicationId(guestId, applicationId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Room retrieved successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve room'
      });
    }
  }

  async getRoomsByGuestIdAndAppIdAndCompanyId(guestId: string, applicationId: string, companyId: string): Promise<IResponse<IRoom[] | null>> {
    try {
      const result = await this.roomModel.getRoomsByGuestIdAndAppIdAndCompanyId(guestId, applicationId, companyId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Room retrieved successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve room'
      });
    }
  }

  async archiveRoom(roomId: string): Promise<IResponse<string | null>> {
    try {
      const result = await this.roomModel.updateRoom(roomId, { archived: true });
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room archived successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to archive room'
      });
    }
  }

  async isGuestMemberOfRoom(guestId: string, roomId: string): Promise<IResponse<boolean>> {
    try {
      const result = await this.roomModel.isGuestInRoom(guestId, roomId);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Guest is member of room' : 'Guest is not member of room'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Room not found')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.notFoundParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to check if guest is member of room'
      });
    }
  }

  async findFirstRecord(query: any): Promise<IResponse<any>> {
    try {
      const result = await this.roomModel.findFirstRecord(query);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room found' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find room'
      });
    }
  }

  async findManyRecord(query: any): Promise<IResponse<any>> {
    try {
      const result = await this.roomModel.findManyRecord(query);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Rooms found' : 'Rooms not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find rooms'
      });
    }
  }

  async getRoomFullDetails(roomId: string): Promise<IResponse<IRoom | null>> {
    try {
      const result = await this.roomModel.getRoomFullDetails(roomId);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room found' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find room'
      });
    }
  };

  async getRoomMedia(roomId: string): Promise<IResponse<IRoomMedia[] | null>> {
    try {
      const result = await this.roomModel.getRoomMedia(roomId);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Room media found' : 'Room media not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find room media'
      });
    }
  };

  async getRoomMessages(roomId: string, limit?: number, cursor?: string): Promise<IResponse<IMessage[] | null>> {
    try {
      const result = await this.roomModel.getRoomMessages(roomId, limit, cursor);
      return this.roomModel.formatResponse({
        ...(result.length > 0 ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result.length > 0 ? 'Room messages found' : 'Room messages not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find room messages'
      });
    }
  };

  async updateRoomMetaData(roomId: string, metaData: any, callerId: string): Promise<IResponse<any>> {
    try {
      const result = await this.roomModel.updateRoomMetaData(roomId, metaData);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room meta data updated successfully' : 'Room not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to update room meta data'
      });
    }
  };

  async getRoomMetaData(roomId: string): Promise<IResponse<any>> {
    try {
      const result = await this.roomModel.getRoomMetaData(roomId);
      return this.roomModel.formatResponse({
        ...(result ? this.roomModel.successParams : this.roomModel.notFoundParams), 
        data: result,
        message: result ? 'Room meta data found' : 'Room meta data not found'
      });
    } catch (error) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.roomModel.formatResponse<null>({
          ...this.roomModel.badRequestParams,
          message: error.message
        });
      }
      return this.roomModel.formatResponse<null>({
        ...this.roomModel.internalServerErrorParams,
        message: error.message || 'Failed to find room meta data'
      });
    }
  };

}