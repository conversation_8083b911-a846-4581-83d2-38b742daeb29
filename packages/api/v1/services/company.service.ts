
import { CloudStorageProvider, Company, IAccessKey, ICloudStorageProvider, ICompany, ICreateCloudStorageProvider, ICreateCompanyAccesskey, ICreateCompanyData, IDeleteCompanyAccesskey, IUpdateCloudStorageProvider, IUpdateCompanyAccesskey, User} from "../../models/";
import { IResponse } from "../../utils";

interface UserRequestPayload { id: string, email: string};
export class CompanyService {
    
    constructor(private readonly companyModel: Company, private readonly StorageModel: CloudStorageProvider) {
  }

  /** Create a new Company*/
  async Create(data: ICreateCompanyData, creatorData: UserRequestPayload): Promise<IResponse<ICompany>> {
    try {
        const result = await this.companyModel.createCompany(data, {id: creatorData.id});
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.createdParams, data: result, message: 'Company created successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('Access denied'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async Update(data: Partial<ICompany>, user: UserRequestPayload, roleName: string): Promise<IResponse<ICompany | null>> {
    try {
        const result = await this.companyModel.updateCompany(data, user, roleName);
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.successParams, data: result, message: 'Company updated successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update company')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('Access denied'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async GenerateAccessKey(data: ICreateCompanyAccesskey, user: UserRequestPayload): Promise<IResponse<IAccessKey>> {
    try {
        const result = await this.companyModel.createCompanyAccessKey(data, user);
        return this.companyModel.formatResponse<IAccessKey>({...this.companyModel.successParams, data: result, message: 'Api key generated successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Access denied') || error.message.includes('Validation failed'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        if(error.message && error.message.includes('Unable to')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async UpdateAccessKey(data: IUpdateCompanyAccesskey, user: UserRequestPayload): Promise<IResponse<IAccessKey | null>> {
    try {
        const result = await this.companyModel.updateCompanyAccessKey(data, user);
        return this.companyModel.formatResponse<IAccessKey>({...this.companyModel.successParams, data: result, message: 'Access key updated successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Access denied') || error.message.includes('Validation failed'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        if(error.message && error.message.includes('Unable to')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async GetAccessKey(data: {key: string, companyId: string, accountId: string}, user: UserRequestPayload): Promise<IResponse<IAccessKey | null>> {
    try {
        const result =  {} as IAccessKey; //await this.companyModel.getCompanyAccessKey(data, {id: user.id});
        return this.companyModel.formatResponse<IAccessKey>({...this.companyModel.successParams, data: result, message: 'Access key retrieved successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Access denied') || error.message.includes('Validation failed'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        if(error.message && error.message.includes('Unable to')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async DeleteAccessKey(data: IDeleteCompanyAccesskey, user: UserRequestPayload): Promise<IResponse<null>> {
    try {
        await this.companyModel.deleteCompanyAccessKey(data, {id: user.id});
        return this.companyModel.formatResponse<null>({...this.companyModel.successParams, message: 'Access key deleted successfully'});
    } catch (error: any) {
        if(error.message && (error.message.includes('Access denied') || error.message.includes('Validation failed'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        if(error.message && error.message.includes('Unable to')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }
  }

  async UpdateCompanyGuestData(companyId: string, guestIdsToAdd: string[], guestIdsToRemove: string[]): Promise<IResponse<ICompany | null>> {
    try {
        const result = await this.companyModel.updateCompanyGuests(companyId, guestIdsToAdd, guestIdsToRemove);
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.successParams, data: result, message: 'Company guest data updated successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update company')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('Access denied'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }   
  }

  async UpdateCompanyDomains(companyId: string, domainsToAdd: string[], domainsToRemove: string[]): Promise<IResponse<ICompany | null>> {
    try {
        const result = await this.companyModel.updateCompanyDomains(companyId, domainsToAdd, domainsToRemove);
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.successParams, data: result, message: 'Company domains updated successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update company')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('Access denied'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }   
  }
  
  async UpdateCompanyUserData(companyId: string, userIdsToAdd: string[], userIdsToRemove: string[]): Promise<IResponse<ICompany | null>> {
    try {
        const result = await this.companyModel.updateCompanyUsers(companyId, userIdsToAdd, userIdsToRemove);
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.successParams, data: result, message: 'Company user data updated successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update company')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
        if(error.message && (error.message.includes('Validation failed') || error.message.includes('Access denied'))) {
            return this.companyModel.formatResponse<null>({...this.companyModel.badRequestParams, message: error.message});
        }
        return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
    }   
  }

  async GetCompanyById(companyId: string): Promise<IResponse<ICompany | null>> {
    try {
        const result = await this.companyModel.getCompanyById(companyId);
        return this.companyModel.formatResponse<ICompany>({...this.companyModel.successParams, data: result, message: 'Company retrieved successfully'});
    } catch (error: any) {
        if(error.message && error.message.includes('Unable to update company')) {
            return this.companyModel.formatResponse<null>({...this.companyModel.notFoundParams, message: error.message});
        }
    }
    return this.companyModel.formatResponse<null>({...this.companyModel.internalServerErrorParams});
  }

  async createCloudStorageProvider(data: ICreateCloudStorageProvider): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.StorageModel.createCloudStorageProvider(data);
      return this.StorageModel.formatResponse<ICloudStorageProvider>({
        ...this.StorageModel.createdParams,
        data: result,
        message: 'CloudStorageProvider created successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.notFoundParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to create CloudStorageProvider'
      });
    }
  }

  async getCloudStorageProvider(id: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.StorageModel.getCloudStorageProvider(id);
      return this.StorageModel.formatResponse<ICloudStorageProvider>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.notFoundParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async updateCloudStorageProvider(id: string, data: IUpdateCloudStorageProvider): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.StorageModel.updateCloudStorageProvider(id, data);
      return this.StorageModel.formatResponse<ICloudStorageProvider>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProvider updated successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.notFoundParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to update CloudStorageProvider'
      });
    }
  }

  async getCloudStorageProviderByCompanyId(companyId: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.StorageModel.getCloudStorageProviderByCompanyId(companyId);
      return this.StorageModel.formatResponse<ICloudStorageProvider>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.notFoundParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async getPrimaryCloudStorageProvider(companyId: string): Promise<IResponse<ICloudStorageProvider | null>> {
    try {
      const result = await this.StorageModel.getPrimaryCloudStorageProvider(companyId);
      return this.StorageModel.formatResponse<ICloudStorageProvider>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProvider retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      if(error.message && error.message.includes('Not Found')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.notFoundParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProvider'
      });
    }
  }

  async getActiveCloudStorageProviders(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.StorageModel.getActiveCloudStorageProviders(companyId);
      return this.StorageModel.formatResponse<ICloudStorageProvider[]>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

  async getRevokedCloudStorageProviders(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.StorageModel.getRevokedCloudStorageProviders(companyId);
      return this.StorageModel.formatResponse<ICloudStorageProvider[]>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

  async getCloudStorageProvidersByCompanyId(companyId: string): Promise<IResponse<ICloudStorageProvider[]>> {
    try {
      const result = await this.StorageModel.getCloudStorageProvidersByCompanyId(companyId);
      return this.StorageModel.formatResponse<ICloudStorageProvider[]>({
        ...this.StorageModel.successParams, 
        data: result,
        message: 'CloudStorageProviders retrieved successfully'
      });
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.StorageModel.formatResponse<null>({
          ...this.StorageModel.badRequestParams,
          message: error.message
        });
      }
      return this.StorageModel.formatResponse<null>({
        ...this.StorageModel.internalServerErrorParams,
        message: error.message || 'Failed to retrieve CloudStorageProviders'
      });
    }
  }

}