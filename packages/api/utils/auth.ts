
import jwt, { Secret }  from  "jsonwebtoken";
import bcrypt from "bcrypt";
import { ApiError, prisma} from ".";
import crypto  from "crypto";
import { NextFunction, Request, Response } from "express";
import { $Enums } from "@prisma/client";

interface ICompanyDomains {
  companyId: string;
  domains: string[];
}

interface ICompanyAndApplicationFromApiKeySecret {
  status: $Enums.KeyStatus;
  expiresAt: Date;
  Company: {
      id: string;
      applications: {
          id: string;
          type: $Enums.ApplicationType;
      }[];
  };
}

type Role = 'user' | 'agent' | 'guest' | 'anonymous'

interface BasePayload {
  id: string;
  isAuthenticated: boolean;
  type: Role;
  iat: number;
  exp: number;
}

interface UserPayload extends BasePayload {
  type: 'user';
  email: string;
}

interface GuestPayload extends BasePayload {
  type: 'guest';
  companyId: string;
}

interface AnonymousPayload extends BasePayload {
  type: 'anonymous';
  companyId: string;
}

interface AgentPayload extends BasePayload {
  type: 'agent';
  companyId: string;
}

export type AuthPayload = UserPayload | GuestPayload | AnonymousPayload | AgentPayload;


const SECRET: Secret = process.env.JWT_SECRET;

export class AuthService {
  
  
  /**
   * Generates a JSON Web Token (JWT) with the provided data and expiration time.
   *
   * @template T - The type of the data to be included in the token.
   * @param {T} data - The data to be included in the token payload.
   * @param {string} [expiresIn="7d"] - The expiration time for the token. Defaults to 7 days.
   * @returns {string} The generated JWT as a string.
   */
  static generateToken<T = any>(data: T, expiresIn: string = "7d"): string {
        return jwt.sign({ data }, SECRET, { expiresIn, algorithm: "HS256" });
   }

  /**
   * Hashes a given password using bcrypt.
   *
   * @param password - The plain text password to be hashed.
   * @returns A promise that resolves to the hashed password.
   */
  static async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  /**
   * Compares a plain text password with a hashed password.
   *
   * @param password - The plain text password to compare.
   * @param hashedPassword - The hashed password to compare against.
   * @returns A promise that resolves to a boolean indicating whether the passwords match.
   */
  static comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  
  /**
   * Verifies a JWT token and optionally includes the token data.
   *
   * @param token - The JWT token to verify.
   * @returns Decoded token with role-based typing.
   * @throws ApiError if token is invalid.
   */
  static verifyToken<T extends AuthPayload = AuthPayload>(
    token: string,
  ): { data: T } {
    try {
      const decoded = jwt.verify(token, SECRET) as { data: T };

      if (decoded.data.type !== 'user' && decoded.data.type !== 'guest') {
        throw new ApiError("Authentication error", 403, "AuthService.verifyToken");
      }

      return decoded;
    } catch (err) {
      throw new ApiError("Authentication error: Invalid token", 401, "AuthService.verifyToken");
    }
  }

   

  /**
   * Extracts the token from the authorization header.
   *
   * @param {string} authorizationHeader - The authorization header value.
   * @returns {string} The extracted token.
   */
  static extractToken(authorizationHeader: string): string | null {
    if (!authorizationHeader || !authorizationHeader.includes(' ')) return null;
    const [scheme, token] = authorizationHeader.split(' ');
    return scheme === 'Bearer' ? token : null;
  }

/**
 * Extracts the token from a cookie by its name.
 *
 * @param {string} cookie - The cookie string (e.g., "token=abc123; otherCookie=xyz").
 * @param {string} name - The name of the cookie to extract (e.g., "token").
 * @returns {string | null} - The extracted token, or `null` if the cookie or token is not found.
 */
  static extractTokenFromCookie(cookie: string, name: string): string | null {
    if (!cookie || !name) return null;

    return cookie
      .split(';')
      .map(part => part.trim())
      .find(part => part.startsWith(`${name}=`)) 
      ?.split("=")[1] 
      ?.trim() 
      || null; 
  }

  /**
   * @returns {string} - A randomly generated 64-character key.
   */
  static generateKey(): string {
    return crypto.randomBytes(32).toString('hex'); // Generates 64-character random key
  }

  /**
   * Hashes a given key using SHA-256.
   * @param {string} key - The key to hash.
   * @returns {string} - The hashed key.
   */
  static hashKey(key: string): string {
    return crypto.createHash('sha256').update(key).digest('hex');
  }

  static async getCompanyDomainsByApiKey(apiKey: string): Promise<ICompanyDomains | null> {
    const result = await prisma.accessKey.findUnique({
      where: { apiKey },
      select: { 
        Company: {
          select: {
            id: true,
            domains: true,
          },
        }
      },
    });
    if (!result || !result.Company) return null;
    const { id: companyId, domains } = result.Company;
    return { companyId, domains };
  }
  
  /**
   * Middleware to handle CORS (Cross-Origin Resource Sharing) for API requests.
   *
   * @param {Request} req - The Express request object.
   * @param {Response} res - The Express response object.
   * @param {NextFunction} next - The next middleware function.
   */
  static async corsMiddleware(req: Request, res: Response, next: NextFunction) {
    const origin = req.headers.origin || '';
    
    // Skip if no Origin header (non-browser client)
    if (!origin) return next();

    // Main request — validate origin
    if(req.method !== 'OPTIONS') {
      const apiKey = req.query.apiKey?.toString() || req.headers['x-api-key']?.toString() || req.headers['X-API-Key']?.toString();
      console.log('**************apiKey**************\n', apiKey, '\n');
      if (!apiKey) return res.status(403).json({ message: 'Missing API key'});
      try {
        const isAllowed = await this.verifyOrigin(origin, apiKey);
        if (!isAllowed) return res.status(403).json({ message: 'CORS origin not allowed'});
  
        res.setHeader('Access-Control-Allow-Origin', origin);
        res.setHeader('Access-Control-Allow-Credentials', 'true');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        next();
      }  catch (err) {
        console.log(err);
        return res.status(500).json({ message: 'CORS check failed'});
      }
    } else {
      // Preflight request — assume preflight already validated origin
      console.log('***************Start-Preflight**********************');
      console.log(req.headers);
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-API-Key');
      //res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
       res.setHeader('Access-Control-Max-Age', '86400');
      res.setHeader('Access-Control-Allow-Credentials', 'true');
      console.log('***************End-Preflight**********************');
      return res.status(204).send();
      // next();
    }
  }

  
  
  /**
   * Checks if the origin is allowed based on the provided domains and access key type.
   *
   * @param {string} origin - The origin to check.
   * @param {string[]} domains - The allowed domains.
   * @returns {boolean} - `true` if the origin is allowed, `false` otherwise.
   */
  static checkOrigin(origin: string, domains: string[]): boolean {
    try {
      if (domains.includes('*')) return true;
      const hostname = new URL(origin).hostname;
      
      // match full origin for production - will have to later defined accesskey type
      // - (development) or (production) so as to safely used hostname.endsWith in dev(due to random preview link) or * but not in company's production environment.
      if (domains.includes(origin)) {
        return true; // match full origin for production
      }
  
      // allow suffix match for dev (e.g. *.vercel.app)
      return domains.some(domain => hostname.endsWith(domain));
    } catch {
      return false;
    }
  }

  /**
   * Verifies if the provided origin is allowed based on the API key.
   *
   * @param {string} origin - The origin to verify.
   * @param {string} apiKey - The API key to check against.
   * @returns {Promise<boolean>} - A promise that resolves to `true` if the origin is allowed, `false` otherwise.
   */
  static async verifyOrigin(origin: string, apiKey: string): Promise<boolean> {
    try {
      const domainsData = await this.getCompanyDomainsByApiKey(apiKey);
      if (!domainsData) return false;
  
      const { domains } = domainsData;
  
      return this.checkOrigin(origin, domains);
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  
  static async getCompanyAndAppsFromApiKeySecret(apiKey: string, apiKeySecret: string): Promise<ICompanyAndApplicationFromApiKeySecret | null> {
    const hashSecretKey = this.hashKey(apiKeySecret);
    const result = await prisma.accessKey.findUnique({
      where: { apiKeySecret: hashSecretKey },
      select: { 
        apiKey: true,
        status: true,
        expiresAt: true,
        Company: {
          select: {
            id: true,
            applications: {
              select: {
                id: true,
                type: true,
              },
            }
          },
        }
      },
    });

    if(apiKey !== result?.apiKey || result.status !== 'Active') {
      console.log('Invalid api key or secret');
      return null;
    }
    return result;
  }

  static async getAppTypeFromApiKeySecret(apiKey: string, apiKeySecret: string, type: $Enums.ApplicationType): Promise<{id: string; type: $Enums.ApplicationType, companyId: string} | null> {
    const result = await this.getCompanyAndAppsFromApiKeySecret(apiKey, apiKeySecret);
    if (!result) return null;
    const app = result.Company.applications.find(app => app.type === type);
    return app ? {...app, companyId: result.Company.id} : null;
  }
  

  static async AuthenticationMiddleware(request: Request, response: Response, next: NextFunction): Promise<void> {
    try {
      const token = this.extractToken(request.headers.authorization);
      if (!token) {
        return next(new ApiError("Unauthenticated: Please login", 401));
      }
  
      const { data: payload } = this.verifyToken<AuthPayload>(token);
  
      if (!payload.isAuthenticated) {
        return next(new ApiError("Unauthenticated: Please login", 401));
      }
  
      request.user = payload;
      return next();
    } catch (error) {
      return next(new ApiError("Unauthenticated: Please login", 401));
    }
  }
}

