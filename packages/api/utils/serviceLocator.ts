import { AwilixContainer } from 'awilix';
import * as Services from '../v1/services';

let containerInstance: AwilixContainer<any> | null = null;

// Lazy initialization of container
const getContainer = async (): Promise<AwilixContainer<any>> => {
  if (!containerInstance) {
    const { container } = await import('../v1/container');
    containerInstance = container;
  }
  return containerInstance;
};

// Service locator class
export class ServiceLocator {
  private static instance: ServiceLocator;
  private container: AwilixContainer<any> | null = null;

  private constructor() {}

  public static getInstance(): ServiceLocator {
    if (!ServiceLocator.instance) {
      ServiceLocator.instance = new ServiceLocator();
    }
    return ServiceLocator.instance;
  }

  private async ensureContainer(): Promise<void> {
    if (!this.container) {
      this.container = await getContainer();
    }
  }

  // Generic service resolver
  public async getService<T>(serviceName: string): Promise<T> {
    await this.ensureContainer();
    return this.container!.resolve<T>(serviceName);
  }

  // Specific service getters for better type safety
  public async getApplicationService(): Promise<Services.ApplicationService> {
    return this.getService<Services.ApplicationService>('applicationService');
  }

  public async getGuestService(): Promise<Services.GuestService> {
    return this.getService<Services.GuestService>('guestService');
  }

  public async getRoomService(): Promise<Services.RoomService> {
    return this.getService<Services.RoomService>('roomService');
  }

  public async getMessageService(): Promise<Services.MessageService> {
    return this.getService<Services.MessageService>('messageService');
  }

  public async getCloudStorageProviderService(): Promise<Services.CloudStorageProviderService> {
    return this.getService<Services.CloudStorageProviderService>('cloudStorageProviderService');
  }

  public async getUserService(): Promise<Services.UserService> {
    return this.getService<Services.UserService>('userService');
  }

  public async getCompanyService(): Promise<Services.CompanyService> {
    return this.getService<Services.CompanyService>('companyService');
  }

  public async getAccountService(): Promise<Services.AccountService> {
    return this.getService<Services.AccountService>('accountService');
  }

  public async getFileService(): Promise<Services.FileService> {
    return this.getService<Services.FileService>('fileService');
  }

  // Batch service getter for socket handlers
  public async getSocketServices() {
    await this.ensureContainer();
    return {
      applicationService: this.container!.resolve<Services.ApplicationService>('applicationService'),
      guestService: this.container!.resolve<Services.GuestService>('guestService'),
      roomService: this.container!.resolve<Services.RoomService>('roomService'),
      messageService: this.container!.resolve<Services.MessageService>('messageService')
    };
  }

  // For testing - allows injecting mock container
  public setContainer(container: AwilixContainer<any>): void {
    this.container = container;
  }

  // Reset for testing
  public reset(): void {
    this.container = null;
    containerInstance = null;
  }
}

// Convenience export
export const serviceLocator = ServiceLocator.getInstance();
