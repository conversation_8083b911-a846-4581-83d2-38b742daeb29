

export class ApiError extends Error {
    statusCode: number;
    success: boolean;
    from: string
  
    constructor(message: string, statusCode: number = 500, from: string = '',  name : string = 'ApiError') {
      super(message);
      this.statusCode = statusCode;
      this.name = name;
      this.from = from;
      this.success = false;
      Error.captureStackTrace(this, this.constructor);
    }
  }
  
