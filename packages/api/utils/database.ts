import { z, ZodSchema, ZodIssue } from "zod";
import { PrismaClient, Prisma } from "@prisma/client";
import { prisma } from "./prismaClient";
import { getLogger } from "./logger";
import { ObjectId } from "mongodb";

/**
 * Represents the names of Prisma models, excluding internal Prisma methods and symbols.
 */
type PrismaModels = keyof Omit<PrismaClient, `$${string}` | symbol>;

/**
 * Utility types to infer the return types of Prisma operations.
 */
type CreateReturnType<K extends PrismaModels> = PrismaClient[K] extends { create: (...args: any) => infer R } ? Awaited<R> : never;
type UpdateReturnType<K extends PrismaModels> = PrismaClient[K] extends { update: (...args: any) => infer R } ? Awaited<R> : never;
type DeleteReturnType<K extends PrismaModels> = PrismaClient[K] extends { delete: (...args: any) => infer R } ? Awaited<R> : never;
type FindManyReturnType<K extends PrismaModels> = PrismaClient[K] extends { findMany: (...args: any) => infer R } ? Awaited<R> : never;
type FindUniqueReturnType<K extends PrismaModels> = PrismaClient[K] extends { findUnique: (...args: any) => infer R } ? Awaited<R> : never;
type FindFirstReturnType<K extends PrismaModels> = PrismaClient[K] extends { findFirst: (...args: any) => infer R } ? Awaited<R> : never;
type CountReturnType<K extends PrismaModels> = PrismaClient[K] extends { count: (...args: any) => infer R } ? Awaited<R> : never;

/**
 * Maps query types to their corresponding return types.
 */
type QueryReturnType<K extends PrismaModels, QueryType> =
  QueryType extends 'findMany' ? FindManyReturnType<K> :
  QueryType extends 'findUnique' ? FindUniqueReturnType<K> :
  QueryType extends 'findFirst' ? FindFirstReturnType<K> :
  QueryType extends 'count' ? CountReturnType<K> :
  QueryType extends 'delete' ? DeleteReturnType<K> :
  QueryType extends 'update' ? UpdateReturnType<K> :
  never;

/**
 * Interface for database method parameters.
 * @template T - The Prisma model type.
 */
export interface IDatabaseMethodParams<T extends PrismaModels> {
    model: T;
    where?: Prisma.Args<PrismaClient[T], "findMany">["where"];
    data?: Prisma.Args<PrismaClient[T], "create">["data"] | Prisma.Args<PrismaClient[T], "update">["data"];
    select?: Prisma.Args<PrismaClient[T], "findMany">["select"];
    include?: Prisma.Args<PrismaClient[T], "findMany"> extends { include?: infer I } ? I : never;
    orderBy?: Prisma.Args<PrismaClient[T], "findMany">["orderBy"];
    skip?: number;
    take?: number;
    cursor?: Prisma.Args<PrismaClient[T], "findMany">["cursor"];
    distinct?: Prisma.Args<PrismaClient[T], "findMany">["distinct"];
}

/**
 * Interface for a generic response object.
 * @template T - The type of the data field.
 */
export interface IResponse<T> {
    data?: T;
    success: boolean;
    message: string;
    statusCode: number;
}

/**
 * Logger instance for database operations.
 */
export const DBLogger = getLogger("database");

/**
 * Abstract class representing a generic database service.
 * 
 * @template T - The type of the Prisma model.
 */
export abstract class Database<T extends Prisma.ModelName>  {
    // Predefined response parameters for various HTTP status codes.
    public notFoundParams = { success: false, message: "Not Found", statusCode: 404 };
    public forbiddenParams = { success: false, statusCode: 403 };
    public successParams = { success: true, message: "Retrieved Successfully", statusCode: 200 };
    public unAuthorizedParams = { success: false, message: "Unauthorized", statusCode: 401 };
    public createdParams = { success: true, message: "Created Successfully", statusCode: 201 };
    public updatedParams = { success: true, message: "Updated Successfully", statusCode: 200 };
    public deletedParams = { success: true, message: "Deleted Successfully", statusCode: 200 };
    public badRequestParams = { success: false, message: "Bad Request", statusCode: 400 };
    public conflictParams = { success: false, message: "Conflict", statusCode: 409 };
    public noContentParams = { success: true, message: "No Content", statusCode: 204 };
    public notImplementedParams = { success: false, message: "Not Implemented", statusCode: 501 };
    public notAcceptableParams = { success: false, message: "Not Acceptable", statusCode: 406 };
    public unsupportedMediaTypeParams = { success: false, message: "Unsupported Media Type", statusCode: 415 };
    public internalServerErrorParams = { success: false, message: "Internal Server Error", statusCode: 500 };
    public serviceUnavailableParams = { success: false, message: "Service Unavailable", statusCode: 503 };
    public gatewayTimeoutParams = { success: false, message: "Gateway Timeout", statusCode: 504 };

    /**
     * The Prisma client instance.
     */
    protected prisma: PrismaClient;

    /**
     * Abstract schema for creating a new record (must be implemented by child classes).
     */
    abstract createSchema: ZodSchema<Prisma.Args<T, "create">["data"]>;

    /**
     * Abstract schema for updating a record (must be implemented by child classes).
     */
    abstract updateSchema: ZodSchema<Prisma.Args<T, "update">["data"]>;

    /**
     * Schema for validating MongoDB ObjectIds.
     */
    idSchema = z
        .string()
        .trim()
        .refine((value) => /^[a-fA-F0-9]{24}$/.test(value) && ObjectId.isValid(value), {
            message: "Invalid Id",
        });

    constructor() {
        this.prisma = prisma;
    }

    /**
     * Formats the response and logs the appropriate message based on the status code.
     *
     * @template T - The type of the response data.
     * @param {IResponse<T>} response - The response object to format and log.
     * @param {boolean} [log=false] - Whether to log the response.
     * @returns {IResponse<T>} - The formatted response object.
     */
    public formatResponse<T = null>(response: IResponse<T>, log: boolean = false): IResponse<T> {
        if (log) {
            if (response.statusCode >= 400) {
                DBLogger.error(`Response error: ${response.message}`);
            } else if (response.statusCode >= 300) {
                DBLogger.warn(`Response redirect: ${response.message}`);
            } else {
                DBLogger.info(`Response success: ${response.message}`);
            }
        }
        return response;
    }

    /**
     * Validates the provided data against the create schema.
     *
     * @param {any} data - The data to be validated.
     * @returns {any} - The validated data.
     * @throws {Error} - Throws an error if validation fails.
     */
    protected validateCreate(data: any): any {
        try {
            const result = this.createSchema.safeParse(data);
            if (!result.success) {
                throw new Error(`Validation failed: ${result.error.errors.map((e: ZodIssue) => e.path + " : " + e.message).join(", ")}`);
            }
            return result.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Validates the data for updating a record using the defined schema.
     *
     * @param {any} data - The data to be validated.
     * @returns {any} - The validated data.
     * @throws {Error} - Throws an error if validation fails.
     */
    protected validateUpdate(data: any): any {
        try {
            const result = this.updateSchema.safeParse(data);
            if (!result.success) {
                throw new Error(`Validation failed: ${result.error.errors.map((e: ZodIssue) => e.path + " : " + e.message).join(", ")}`);
            }
            return result.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Validates the provided ID string against the defined schema.
     *
     * @param {string} id - The ID string to be validated.
     * @returns {string} - The validated ID string.
     * @throws {Error} - Throws an error if validation fails.
     */
    protected validateId(id: string): string {
        try {
            const result = this.idSchema.safeParse(id);
            if (!result.success) {
                throw new Error(`Validation failed: ${result.error.errors.map((e: ZodIssue) => e.path + " : " + e.message).join(", ")}`);
            }
            return result.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Validates the provided data using custom logic.
     *
     * @param {any} data - The data to be validated.
     * @returns {any} - The validated data.
     */
    validateCustom(data: any): any {
        return data;
    }

    /**
     * Creates a new record in the database.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the create operation.
     * @returns {Promise<CreateReturnType<K>>} - A promise that resolves to the created record.
     * @throws {Error} - Throws an error if data is not provided or the model does not support the create operation.
     */
    protected async create<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<CreateReturnType<K>> {
        const { model, data, ...rest } = params;

        if (!data) throw new Error("Data is required for create operation");

        const modelClient = this.prisma[model] as unknown as { create: (args: any) => Promise<CreateReturnType<K>> };

        if (typeof modelClient.create !== "function") {
            throw new Error(`Model ${String(model)} does not support the create operation`);
        }

        const result = await modelClient.create({
            data,
            ...rest,
        });

        return result;
    }

    /**
     * Updates a record in the database.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the update operation.
     * @returns {Promise<UpdateReturnType<K>>} - A promise that resolves to the updated record.
     * @throws {Error} - Throws an error if data or where condition is not provided, or if the model does not support the update operation.
     */
    protected async update<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<UpdateReturnType<K>> {
        const { model, where, data, ...rest } = params;

        if (!data) throw new Error("Data is required for update operation");
        if (!where) throw new Error("Unable to update record without an identifier");

        const modelClient = this.prisma[model] as unknown as { update: (args: any) => Promise<UpdateReturnType<K>> };

        if (typeof modelClient.update !== "function") {
            throw new Error(`Model ${String(model)} does not support the update operation`);
        }

        const result = await modelClient.update({
            where,
            data,
            ...rest,
        });

        return result;
    }

    /**
     * Executes a database query based on the provided query type.
     *
     * @template K - The type of the Prisma model.
     * @template QueryType - The type of the query (e.g., 'findMany', 'findUnique', etc.).
     * @param {QueryType} queryType - The type of query to execute.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the query.
     * @returns {Promise<QueryReturnType<K, QueryType>>} - A promise that resolves to the query result.
     * @throws {Error} - Throws an error if the query type is invalid or the model does not support the operation.
     */
    protected async executeQuery<
        K extends PrismaModels,
        QueryType extends 'findMany' | 'findUnique' | 'findFirst' | 'count' | 'delete' | 'update' 
    >(
        queryType: QueryType,
        params: IDatabaseMethodParams<K>
    ): Promise<QueryReturnType<K, QueryType>> {
        const { model, where, ...rest } = params;

        if (queryType !== 'findMany' && !where) {
            throw new Error(`Unable to perform ${queryType} operation without an identifier`);
        }

        const modelClient = this.prisma[model] as unknown as Record<QueryType, (args: any) => Promise<QueryReturnType<K, QueryType>>>;

        if (!modelClient || !(queryType in modelClient)) {
            throw new Error(`Invalid operation "${queryType}" on model "${model}"`);
        }

        const result = await modelClient[queryType]({ where, ...rest });
        return result;
    }

    /**
     * Finds multiple records in the database.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the findMany operation.
     * @returns {Promise<FindManyReturnType<K>>} - A promise that resolves to an array of records.
     */
    protected async findMany<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<FindManyReturnType<K> | []> {
        return await this.executeQuery<K, 'findMany'>('findMany', params);
    }

    /**
     * Finds a unique record in the database.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the findUnique operation.
     * @returns {Promise<FindUniqueReturnType<K>>} - A promise that resolves to the unique record.
     */
    protected async findUnique<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<FindUniqueReturnType<K> | null> {
        return this.executeQuery<K, 'findUnique'>('findUnique', params);
    }

    /**
     * Finds the first record in the database that matches the criteria.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the findFirst operation.
     * @returns {Promise<FindFirstReturnType<K>>} - A promise that resolves to the first matching record.
     */
    protected async findFirst<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<FindFirstReturnType<K> | null> {
        return this.executeQuery<K, 'findFirst'>('findFirst', params);
    }

    /**
     * Counts the number of records that match the criteria.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the count operation.
     * @returns {Promise<CountReturnType<K>>} - A promise that resolves to the count of matching records.
     */
    protected async count<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<CountReturnType<K> | number> {
        return this.executeQuery<K, 'count'>('count', params);
    }

    /**
     * Deletes a record from the database.
     *
     * @template K - The type of the Prisma model.
     * @param {IDatabaseMethodParams<K>} params - The parameters for the delete operation.
     * @returns {Promise<DeleteReturnType<K>>} - A promise that resolves to the deleted record.
     */
    protected async delete<K extends PrismaModels>(params: IDatabaseMethodParams<K>): Promise<DeleteReturnType<K> | null> {
        return this.executeQuery<K, 'delete'>('delete', params);
    }
}
