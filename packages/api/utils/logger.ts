import * as winston from "winston";
import { LoggerOptions, Logger } from "winston";
import * as fs from "fs";
import * as path from "path";

function ensureLogDirectory(pathname: string) {
  const logDir = path.join("logs", pathname);
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
}

function createLoggerOptions(pathname: string = "generals"): LoggerOptions {
    ensureLogDirectory(pathname);

    const isProduction = process.env.NODE_ENV === "production";
    // set the log level based on the environment
    return {
      level: isProduction ? "warn" : "debug",
      format: winston.format.combine(
        winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
        isProduction ? winston.format.json() : winston.format.prettyPrint()
      ),
      transports: [
        new winston.transports.Console(),
        ...(isProduction
          ? [
              new winston.transports.File({ filename: `logs/${pathname}/error.log`, level: "error" }),
              new winston.transports.File({ filename: `logs/${pathname}/combined.log` }),
            ]
          : []),
      ],
    };
};



// standalone logger instance can be used for logging instead of creating a new instance and using it.
export function getLogger(pathname: string): Logger {
    return winston.createLogger(createLoggerOptions(pathname));
};

