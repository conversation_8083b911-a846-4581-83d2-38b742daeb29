import { Request, Response, NextFunction } from 'express';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Storage } from '@google-cloud/storage';
import { BlobSASPermissions, BlobServiceClient } from '@azure/storage-blob';
import { ApiError } from '.';
import { getLogger } from './logger';
import { serviceLocator } from './serviceLocator';


const logger = getLogger('fileViewer');

// File types to stream (sensitive)
const SENSITIVE_FILE_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'text/plain',
];

// File types to use presigned URLs (less-sensitive)
const PRESIGNED_FILE_TYPES = ['image/jpeg', 'image/png'];

export const FileViewer = async (req: Request, res: Response, next: NextFunction) => {
  const fileId  = req.params.fileId;

  try {
    // Use service locator to get the service
    const cloudStorageProviderService = await serviceLocator.getCloudStorageProviderService();
    const result = await cloudStorageProviderService.getFileCloudStorageProvider(fileId);
    const fileNotFound = !result.success && result.message.includes('Not Found');
    const fileProviderNotSet = !result.success && result.message.includes('Not Available');
    const fileProviderNotActive = !result.success && result.message.includes('not active')

    if(fileNotFound || fileProviderNotActive) {
      throw new ApiError(result.message, 404, 'FileViewer');
    }

    // this means the file was uploaded using default aws s3 i.e sparkStrandChat s3 bucket
    const provider = fileProviderNotSet ? null : result.data.provider;
    const file = result.data.file;

    // Set response timeout (100 seconds for streaming)
    res.setTimeout(100000, () => {
      logger.warn(`Streaming timeout for file ${fileId}`);
      res.status(504).json({ message: 'Request timed out while streaming file' });
    });

    // Determine approach based on file type
    const useStreaming = SENSITIVE_FILE_TYPES.includes(file.fileType || '');
    const usePresigned = PRESIGNED_FILE_TYPES.includes(file.fileType || '');

    if (!useStreaming && !usePresigned) {
      logger.warn(`Unsupported file type for file ${fileId}: ${file?.fileType}`);
      throw new ApiError('Unsupported file type', 400, 'FileViewer');
    }

    // Fallback to default AWS S3 if no provider, since that was the default s3 used to save in first place, check clout.ts.
    if (!provider || !provider.isActive) {
      const s3Client = new S3Client({
        region: process.env.AWS_REGION,
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      });

      if (useStreaming) {
        res.setHeader('Content-Type', file.fileType || 'application/octet-stream');
        res.setHeader('Content-Disposition', `inline; filename="${file.filename}"`);
        res.setHeader('Content-Length', file.size.toString());
        res.setHeader('Cache-Control', 'max-age=31536000');
        res.setHeader('Expires', new Date(Date.now() + 31536000000).toUTCString());

        const command = new GetObjectCommand({
          Bucket: process.env.AWS_S3_BUCKET_NAME!,
          Key: file.key,
        });

        const { Body } = await s3Client.send(command);
        if (!Body) {
          throw new ApiError('File content not found', 404, 'FileViewer');
        }

        (Body as any).pipe(res);
      } else {
        const url = await getSignedUrl(s3Client, new GetObjectCommand({
          Bucket: process.env.AWS_S3_BUCKET_NAME!,
          Key: file.key,
        }), { expiresIn: 24 * 60 * 60 }); // 1 day
        res.redirect(307, url);
      }
      return;
    }

    switch (provider.provider) {
      case 'AWS':
        const s3Client = new S3Client({
          region: provider.region,
          credentials: {
            accessKeyId: provider.keyId!,
            secretAccessKey: provider.secretKey!,
          },
        });

        if (useStreaming) {
          res.setHeader('Content-Type', file.fileType || 'application/octet-stream');
          res.setHeader('Content-Disposition', `inline; filename="${file.filename}"`);
          res.setHeader('Content-Length', file.size.toString());

          const command = new GetObjectCommand({
            Bucket: provider.bucketName!,
            Key: file.key,
          });

          const { Body } = await s3Client.send(command);
          if (!Body) {
            throw new ApiError('File content not found', 404, 'FileViewer');
          }

          (Body as any).pipe(res);
        } else {
          const url = await getSignedUrl(s3Client, new GetObjectCommand({
            Bucket: provider.bucketName!,
            Key: file.key,
          }), { expiresIn: 24 * 60 * 60 });
          res.redirect(307, url);
        }
        break;

      case 'GCP':
        const gcpStorage = new Storage({
          projectId: provider.projectId,
          credentials: JSON.parse(provider.credentialsJson!),
        });
        const bucket = gcpStorage.bucket(provider.bucketName!);
        const gcpFile = bucket.file(file.key);

        if (useStreaming) {
          res.setHeader('Content-Type', file.fileType || 'application/octet-stream');
          res.setHeader('Content-Disposition', `inline; filename="${file.filename}"`);
          res.setHeader('Content-Length', file.size.toString());

          const readStream = gcpFile.createReadStream();
          readStream.on('error', (err) => {
            logger.error(`GCP streaming error for file ${fileId}: ${err.message}`);
            res.status(500).json({ message: 'Error streaming file' });
          });
          readStream.pipe(res);
        } else {
          const [url] = await gcpFile.getSignedUrl({
            action: 'read',
            expires: Date.now() + 24 * 60 * 60 * 1000, //  1 day
          });
          res.redirect(307, url);
        }
        break;

      case 'AZURE':
        const azureBlobClient = BlobServiceClient.fromConnectionString(provider.connectionString!);
        const containerClient = azureBlobClient.getContainerClient(provider.containerName!);
        const blobClient = containerClient.getBlobClient(file.key);

        if (useStreaming) {
          res.setHeader('Content-Type', file.fileType || 'application/octet-stream');
          res.setHeader('Content-Disposition', `inline; filename="${file.filename}"`);
          res.setHeader('Content-Length', file.size.toString());

          const downloadResponse = await blobClient.download();
          if (!downloadResponse.readableStreamBody) {
            throw new ApiError('File content not found', 404, 'FileViewer');
          }

          downloadResponse.readableStreamBody.pipe(res);
        } else {
          const url = await blobClient.generateSasUrl({
            startsOn: new Date(),
            expiresOn: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day
            permissions: BlobSASPermissions.parse('r'),
          });
          res.redirect(307, url);
        }
        break;

      default:
        throw new ApiError('Unsupported cloud provider', 400, 'FileViewer');
    }
  } catch (err: any) {
    logger.error(`Error processing file ${fileId}: ${err.message}`);
    if (err instanceof ApiError) {
      throw err;
    }
    throw new ApiError('Internal Server Error', 500, 'FileViewer');
  }
};
