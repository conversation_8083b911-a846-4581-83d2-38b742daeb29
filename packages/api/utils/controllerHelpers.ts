import { Request, Response } from 'express';
import { z, ZodIssue } from "zod";
import { ObjectId } from 'mongodb';
import { ApiError } from "./error";
import { IResponse } from './database';
import { AuthPayload } from './auth';

export interface UserRequestPayload { id: string, email: string };

export class ControllerHelper {

    public validateRequestBody<T>(request: Request, controllerName?: string): T {
        if (!request?.body || Object.keys(request.body).length === 0) {
            throw new ApiError("Invalid request body", 400, controllerName || "Controller Error");
        }
        return request.body as T;
    }

    public validateQueryParams<T extends Record<string, unknown>>(query: T, requiredParams: Array<keyof T>, controllerName?: string): void {
        for (const param of requiredParams) {
            if (!query[param]) {
                throw new ApiError(`Missing required query parameter: ${String(param)}`, 400, controllerName || "Controller Error");
            }
        }
    };

    public validateUser(request: Request, controllerName?: string): any {
        if (!request?.user) {
            throw new ApiError("Unauthorized", 401, controllerName || "Controller Error");
        }
        return request.user as AuthPayload;
    }

    public validateParameterId(id: any, controllerName?: string): string{
        const idSchema = z.string().trim().refine((value) => {
            return /^[a-fA-F0-9]{24}$/.test(value) && ObjectId.isValid(value);
            }, {
                message: "Invalid Id",
            });
        try {
            const result = idSchema.safeParse(id);
            if (!result.success) {
                throw new ApiError(
                    `Invalid Parameter Id: ${result.error.errors.map((e: ZodIssue) => e.path + " : " + e.message).join(", ")}`,
                    400,
                    controllerName || 'Controller Error'
                );
            }
            return result.data;
            } catch (error) {
                throw error;
            }
    }

    public handleServiceResult<T>(result: IResponse<T>, response: Response, controllerName?: string): void {
        if (!result.success) {
            throw new ApiError(result.message, result.statusCode, controllerName || "Controller Error");
        }
        response.status(result.statusCode).json(result);
    }

    public handleServiceResultWithCookie<T extends { token: string, data: any}>(result: IResponse<T>, response: Response, isSDk: boolean = false, controllerName?: string): void {
        if (!result.success) {
            throw new ApiError(result.message, result.statusCode, controllerName || "Controller Error");
        }
    
        this.clearCookie(response, isSDk);

        const token: string | undefined = result.data?.token;

        if(!token){
            throw new ApiError("Fail to set cookie", 500, controllerName || "Controller Error")
        }

        result.data = result.data.data;
        const sevenDaysFromNow = new Date();
        sevenDaysFromNow.setDate(sevenDaysFromNow.getDate() + 7);

        const cookieName = isSDk ? 'sparkstrand_token' : 'token';

        response.cookie(cookieName, token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' && isSDk ? 'none' : 'lax', 
            expires: sevenDaysFromNow,
        });
        response.status(result.statusCode).json(result);
    }

    public clearCookie(response: Response, isSDk: boolean = false): void {
        const cookieName = isSDk ? 'sparkstrand_token' : 'token';

        if (response.req?.cookies && response.req.cookies?.[cookieName]) {
            response.clearCookie(cookieName, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' && isSDk ? 'none' : 'lax', 
            });
        };
    }
}
