import multer, { StorageEngine } from 'multer';
import multerS3 from 'multer-s3';
import { S3Client } from '@aws-sdk/client-s3';
import { Storage, } from '@google-cloud/storage';
import { BlobServiceClient } from '@azure/storage-blob';
import { v4 as uuidv4 } from 'uuid';
import { CloudStorageProviderService } from '../v1/services';
import { Request, Response, NextFunction } from 'express';
import { getLogger } from './logger';
import { ICloudStorageProvider } from '../models';


const logger = getLogger('file-upload');

const ALLOW_TYPE = [
          'image/jpeg',
          'image/png',
          'application/pdf',
          'text/plain',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
] ;

// Initialize default AWS S3 client (fallback)
const s3Client = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Custom GCP Storage Engine
class GCPStorageEngine implements StorageEngine {
  private bucket: any;
  private companyName: string;
  private userId: string;

  constructor(bucket: any, companyName?: string, userId?: string) {
    this.bucket = bucket;
    this.companyName = companyName || 'sparkstrandChat';
    this.userId = userId || 'unknown';
  }

  async _handleFile(req: any, file: Express.Multer.File, cb: (error?: any, info?: Partial<Express.Multer.File>) => void) {
    try {
      const fileName = `uploads/${this.companyName}/${this.userId}/media/${uuidv4()}-${file.originalname}`;
      const blob = this.bucket.file(fileName);
      const blobStream = blob.createWriteStream({ resumable: false });

      blobStream.on('error', (err: any) => cb(err));
      blobStream.on('finish', () => {
        const publicUrl = `https://storage.googleapis.com/${this.bucket.name}/${fileName}`;
        cb(null, { path: publicUrl, filename: fileName });
      });

      file.stream.pipe(blobStream);
    } catch (err) {
      cb(err);
    }
  }

  async _removeFile(_req: Request, file: Express.Multer.File, cb: (error: Error | null) => void) {
    try {
      await this.bucket.file(file.filename).delete();
      cb(null);
    } catch (err) {
      cb(err as Error);
    }
  }
}

// Custom Azure Storage Engine
class AzureStorageEngine implements StorageEngine {
  private containerClient: any;
  private companyName: string;
  private userId: string;

  constructor(containerClient: any, companyName?: string, userId?: string) {
    this.containerClient = containerClient;
    this.companyName = companyName || 'sparkstrandChat';
    this.userId = userId || 'unknown';
  }

  async _handleFile(req: any, file: Express.Multer.File, cb: (error?: any, info?: Partial<Express.Multer.File>) => void) {
    try {
      const fileName = `uploads/${this.companyName}/${this.userId}/media/${uuidv4()}-${file.originalname}`;
      const blobClient = this.containerClient.getBlockBlobClient(fileName);

      await blobClient.uploadStream(file.stream, undefined, undefined, {
        blobHTTPHeaders: { blobContentType: file.mimetype },
      });

      cb(null, { path: blobClient.url, filename: fileName });
    } catch (err) {
      cb(err);
    }
  }

  async _removeFile(_req: Request, file: Express.Multer.File, cb: (error: Error | null) => void) {
    try {
      const blobClient = this.containerClient.getBlockBlobClient(file.filename);
      await blobClient.delete();
      cb(null);
    } catch (err) {
      cb(err as Error);
    }
  }
}

/**
 * Helper function to get the multer storage configuration based on the user's company's cloud storage provider.
 * If the user cloud storage provider is not found, it will fallback to the default AWS S3 provided by the server.
 * @param req - The Express request object.
 */
const getMulterStorage = async (provider: ICloudStorageProvider | null, userType: string, userId: string): Promise<StorageEngine> => {


  // Determine if user is a guest
  const isGuest = userType === 'guest';

  // Fallback to default AWS S3 for non-guests or if no provider is found for a company
  if (!provider || !isGuest) {
    return multerS3({
      s3: s3Client as any,
      bucket: process.env.AWS_S3_BUCKET_NAME!,
      // acl: 'public-read',
      contentType: multerS3.AUTO_CONTENT_TYPE,
      key: (req, file, cb) => {
        const fileName = `uploads/sparkstrandChat/${userId}/media/${uuidv4()}-${file.originalname}`;
        cb(null, fileName);
      },
    });
  }

  try {
    const name = 'sparkStrandChat';
    switch (provider.provider) {
      case 'AWS':
        const s3 = new S3Client({
          region: provider.region,
          credentials: {
            accessKeyId: provider.keyId!,
            secretAccessKey: provider.secretKey!,
          },
        });
        return multerS3({
          s3: s3 as any,
          bucket: provider.bucketName!,
          // acl: 'public-read',
          contentType: multerS3.AUTO_CONTENT_TYPE,
          key: (req: any, file, cb) => {
            const fileName = `uploads/${name}/${userId}/media/${uuidv4()}-${file.originalname}`;
            cb(null, fileName);
          },
        });

      case 'GCP':
        const gcpStorage = new Storage({
          projectId: provider.projectId!,
          credentials: JSON.parse(provider.credentialsJson!),
        });
        return new GCPStorageEngine(gcpStorage.bucket(provider.bucketName!), name, userId);

      case 'AZURE':
        const azureBlobClient = BlobServiceClient.fromConnectionString(provider.connectionString!);
        return new AzureStorageEngine(azureBlobClient.getContainerClient(provider.containerName!), name, userId);

      default:
        throw new Error('Unsupported cloud provider');
    }
  } catch (err) {
    logger.error(`Error configuring storage: ${err.message || err}`);
    throw err;
  }
};

// Multer middleware
export const upload = async (req: any, res: Response, next: NextFunction) => {
  try {
    const type = req.user?.type;
    const companyId = req.user?.companyId;
    const userId = req.user?.id || 'unknown';

    // Import container when needed to avoid circular dependency
    const { container } = await import('../v1/container');
    // Resolve the service from container when needed
    const cloudStorageProviderService = container.resolve<CloudStorageProviderService>('cloudStorageProviderService');
    const result = await cloudStorageProviderService.getPrimaryCloudStorageProvider(companyId);
    const provider = result.success ? result.data: null;
    const storage = await getMulterStorage(provider, type, userId);

    logger.info(`Storage configured: ${storage}`);
    req.provider = provider;

    return multer({
      storage,
      limits: { fileSize: 25 * 1024 * 1024 },
      fileFilter: (_req, file, cb) => {
        if (ALLOW_TYPE.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error('File type not supported'));
        }
      },
    }).array('files', 5)(req, res, next);
  } catch (err: any) {
    logger.error(`Upload middleware error: ${err.message}`);
    if (err.message.includes('Unsupported cloud')) {
      return res.status(400).json({ error: 'Unsupported cloud storage provider', success: false, statusCode: 400 });
    }
    return res.status(500).json({ error: 'Internal Server Error', success: false, statusCode: 500 });
  }
};