import { PrismaClient } from "@prisma/client";

interface GlobalPrisma {
  prisma?: PrismaClient;
}

const globalForPrisma = global as GlobalPrisma;

let prisma: PrismaClient;
if (process.env.NODE_ENV === "production") {
  prisma = new PrismaClient({
    log: ["query", "info", "warn", "error"],
  });
} else {
  if (!globalForPrisma.prisma) {
    globalForPrisma.prisma = new PrismaClient({
      log: ["query", "info", "warn", "error"],
    });
  }
  prisma = globalForPrisma.prisma!;
}

export { prisma };
