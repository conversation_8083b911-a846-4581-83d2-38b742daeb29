import 'dotenv/config';
import 'express-async-errors';

import http from 'http';
import rateLimit from 'express-rate-limit';
import { Application, Request, Response, NextFunction, json, urlencoded } from 'express';
import hpp from 'hpp';
import helmet from 'helmet';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import { appRoutes } from './v1/routes';
import { AuthPayload, AuthService } from './utils/auth';
import { getLogger } from './utils/logger';
import { Server } from "socket.io";
import { setupSocket } from './socket/index';
import morgan from 'morgan';
import { ApiError } from './utils/error';
import { swaggerMiddleware } from './utils/swagger';
import { upload } from './utils/cloud';
import multer from 'multer';

const logger = getLogger("generals");
const MORGAN_STREAM = { write: (message: string) => logger.http(message.trim()) };
const EXCLUDED_PATHS = [
  '/api/v1/health', '/api/v1/documentation',
  '/api/v1/users/signup', '/api/v1/users/signin',
  '/api/v1/users/verify', '/api/v1/users/password/forgot',
  '/api/v1/guests/upsert', '/api/v1/guests/login',
  '/api/v1/guests/logout', '/api/v1/files/view',
];


const ERROR_CODES: Record<string, number> = {
  Unauthorized: 401,
  Forbidden: 403,
  'Not Found': 404,
  Conflict: 409,
  'Bad Request': 400,
  'Internal Server Error': 500,
  'Service Unavailable': 503,
  'Not Implemented': 501,
  'Gateway Timeout': 504,
  'Request Timeout': 408,
};

export async function start(app: Application, port: string, baseApiPath: string = process.env.BASE_API_PATH): Promise<void> {
  securityMiddleware(app);
  standardMiddleware(app);
  routesMiddleware(app, baseApiPath);
  swaggerMiddleware(app, baseApiPath);
  authErrorHandler(app);
  await startServer(app, port);
}

function securityMiddleware(app: Application): void {
  // CORS middleware
  app.use(async (req: Request, res: Response, next: NextFunction) => {
    await AuthService.corsMiddleware(req, res, next);
  });
  app.set('trust proxy', 1);
  app.disable('x-powered-by');
  app.use(cookieParser());
  app.use(hpp());
  app.use(helmet());


   // Rate Limiting to prevent brute-force attacks
   const limiter = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: {
      message: 'Too many requests, please try again later',
      success: false,
      statusCode: 429,
    },
    standardHeaders: true,
    legacyHeaders: false,
  });

  app.use(limiter);

  app.use((req: Request, res: Response, next: NextFunction) => {
    // Allow requests to excluded paths
    console.log(`****Request path*****: ${req.path}`);

    // Check if the path starts with any of the excluded paths
    const isExcluded = EXCLUDED_PATHS.some(excludedPath =>
      req.path === excludedPath || req.path.startsWith(`${excludedPath}/`)
    );

    if (isExcluded) {
      console.log(`Path ${req.path} is excluded from authentication`);
      return next();
    }

    // Allow requests from chat-api-client/lib/backend
    if(req.headers['RestClient'] && req.headers['RestClient'] === 'true'){
      return next();
    }

    const authHeader = req.headers.authorization;
    const cookieToken = req.cookies?.token || req.cookies?.sparkstrand_token;
    const bearerToken = authHeader?.startsWith("Bearer ") ? authHeader.split(" ")[1] : undefined;
    const token = bearerToken || cookieToken;

    if (!token) {
      return next(new ApiError("Unauthenticated: Please login", 401));
    }

    try {
      // Verify token and extract payload
      const { data: payload } = AuthService.verifyToken<AuthPayload>(token);

      if (!payload.isAuthenticated) {
        return next(new ApiError("Unauthenticated: Please login", 401));
      }

      // Guest access restriction
      const isGuestPath = /\/guests(\/|$)/.test(req.path) || /\/rooms(\/|$)/.test(req.path) || /\/messages(\/|$)/.test(req.path) || /\/files(\/|$)/.test(req.path);
      
      if (payload.type === "guest" && !isGuestPath) {
        return next(new ApiError("Forbidden: Access denied", 403));
      }

      req.user = payload;
      return next();
    } catch (error) {
      return next(new ApiError("Unauthenticated: Please login", 401));
    }
  });

}

function shouldCompress(req: any, res:  any) {
  if (req.headers['x-no-compression']) {
    return false; // Skip compression if the client requests it
  }
  return compression.filter?.(req, res) ?? true;
};
function standardMiddleware(app: Application): void {
  // Compression middleware with threshold and filter
  app.use(compression({threshold: 2048, filter: shouldCompress as compression.CompressionFilter} as compression.CompressionOptions));

  // JSON body parser with error handling
  app.use(
    json({
      limit: '200mb',
      strict: true, // Only parse arrays and objects
      type: (req) => {
        // Only parse requests with JSON content type
        return req.headers['content-type']?.startsWith('application/json');
      },
    })
  );

  // URL-encoded body parser with error handling
  app.use(
    urlencoded({
      extended: true,
      limit: '200mb',
      type: (req) => {
        // Only parse requests with URL-encoded content type
        return req.headers['content-type']?.startsWith('application/x-www-form-urlencoded');
      },
    })
  );

  // Multer file upload middleware with error handling
  app.use(async (req: Request, res: Response, next: NextFunction) => {
    if (req.headers['content-type']?.startsWith('multipart/form-data')) {
        upload(req, res, next);
    } else{
      next();
    }
  });


  // Morgan logging with custom format (optional)
  app.use(
    morgan('combined', {
      stream: MORGAN_STREAM,
      skip: (req: Request) => {
        return req.path === '/api/v1/health';
      },
    })
  );

}

function routesMiddleware(app: Application, baseApiPath: string): void {
  appRoutes(app, baseApiPath);
}
function authErrorHandler(app: Application): void {
  app.use((error: Error | ApiError, _req: Request, res: Response, next_: NextFunction) => {
    const statusCode = error instanceof ApiError ? error.statusCode : ERROR_CODES[error?.message || ""] || 500;
    // Logging based on error type
    if (statusCode < 500) {
      logger.warn(`{statusCode: ${statusCode}, details: ${error.message}, stack: ${error.stack}}`);
    } else {
      logger.error(`{statusCode: ${statusCode || 500}, details: ${error.message || 'Internal Server Error'}, stack: ${error.stack}}`);
    }

    // handle multer errors
    if (error instanceof multer.MulterError) {
        console.log(error);
        res.status(400).json(
          { message: error.message, success: false, statusCode: 400, data: null, stack: process.env.NODE_ENV !== 'production' ? error.stack : null });
        return;
    }

    // Handle JSON parsing errors
    if (error instanceof SyntaxError && 'body' in error) {
      logger.warn(`JSON parsing error: ${error.message}`);
      res.status(400).json({
        message: 'Invalid JSON payload',
        success: false,
        statusCode: 400,
        data: null,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : null,
      });
      return;
    }

    res.status(statusCode).json({
      message: error.message || 'Internal Server Error',
      success: false,
      statusCode,
      data: null,
      stack: process.env.NODE_ENV  !== 'production' ? error.stack : null,
    });
  });
}

async function startServer(app: Application, port: string): Promise<void> {
  try {
    const server: http.Server = http.createServer(app);

    server.setTimeout(120000, () => {
      logger.warn('Server request timed out');
    })

    // Initialize Socket.IO with CORS and connection options
    const io = new Server(server, {
      cors: {
        origin: true,
        credentials: true,
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      },
      connectionStateRecovery: {
        maxDisconnectionDuration: 2 * 60 * 1000,  // 2 minutes
        skipMiddlewares: true,
      },
      pingTimeout: 60000,
      pingInterval: 25000,
      transports: ['websocket', 'polling'],
    });

    // Setup socket handlers
    setupSocket(io);
    // Make io available in the route
    app.set('io', io)

    server.listen(port, () => {
      logger.info(`SparkStrand Chat Application running on port ${port}`);
    });

    server.on("error", (error) => {
      logger.error(`Server error: ${error.message}`);
      process.exit(1);
    });

  } catch (error) {
    logger.error("Error in startServer():", error);
    process.exit(1);
  }
}
