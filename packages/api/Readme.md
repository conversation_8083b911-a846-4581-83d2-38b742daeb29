# Socket.IO Enhancements

This document outlines the features made to the Socket.IO implementation in the SparkStrand Chat Application.

## Overview

The Socket.IO implementation has the following features:

1. **Connection Reliability**
   - Improved reconnection strategy with exponential backoff
   - Better error handling and logging
   - Connection state monitoring

2. **Performance Optimizations**
   - Message batching for offline messages
   - Optimized room management
   - Reduced unnecessary database queries

3. **Security Enhancements**
   - Rate limiting for socket events
   - Input validation and sanitization
   - Enhanced authentication middleware

4. **Developer Experience**
   - TypeScript client wrapper with better type safety
   - Comprehensive event documentation
   - Improved logging

5. **Feature Additions**
   - Typing indicators
   - Read receipts
   - User presence awareness

## Architecture

The enhanced Socket.IO implementation follows a modular architecture:

```
socket/
├── middleware/
│   └── globalMiddleware.ts      # Enhanced authentication middleware
├── general/
│   └── index.ts                 # general namespace implementation - which is default for every company - no set needed
└── index.ts                     # Main Socket.IO setup entry and registery for other specialised namescape
```

## Authentication Flow

The guest authentication flow remains the same but with enhanced security:

1. **Initiation Phase**:
   - Client calls `/guests/initiate` with API key, API key secret, and metadata
   - Server validates the API key and creates/finds a guest user
   - <PERSON> generates two tokens: `clientToken` and `serverToken`

2. **Connection Phase**:
   - Client calls `/guests/connect` with the `clientToken`
   - Server validates the token and generates a JWT (`sparkstrand_token`)
   - Server returns the token and sets it as a cookie

3. **Socket.IO Connection**:
   - Client connects to Socket.IO with the JWT token
   - `GlobalMiddleware` validates the token and retrieves guest information
   - Socket connection is established with user data attached to the socket

4. **Application Verification**:
   - Each Socket.IO namespace has middleware that verifies if the guest has access to the specific application type
   - If verification succeeds, the socket joins the namespace and can interact with it

## New Features

### Typing Indicators

The typing indicator feature allows users to see when someone is typing in a chat room:

```typescript
// Client-side
socket.emit('typing', { roomId: 'room-id' });
socket.emit('stopTyping', { roomId: 'room-id' });

// Server-side events
socket.on('typing', (data) => {
  handleTyping(socket, io, data.roomId);
});

socket.on('stopTyping', (data) => {
  handleStopTyping(socket, io, data.roomId);
});
```

### Read Receipts

The read receipt feature allows users to see when their messages have been read:

```typescript
// Client-side
socket.emit('markMessageRead', 'message-id');

// Server-side
socket.on('markMessageRead', async (messageId) => {
  // Update message read status
  await prisma.message.update({
    where: { id: messageId },
    data: { read: true }
  });
  
  // Broadcast read status to room
  io.to(roomId).emit('messageRead', {
    messageId,
    userId: socket.user.id,
    timestamp: new Date()
  });
});
```

### User Presence

The user presence feature allows users to see the online status of other users:

```typescript
// Client-side
socket.emit('setUserStatus', 'online' | 'away' | 'offline');

// Server-side
socket.on('setUserStatus', async (status) => {
  // Update user status in database
  await prisma.guest.update({
    where: { id: socket.user.id },
    data: { 
      awayModeEnabled: status === 'away',
      lastSeenAt: new Date()
    }
  });
  
  // Broadcast status change
  io.to(roomId).emit('userStatusChanged', {
    userId: socket.user.id,
    status,
    timestamp: new Date()
  });
});
```

## Rate Limiting

Rate limiting has been implemented to prevent abuse:

```typescript
const RATE_LIMITS = {
  messages: {
    windowMs: 60 * 1000, // 1 minute
    max: 60, // 60 messages per minute
  },
  rooms: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 room operations per minute
  },
};
```

## Client Wrapper

A TypeScript client wrapper has been created to provide a better developer experience:

```typescript
import { SocketClient, SocketEvent, RoomType } from './socket-client';

// Create a new client
const client = new SocketClient({
  url: 'https://api.example.com',
  token: 'jwt-token or cookie token'
  debug: true
});

// Listen for events
client.on(SocketEvent.CONNECT, () => {
  console.log('Connected to server');
});

client.on(SocketEvent.NEW_MESSAGE, (data) => {
  console.log('New message:', data);
});

// Send a message
client.sendMessage({
  text: 'Hello, world!',
  roomId: 'room-id'
});

// Join a room
client.joinRoom('room-id');
```

## Implementation Notes

1. **Error Handling**: All socket events now have proper error handling with detailed logging.

2. **Reconnection Strategy**: The client automatically reconnects with exponential backoff.

3. **Message Queueing**: Messages sent while offline are queued and sent when the connection is restored.

4. **Type Safety**: All events and data structures are properly typed for better developer experience.

5. **Security**: Input validation and sanitization are performed on all user inputs.

## How to Use

To use the enhanced Socket.IO implementation:

1. Update the app.ts file to use the enhanced Socket.IO setup:

```typescript
import { setupEnhancedSocket } from './socket/enhancedIndex';

// Initialize Socket.IO with CORS and connection options
const io = new Server(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGIN,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  },
  connectionStateRecovery: {
    maxDisconnectionDuration: 2 * 60 * 1000, 
    skipMiddlewares: true,
  },
  pingTimeout: 60000, 
  pingInterval: 25000, 
});

// Setup socket handlers
setupSocket(io);
```

2. Use the client wrapper in your frontend code:

```typescript
import { SocketClient, SocketEvent } from '@sparkstrand/socket-client';

const socket = new SocketClient({
  url: 'https://api.example.com',
  token: 'jwt-token'
});

socket.on(SocketEvent.CONNECT, () => {
  console.log('Connected to server');
});

socket.on(SocketEvent.NEW_MESSAGE, (data) => {
  console.log('New message:', data);
});
```

## Event Reference

### Connection Events

- `connect`: Emitted when the socket connects to the server
- `disconnect`: Emitted when the socket disconnects from the server
- `connect_error`: Emitted when a connection error occurs
- `reconnect`: Emitted when the socket reconnects to the server
- `reconnect_attempt`: Emitted when the socket attempts to reconnect
- `reconnect_error`: Emitted when a reconnection error occurs
- `reconnect_failed`: Emitted when all reconnection attempts fail

### Authentication Events

- `authenticated`: Emitted when the socket is authenticated
- `auth_error`: Emitted when an authentication error occurs

### Room Events

- `joinRoom`: Emitted to join a room
- `leaveRoom`: Emitted to leave a room
- `roomJoined`: Emitted when a room is joined
- `roomLeft`: Emitted when a room is left
- `createRoom`: Emitted to create a room
- `roomCreated`: Emitted when a room is created
- `userJoined`: Emitted when a user joins a room
- `userLeft`: Emitted when a user leaves a room

### Message Events

- `sendMessage`: Emitted to send a message
- `newMessage`: Emitted when a new message is received
- `markMessageRead`: Emitted to mark a message as read
- `messageRead`: Emitted when a message is marked as read

### Typing Events

- `typing`: Emitted when a user starts typing
- `stopTyping`: Emitted when a user stops typing
- `userTyping`: Emitted when a user is typing
- `userStoppedTyping`: Emitted when a user stops typing

### User Status Events

- `setUserStatus`: Emitted to set user status
- `userStatusChanged`: Emitted when a user's status changes

### Error Events

- `error`: Emitted when an error occurs
