import { z, Zod<PERSON><PERSON><PERSON>,  <PERSON>od<PERSON>ssue,  ZodSchema } from "zod";
import { ObjectId } from "mongodb";

// Custom Zod schema for MongoDB ObjectId
const objectIdSchema = z.string().refine((value: any) => ObjectId.isValid(value), {
  message: "Invalid MongoDB ObjectId",
});

export abstract class ZodValidation<T> {
  // Required schemas (must be implemented by child classes)
  abstract createSchema: ZodSchema<T>; // Schema for create operations
  abstract updateSchema: ZodSchema<Partial<T>>; // Schema for update operations

  // Default ID schema for MongoDB ObjectId
  idSchema: ZodSchema<string> = objectIdSchema;

  // Default validation methods
  validateCreate(data: T): T {
    try {
      const result = this.createSchema.safeParse(data);
      if (!result.success) {
        throw new Error(`Validation failed: ${result.error.errors.map((e: ZodIssue) => e.message).join(", ")}`);
      }
      return result.data;
    } catch (error) {
      if (error instanceof ZodError) {
        throw new Error(`Validation failed: ${error.errors.map((e: ZodIssue) => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  validateUpdate(data: Partial<T>): Partial<T> {
    try {
      const result = this.updateSchema.safeParse(data);
      if(!result.success) {
        throw new Error(`Validation failed: ${result.error.errors.map((e: ZodIssue) => e.message).join(", ")}`);
      }
      return result.data;
    } catch (error) {
      if (error instanceof ZodError) {
        throw new Error(`Validation failed: ${error.errors.map((e: ZodIssue) => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  validateId(id: string): string {
    try {
      return this.idSchema.parse(id);
    } catch (error) {
      if (error instanceof ZodError) {
        throw new Error(`Validation failed: ${error.errors.map((e: ZodIssue) => e.message).join(", ")}`);
      }
      throw error;
    }
  }

  // Optional validation method (can be overridden by child classes)
  validateCustom(data: any): any {
    // Default implementation does nothing
    return data;
  }
}