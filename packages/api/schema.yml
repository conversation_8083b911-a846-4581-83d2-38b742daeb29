openapi: 3.0.1
info:
  title: Chat API
  version: '0.1'
  description: The Chat API reference.
  contact:
    name: Spark Strand Chat
    url: https://sparkstrand.com
  license:
    name: MIT
    url: https://spdx.org/licenses/MIT

# region ENDPOINTS

paths:

  # regions USERS
  /me:
    get:
      summary: Identify an admin
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Admins
      operationId: identifyAdmin
      description: >

        You can view the currently authorised admin along with the embedded app
        object (a "workspace" in legacy terminology).


        > 🚧 Single Sign On

        >

        > If you are building a custom "Log in with Intercom" flow for your
        site, and you call the `/me` endpoint to identify the logged-in user,
        you should not accept any sign-ins from users with unverified email
        addresses as it poses a potential impersonation security risk.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Successful response:
                  value:
                    type: admin
                    id: '991267390'
                    email: <EMAIL>
                    name: Ciaran1 Lee
                    email_verified: true
                    app:
                      type: app
                      id_code: this_is_an_id1_that_should_be_at_least_40
                      name: MyApp 1
                      created_at: 1719492696
                      secure: false
                      identity_verification: false
                      timezone: America/Los_Angeles
                      region: US
                    avatar:
                      type: avatar
                      image_url: >-
                        https://static.chatassets.com/assets/default-avatars/admins/128.png
                    has_inbox_seat: true
              schema:
                $ref: '#/components/schemas/admin_with_app'
  /users/{id}/away:
    put:
      summary: Set a user to away
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of a given user
          schema:
            type: string
      tags:
        - Users
      description: You can set a User as away.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Successful response:
                  value:
                    type: admin
                    id: '991267391'
                    name: Ciaran2 Lee
                    email: <EMAIL>
                    away_mode_enabled: true
                    away_mode_reassign: true
                    has_inbox_seat: true
                    team_ids: []
              schema:
                $ref: '#/components/schemas/admin'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 18722269-a019-46c4-87d7-50d0f6f8a990
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Admin not found
          content:
            application/json:
              examples:
                Admin not found:
                  value:
                    type: error.list
                    request_id: 9818bd03-9cc6-4ab8-8e7c-20a45ac58e97
                    errors:
                      - code: admin_not_found
                        message: Admin for admin_id not found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - away_mode_enabled
                - away_mode_reassign
              properties:
                away_mode_enabled:
                  type: boolean
                  description: Set to "true" to change the status of the admin to away.
                  example: true
                  default: true
                away_mode_reassign:
                  type: boolean
                  description: >-
                    Set to "true" to assign any new conversation replies to your
                    default inbox.
                  example: false
                  default: false
            examples:
              successful_response:
                summary: Successful response
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
              admin_not_found:
                summary: Admin not found
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
              unauthorized:
                summary: Unauthorized
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
  /users/activity_logs:
    get:
      summary: List all activity logs
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: created_at_after
          in: query
          required: true
          description: >-
            The start date that you request data for. It must be formatted as a
            UNIX timestamp.
          example: '1677253093'
          schema:
            type: string
        - name: created_at_before
          in: query
          required: false
          description: >-
            The end date that you request data for. It must be formatted as a
            UNIX timestamp.
          example: '1677861493'
          schema:
            type: string
      tags:
        - Admins
      operationId: listActivityLogs
      description: You can get a log of activities by all admins in an app.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Successful response:
                  value:
                    type: activity_log.list
                    pages:
                      type: pages
                      next: null
                      page: 1
                      per_page: 20
                      total_pages: 1
                    activity_logs:
                      - id: ddee3a18-0032-4061-b9b9-26230c3dd5f7
                        performed_by:
                          type: admin
                          id: '991267395'
                          email: <EMAIL>
                          ip: 127.0.0.1
                        metadata:
                          message:
                            id: 123
                            title: Initial message title
                          before: Initial message title
                          after: Eventual message title
                        created_at: 1719492702
                        activity_type: message_state_change
                        activity_description: >-
                          Ciaran5 Lee changed your Initial message title message
                          from Initial message title to Eventual message title.
                      - id: 5eec951b-db7a-4b5b-add5-95ffc90969b6
                        performed_by:
                          type: admin
                          id: '991267395'
                          email: <EMAIL>
                          ip: 127.0.0.1
                        metadata:
                          before: before
                          after: after
                        created_at: 1719492702
                        activity_type: app_name_change
                        activity_description: >-
                          Ciaran5 Lee changed your app name from before to
                          after.
              schema:
                $ref: '#/components/schemas/activity_log_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 06d9eefd-2b3a-48f7-938a-5a10383a4ebf
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
  /users:
    get:
      summary: List all users
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Users
      description: You can fetch a list of users for a given workspace.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Successful response:
                  value:
                    type: admin.list
                    admins:
                      - type: admin
                        email: <EMAIL>
                        id: '991267397'
                        name: Ciaran7 Lee
                        away_mode_enabled: false
                        away_mode_reassign: false
                        has_inbox_seat: true
                        team_ids: []
              schema:
                $ref: '#/components/schemas/admin_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 4ba8121e-4a4a-4668-adb2-363c561f3c52
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
  /users/{id}:
    get:
      summary: Retrieve a user
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier of a given admin
          example: 123
          schema:
            type: integer
      tags:
        - Users
      description: You can retrieve the details of a single user.
      responses:
        '200':
          description: User found
          content:
            application/json:
              examples:
                User found:
                  value:
                    type: admin
                    id: 65ea80ee37ac531683834248
                    name: Ciaran9 Lee
                    email: <EMAIL>
                    companyId: 65ea80ee37ac531683834248
                    externalId: 65ea80ee37ac531683834248
                    awayModeEnabled: false
                    avatar:
                      filename: 'myprofilePic.png'
                      fileUrl: https://picsum.photos/200/300
              schema:
                $ref: '#/components/schemas/user'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 83978032-1473-4696-b755-b497d46a23cf
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: User not found
          content:
            application/json:
              examples:
                Admin not found:
                  value:
                    type: error.list
                    request_id: 989bdb0b-1e8c-46cc-8953-9733dad40562
                    errors:
                      - code: user_not_found
                        message: User not found
              schema:
                $ref: '#/components/schemas/error'
  /users/{id}/rooms:
    get:
      summary: Retrieve all rooms a user is part of
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of a given user
          example: 123
          schema:
            type: string
      tags:
        - Users
        - Rooms
      description: You can retrieve the rooms of a single user.
      responses:

        '200':
          description: User found
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/room'

        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 83978032-1473-4696-b755-b497d46a23cf
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: User not found
          content:
            application/json:
              examples:
                Admin not found:
                  value:
                    type: error.list
                    request_id: 989bdb0b-1e8c-46cc-8953-9733dad40562
                    errors:
                      - code: user_not_found
                        message: User not found
              schema:
                $ref: '#/components/schemas/error'
  /users/{id}/messages/{messageId}:
    put:
      summary: Update a user's mesage in a room
      parameters:
        - name: id
          in: path
          description: The unique identifier of a user
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
        - name: messageId
          in: path
          description: The unique identifier of a message
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      tags:
        - Messages
        - Users
      description: |
        You can use this endpoint to update a user's message sent to a room.

        Fields that can be updated
        - content
          - text
          - files
        - mentions
          - type
          - mentionedUserIds

      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    $ref: '#/components/schemas/message'
        '400':
          description: Invalid parameters
          content:
            application/json:
              examples:
                Invalid parameters:
                  value:
                    type: error.list
                    request_id: a7afe3c5-be52-4b69-9268-50ef1d917a1b
                    errors:
                      - code: parameter_invalid
                        message: invalid tag parameters
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3609e8b1-a6aa-4c57-a994-3d95743f20a2
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/updateMessage'
    get:
      summary: Gets a user's mesage in a room
      parameters:
        - name: id
          in: path
          description: The unique identifier of a user
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
        - name: messageId
          in: path
          description: The unique identifier of a message
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      
      tags:
        - Messages
        - Users
      description: |
        You can use this endpoint to get a user's message sent to a room.

      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    $ref: '#/components/schemas/message'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'

    delete:
      summary: Delete a user's mesage in a room
      parameters:
        - name: id
          in: path
          description: The unique identifier of a user
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
        - name: messageId
          in: path
          description: The unique identifier of a message
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      
      tags:
        - Messages
        - Users
      description: You can use this endpoint to delete a user's message sent to a room.

      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type:
                      string
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'


# region APPLICATIONS
  /applications/{id}:
    get:
      summary: Get an applicatiom
      parameters:
        - name: id
          in: path
          schema:
            type: string
          required: true
      tags:
        - Applications
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/app'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'

# region COMPANIES
  /companies:
    post:
      summary: Create a company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Companies
      description: >
        You can create a company.


        Companies will be only visible in Intercom when there is at least one
        associated user.


        Companies are looked up via `company_id` in a `POST` request, if not
        found via `company_id`, the new company will be created, if found, that
        company will be updated.


        {% admonition type="warning" name="Using `company_id`" %}
          You can set a unique `company_id` value when creating a company. However, it is not possible to update `company_id`. Be sure to set a unique value once upon creation of the company.
        {% /admonition %}
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: company
                    company_id: company_remote_id
                    id: 667d607c8a68186f43bafd1e
                    app_id: this_is_an_id116_that_should_be_at_least_
                    name: my company
                    remote_created_at: 1374138000
                    created_at: 1719492732
                    updated_at: 1719492732
                    monthly_spend: 0
                    session_count: 0
                    user_count: 0
                    tags:
                      type: tag.list
                      tags: []
                    segments:
                      type: segment.list
                      segments: []
                    plan: {}
                    custom_attributes:
                      creation_source: api
              schema:
                $ref: '#/components/schemas/company'
        '400':
          description: Bad Request
          content:
            application/json:
              examples:
                Bad Request:
                  value:
                    type: error.list
                    request_id: null
                    errors:
                      - code: bad_request
                        message: bad 'test' parameter
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 9b0d6fb9-d2d7-4904-a13c-97557a802323
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/create_or_update_company_request'
            examples:
              successful:
                summary: Successful
                value:
                  company_id: company_remote_id
                  name: my company
                  remote_created_at: 1374138000
              bad_request:
                summary: Bad Request
                value:
                  test: invalid
    get:
      summary: Retrieve companies
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: name
          in: query
          required: false
          description: The `name` of the company to filter by.
          example: my company
          schema:
            type: string
        - name: perPage
          in: query
          required: false
          description: How many results to display per page. Defaults to 10
          example: 10
          schema:
            type: integer
      tags:
        - Companies
      description: >
        You can fetch a single company by passing in a filter `name`.

          `https://api.chat.io/companies?name={name}`

          `https://api.chat.io/companies?company_id={company_id}`
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/company'      
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 6ef54f1c-70a4-4779-b3a6-29e4fd65d9dd
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: c97dd75f-a434-4c83-a8e8-c4d1887d6c48
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
  /companies/{id}:
    get:
      summary: Retrieve a company by ID
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by ChatApp
          example: 5f4d3c1c-7b1b-4d7d-a97e-6095715c6632
          schema:
            type: string
      tags:
        - Companies
      description: You can fetch a single company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/company'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: c4e2fcb8-815e-4bee-80c7-9d5b1ab0f6fe
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: b49593ed-49a6-4497-8fb7-220ff74527f6
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
    put:
      summary: Update a company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by Intercom
          example: 5f4d3c1c-7b1b-4d7d-a97e-6095715c6632
          schema:
            type: string
      tags:
        - Companies
      operationId: UpdateCompany
      description: |
        You can update a single company using the Intercom provisioned `id`.

        {% admonition type="warning" name="Using `company_id`" %}
          When updating a company it is not possible to update `company_id`. This can only be set once upon creation of the company.
        {% /admonition %}
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: company
                    company_id: '1'
                    id: 667d60828a68186f43bafd3b
                    app_id: this_is_an_id134_that_should_be_at_least_
                    name: company2
                    remote_created_at: 1719492738
                    created_at: 1719492738
                    updated_at: 1719492738
                    monthly_spend: 0
                    session_count: 0
                    user_count: 1
                    tags:
                      type: tag.list
                      tags: []
                    segments:
                      type: segment.list
                      segments: []
                    plan: {}
                    custom_attributes: {}
              schema:
                $ref: '#/components/schemas/company'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3f26a216-ddff-4782-9529-514f5bad56ea
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: b1ce72df-630f-4925-b212-fca6e833eb8d
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
    delete:
      summary: Delete a company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by Intercom
          example: 5f4d3c1c-7b1b-4d7d-a97e-6095715c6632
          schema:
            type: string
      tags:
        - Companies
      operationId: deleteCompany
      description: You can delete a single company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    id: 667d60848a68186f43bafd45
                    object: company
                    deleted: true
              schema:
                $ref: '#/components/schemas/deleted_company_object'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 0a1a5065-69fe-47a4-9804-4cb2347671ef
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: 35a9b551-331e-499e-a63f-20396bfd29f5
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
  
                /companies/{id}/contacts:
    get:
      summary: List attached contacts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by Intercom
          example: 5f4d3c1c-7b1b-4d7d-a97e-6095715c6632
          schema:
            type: string
      tags:
        - Companies
        - Contacts
      operationId: ListAttachedContacts
      description: You can fetch a list of all contacts that belong to a company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: list
                    data: []
                    total_count: 0
                    pages:
                      type: pages
                      page: 1
                      per_page: 50
                      total_pages: 0
              schema:
                $ref: '#/components/schemas/company_attached_contacts'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: fe20b681-f988-4154-bec9-a5087fe0842e
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: a6381081-a166-4e8e-952d-38bb2cd1c2b4
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'

  /companies/{id}/applications:
    get:
      summary: List attached applications
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by ChatApp
          example: 667d616e8a68186f43bafe55
          schema:
            type: string
      tags:
        - Companies
        - Applications
      description: You can fetch a list of all applications that belong to a company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/app'
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: fe20b681-f988-4154-bec9-a5087fe0842e
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: a6381081-a166-4e8e-952d-38bb2cd1c2b4
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
    #jj
    post:
      summary: Create an application in a company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Companies
        - Applications
      description: You can create an application within a company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/app'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createApp'
  
  /companies/{id}/users:
    get:
      summary: List attached users
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by ChatApp
          example: 667d616e8a68186f43bafe55
          schema:
            type: string
      tags:
        - Companies
        - Applications
      description: You can fetch a list of all users that belong to a company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/user'
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: fe20b681-f988-4154-bec9-a5087fe0842e
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: a6381081-a166-4e8e-952d-38bb2cd1c2b4
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
  
  /companies/{id}/guests:
    get:
      summary: List attached guests
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by ChatApp
          example: 667d616e8a68186f43bafe55
          schema:
            type: string
      tags:
        - Companies
        - Applications
      description: You can fetch a list of all guests that belong to a company.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/guest'
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: fe20b681-f988-4154-bec9-a5087fe0842e
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: a6381081-a166-4e8e-952d-38bb2cd1c2b4
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
  
  /contacts/{id}/companies:
    post:
      summary: Attach a Contact to a Company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier for the contact which is given by Intercom
          schema:
            type: string
      tags:
        - Companies
        - Contacts
      operationId: attachContactToACompany
      description: You can attach a company to a single contact.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: company
                    company_id: '1'
                    id: 667d608d8a68186f43bafd70
                    app_id: this_is_an_id166_that_should_be_at_least_
                    name: company6
                    remote_created_at: 1719492749
                    created_at: 1719492749
                    updated_at: 1719492749
                    monthly_spend: 0
                    session_count: 0
                    user_count: 1
                    tags:
                      type: tag.list
                      tags: []
                    segments:
                      type: segment.list
                      segments: []
                    plan: {}
                    custom_attributes: {}
              schema:
                $ref: '#/components/schemas/company'
        '400':
          description: Bad Request
          content:
            application/json:
              examples:
                Bad Request:
                  value:
                    type: error.list
                    request_id: 9297dcfc-1896-43a3-a3f9-131238422ed2
                    errors:
                      - code: parameter_not_found
                        message: company not specified
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 99739bbd-2dbe-4ce3-ae91-af23379b5cd7
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Company Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: 32d121d8-fcbf-4c59-9c60-204f7d602f36
                    errors:
                      - code: company_not_found
                        message: Company Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: string
                  description: >-
                    The unique identifier for the company which is given by
                    Intercom
                  example: 58a430d35458202d41b1e65b
            examples:
              successful:
                summary: Successful
                value:
                  id: 667d608d8a68186f43bafd70
              bad_request:
                summary: Bad Request
                value: null
              company_not_found:
                summary: Company Not Found
                value:
                  id: '123'
    get:
      summary: List attached companies for contact
      parameters:
        - name: id
          in: path
          description: The unique identifier for the contact which is given by Intercom
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Contacts
        - Companies
      operationId: listCompaniesForAContact
      description: You can fetch a list of companies that are associated to a contact.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: list
                    data:
                      - type: company
                        company_id: '1'
                        id: 667d60938a68186f43bafd91
                        app_id: this_is_an_id182_that_should_be_at_least_
                        name: company12
                        remote_created_at: 1719492755
                        created_at: 1719492755
                        updated_at: 1719492755
                        last_request_at: 1719319955
                        monthly_spend: 0
                        session_count: 0
                        user_count: 1
                        tags:
                          type: tag.list
                          tags: []
                        segments:
                          type: segment.list
                          segments: []
                        plan: {}
                        custom_attributes: {}
                    pages:
                      type: pages
                      next: null
                      page: 1
                      per_page: 50
                      total_pages: 1
                    total_count: 1
              schema:
                $ref: '#/components/schemas/contact_attached_companies'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: d211ec8c-df9b-420c-86df-23c27ad54bc5
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact not found
          content:
            application/json:
              examples:
                Contact not found:
                  value:
                    type: error.list
                    request_id: b9d7374d-1780-4668-bb62-0e1ff9cdab45
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
  /contacts/{contact_id}/companies/{id}:
    delete:
      summary: Detach a contact from a company
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: contact_id
          in: path
          required: true
          description: The unique identifier for the contact which is given by Intercom
          example: 58a430d35458202d41b1e65b
          schema:
            type: string
        - name: id
          in: path
          required: true
          description: The unique identifier for the company which is given by Intercom
          example: 58a430d35458202d41b1e65b
          schema:
            type: string
      tags:
        - Companies
        - Contacts
      operationId: detachContactFromACompany
      description: You can detach a company from a single contact.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: company
                    company_id: '1'
                    id: 667d60918a68186f43bafd80
                    app_id: this_is_an_id174_that_should_be_at_least_
                    name: company8
                    remote_created_at: 1719492753
                    created_at: 1719492753
                    updated_at: 1719492753
                    monthly_spend: 0
                    session_count: 0
                    user_count: 0
                    tags:
                      type: tag.list
                      tags: []
                    segments:
                      type: segment.list
                      segments: []
                    plan: {}
                    custom_attributes: {}
              schema:
                $ref: '#/components/schemas/company'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: d8c1ab2d-4044-4c4f-98f0-176860747112
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact Not Found
          content:
            application/json:
              examples:
                Company Not Found:
                  value:
                    type: error.list
                    request_id: cd4f1648-724c-45f0-b6e1-72a1bc6479ee
                    errors:
                      - code: company_not_found
                        message: Company Not Found
                Contact Not Found:
                  value:
                    type: error.list
                    request_id: d316defc-0a2f-49e7-b8ff-4cb6ccf46c90
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
  
  /accounts/{id}/subscriptions:
    get:
      summary: List subscriptions for an account
      parameters:
        - name: id
          in: path
          description: The unique identifier for the account which is given by ChatApp
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Accounts
        - Subscription Types
      description: >
        You can fetch a list of subscription types that are attached to a
        contact. These can be subscriptions that a user has 'opted-in' to or has
        'opted-out' from, depending on the subscription type.

        This will return a list of Subscription Type objects that the contact is
        associated with.


        The data property will show a combined list of:

          1.Opt-out subscription types that the user has opted-out from.
          2.Opt-in subscription types that the user has opted-in to receiving.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: list
                    data:
                      - type: subscription
                        id: '93'
                        state: live
                        consent_type: opt_out
                        default_translation:
                          name: Newsletters
                          description: Lorem ipsum dolor sit amet
                          locale: en
                        translations:
                          - name: Newsletters
                            description: Lorem ipsum dolor sit amet
                            locale: en
                        content_types:
                          - email
                      - type: subscription
                        id: '95'
                        state: live
                        consent_type: opt_in
                        default_translation:
                          name: Newsletters
                          description: Lorem ipsum dolor sit amet
                          locale: en
                        translations:
                          - name: Newsletters
                            description: Lorem ipsum dolor sit amet
                            locale: en
                        content_types:
                          - sms_message
              schema:
                $ref: '#/components/schemas/subscription_type_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 82a95655-569d-4e5d-b0d9-f8a6c7a379f3
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact not found
          content:
            application/json:
              examples:
                Contact not found:
                  value:
                    type: error.list
                    request_id: 3f481052-cf49-4b95-a492-************
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
    put:
      summary: update the subscription for an account
      tags:
        - Accounts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: The unique identifier for the account which is given by ChatApp
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
      description: >
        You can update the subscription of an account. In ChatApp, we have
        four different subscription types based on the user preference.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    message: 'Update successful'
                    data:
                      subscriptionType: standard
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 377d162e-82a5-4148-a26f-29c9c760dadc
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Resource not found
          content:
            application/json:
              examples:
                Account not found:
                  value:
                    type: error.list
                    request_id: cf0f6fd6-7c5e-492b-909d-f60b35eea1c4
                    errors:
                      - code: not_found
                        message: Resource Not Found
                Resource not found:
                  value:
                    type: error.list
                    request_id: 3f852f45-1a80-4ade-9bc6-72b377d2bbd8
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - subscriptionType
              properties:
                subscriptionType:
                  type: string
                  enum: [free, basic, standard, premimum]
                  description: The type of subscription
                  example: premium
            examples:
              successful:
                summary: Successful
                value:
                  subscriptionType: premium
  /accounts/{id}/subscriptions/{id}:
    delete:
      summary: Remove subscription from a contact
      tags:
        - Subscription Types
        - Contacts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: contact_id
          in: path
          description: The unique identifier for the contact which is given by Intercom
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
        - name: id
          in: path
          description: >-
            The unique identifier for the subscription type which is given by
            Intercom
          example: '37846'
          required: true
          schema:
            type: string
      operationId: detachSubscriptionTypeToContact
      description: >-
        You can remove a specific subscription from a contact. This will return
        a subscription type model for the subscription type that was removed
        from the contact.
      responses:
        '200':
          description: Successful
          content:
            application/json:
              examples:
                Successful:
                  value:
                    type: subscription
                    id: '124'
                    state: live
                    consent_type: opt_in
                    default_translation:
                      name: Newsletters
                      description: Lorem ipsum dolor sit amet
                      locale: en
                    translations:
                      - name: Newsletters
                        description: Lorem ipsum dolor sit amet
                        locale: en
                    content_types:
                      - sms_message
              schema:
                $ref: '#/components/schemas/subscription_type'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 99fba0c6-2252-4658-abd2-1d2ff16a508b
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Resource not found
          content:
            application/json:
              examples:
                Contact not found:
                  value:
                    type: error.list
                    request_id: 7bc429e4-e887-4f53-b69c-94e6e55d2125
                    errors:
                      - code: not_found
                        message: User Not Found
                Resource not found:
                  value:
                    type: error.list
                    request_id: 66ebc1f5-5e02-4584-8028-f2559a41e8df
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
  /contacts/{contact_id}/tags:
    get:
      summary: List tags attached to a contact
      tags:
        - Contacts
        - Tags
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: contact_id
          in: path
          description: The unique identifier for the contact which is given by Intercom
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
      operationId: listTagsForAContact
      description: >-
        You can fetch a list of all tags that are attached to a specific
        contact.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: list
                    data:
                      - type: tag
                        id: '93'
                        name: Manual tag
              schema:
                $ref: '#/components/schemas/tag_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 97383559-0fb0-4084-8a9a-8e3407c46108
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact not found
          content:
            application/json:
              examples:
                Contact not found:
                  value:
                    type: error.list
                    request_id: 2b513026-b78c-4c67-b073-da0266f62cc7
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
    post:
      summary: Add tag to a contact
      tags:
        - Tags
        - Contacts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: contact_id
          in: path
          description: The unique identifier for the contact which is given by Intercom
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
      operationId: attachTagToContact
      description: >-
        You can tag a specific contact. This will return a tag object for the
        tag that was added to the contact.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: tag
                    id: '94'
                    name: Manual tag
              schema:
                $ref: '#/components/schemas/tag'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 63a2828f-107e-4d51-9398-a220b81a7bce
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Tag not found
          content:
            application/json:
              examples:
                Contact not found:
                  value:
                    type: error.list
                    request_id: 1bbd7e4b-718e-46f4-b682-a429aea78f01
                    errors:
                      - code: not_found
                        message: User Not Found
                Tag not found:
                  value:
                    type: error.list
                    request_id: a1a28017-728a-423b-adc0-2705d375f533
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - id
              properties:
                id:
                  type: string
                  description: The unique identifier for the tag which is given by Intercom
                  example: '7522907'
            examples:
              successful:
                summary: successful
                value:
                  id: 94
              contact_not_found:
                summary: Contact not found
                value:
                  id: 95
              tag_not_found:
                summary: Tag not found
                value:
                  id: '123'
  
#region ACCOUNTS
  /accounts/{id}:
    put:
      summary: Update an account
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: id
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
      tags:
        - Accounts
      description: You can update an existing account.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    id: 667d60a88a68186f43bafdb8
                    email: <EMAIL>
                    name: SportyExpats One
                    companiesCount: 6
                    applicationsCount: 8
                    usersCount: 12
                    guestsCount: 230
                    updatedAt: **********
              schema:
                $ref: '#/components/schemas/account'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: b1b88e2d-938c-4a26-b65d-26ff65a0af36
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/accountUpdate'
            examples:
              successful:
                summary: successful
                value:
                  companiesCount: 6
                  applicationsCount: 8
                  usersCount: 12
                  guestsCount: 230
    get:
      summary: Get an account
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: id
          example: 63a07ddf05a32042dffac965
          required: true
          schema:
            type: string
      tags:
        - Accounts
      description: You can fetch the details of a single account.
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/account'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: f70085f1-f655-43ee-9585-d2061b260fcd
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
    delete:
      summary: Delete an account
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: id
          required: true
          schema:
            type: string
      tags:
        - Accounts
      description: You can delete a single account.
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 0bce3945-d2ec-4b8e-a790-b16fd52d9f11
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
  
  /accounts/search:
    post:
      summary: Search accounts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Accounts
      description: >
        You can search for multiple contacts by the value of their attributes in
        order to fetch exactly who you want.


        To search for contacts, you need to send a `POST` request to
        `https://api.chat.io/contacts/search`.


        This will accept a query object in the body which will define your
        filters in order to search for contacts.


        {% admonition type="warning" name="Optimizing search queries" %}
          Search queries can be complex, so optimizing them can help the performance of your search.
          Use the `AND` and `OR` operators to combine multiple filters to get the exact results you need and utilize
          pagination to limit the number of results returned. The default is `50` results per page.
          See the [pagination section](https://developers.chat.com/docs/build-an-integration/learn-more/rest-apis/pagination/#example-search-conversations-request) for more details on how to use the `starting_after` param.
        {% /admonition %}

        ### Contact Creation Delay


        If a contact has recently been created, there is a possibility that it
        will not yet be available when searching. This means that it may not
        appear in the response. This delay can take a few minutes. If you need
        to be instantly notified it is recommended to use webhooks and iterate
        to see if they match your search filters.


        ### Nesting & Limitations


        You can nest these filters in order to get even more granular insights
        that pinpoint exactly what you need. Example: (1 OR 2) AND (3 OR 4).

        There are some limitations to the amount of multiple's there can be:

        * There's a limit of max 2 nested filters

        * There's a limit of max 15 filters for each AND or OR group


        ### Searching for Timestamp Fields


        All timestamp fields (created_at, updated_at etc.) are indexed as Dates
        for Contact Search queries; Datetime queries are not currently
        supported. This means you can only query for timestamp fields by day -
        not hour, minute or second.

        For example, if you search for all Contacts with a created_at value
        greater (>) than 1577869200 (the UNIX timestamp for January 1st, 2020
        9:00 AM), that will be interpreted as 1577836800 (January 1st, 2020
        12:00 AM). The search results will then include Contacts created from
        January 2nd, 2020 12:00 AM onwards.

        If you'd like to get contacts created on January 1st, 2020 you should
        search with a created_at value equal (=) to 1577836800 (January 1st,
        2020 12:00 AM).

        This behaviour applies only to timestamps used in search queries. The
        search results will still contain the full UNIX timestamp and be sorted
        accordingly.


        ### Accepted Fields


        Most key listed as part of the Contacts Model are searchable, whether
        writeable or not. The value you search for has to match the accepted
        type, otherwise the query will fail (ie. as `created_at` accepts a date,
        the `value` cannot be a string such as `"foorbar"`).


        | Field                              | Type                           |

        | ---------------------------------- | ------------------------------ |

        | id                                 | String                         |

        | role                               | String<br>Accepts user or lead |

        | name                               | String                         |

        | avatar                             | String                         |

        | owner_id                           | Integer                        |

        | email                              | String                         |

        | email_domain                       | String                         |

        | phone                              | String                         |

        | formatted_phone                    | String                         |

        | external_id                        | String                         |

        | created_at                         | Date (UNIX Timestamp)          |

        | signed_up_at                       | Date (UNIX Timestamp)          |

        | updated_at                         | Date (UNIX Timestamp)          |

        | last_seen_at                       | Date (UNIX Timestamp)          |

        | last_contacted_at                  | Date (UNIX Timestamp)          |

        | last_replied_at                    | Date (UNIX Timestamp)          |

        | last_email_opened_at               | Date (UNIX Timestamp)          |

        | last_email_clicked_at              | Date (UNIX Timestamp)          |

        | language_override                  | String                         |

        | browser                            | String                         |

        | browser_language                   | String                         |

        | os                                 | String                         |

        | location.country                   | String                         |

        | location.region                    | String                         |

        | location.city                      | String                         |

        | unsubscribed_from_emails           | Boolean                        |

        | marked_email_as_spam               | Boolean                        |

        | has_hard_bounced                   | Boolean                        |

        | ios_last_seen_at                   | Date (UNIX Timestamp)          |

        | ios_app_version                    | String                         |

        | ios_device                         | String                         |

        | ios_app_device                     | String                         |

        | ios_os_version                     | String                         |

        | ios_app_name                       | String                         |

        | ios_sdk_version                    | String                         |

        | android_last_seen_at               | Date (UNIX Timestamp)          |

        | android_app_version                | String                         |

        | android_device                     | String                         |

        | android_app_name                   | String                         |

        | andoid_sdk_version                 | String                         |

        | segment_id                         | String                         |

        | tag_id                             | String                         |

        | custom_attributes.{attribute_name} | String                         |


        ### Accepted Operators


        {% admonition type="warning" name="Searching based on `created_at`" %}
          You cannot use the `<=` or `>=` operators to search by `created_at`.
        {% /admonition %}


        The table below shows the operators you can use to define how you want
        to search for the value.  The operator should be put in as a string
        (`"="`). The operator has to be compatible with the field's type (eg.
        you cannot search with `>` for a given string value as it's only
        compatible for integer's and dates).


        | Operator | Valid Types                      |
        Description                                                      |

        | :------- | :------------------------------- |
        :--------------------------------------------------------------- |

        | =        | All                              |
        Equals                                                           |

        | !=       | All                              | Doesn't
        Equal                                                    |

        | IN       | All                              | In<br>Shortcut for `OR`
        queries<br>Values must be in Array       |

        | NIN      | All                              | Not In<br>Shortcut for
        `OR !` queries<br>Values must be in Array |

        | >        | Integer<br>Date (UNIX Timestamp) | Greater
        than                                                     |

        | <       | Integer<br>Date (UNIX Timestamp) | Lower
        than                                                       |

        | ~        | String                           |
        Contains                                                         |

        | !~       | String                           | Doesn't
        Contain                                                  |

        | ^        | String                           | Starts
        With                                                      |

        | $        | String                           | Ends
        With                                                        |
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: list
                    data: []
                    total_count: 0
                    pages:
                      type: pages
                      page: 1
                      per_page: 5
                      total_pages: 0
              schema:
                $ref: '#/components/schemas/contact_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 2b28c47b-8fa3-4e17-8fc1-6ba80f1cc844
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/search_request'
            examples:
              successful:
                summary: successful
                value:
                  query:
                    operator: AND
                    value:
                      - field: created_at
                        operator: '>'
                        value: '**********'
                  pagination:
                    per_page: 5
  /accounts:
    get:
      summary: List all accounts
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Accounts
      description: You can fetch a list of accounts ( ChatApp ADMIN).
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                allOf:
                  $ref: '#/components/schemas/account'
                  prevCursor:
                    type: string
                    nullable: true
                  nextCursor:
                    type: string
                    nullable: true
                  

        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: d5bac7ff-7961-4fe5-8aed-6f1b031b38af
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
    post:
      summary: Create contact
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Contacts
      operationId: CreateContact
      description: You can create a new contact (ie. user or lead).
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: contact
                    id: 667d60b08a68186f43bafdbf
                    workspace_id: this_is_an_id272_that_should_be_at_least_
                    external_id: null
                    role: user
                    email: <EMAIL>
                    phone: null
                    name: null
                    avatar: null
                    owner_id: null
                    social_profiles:
                      type: list
                      data: []
                    has_hard_bounced: false
                    marked_email_as_spam: false
                    unsubscribed_from_emails: false
                    created_at: 1719492784
                    updated_at: 1719492784
                    signed_up_at: null
                    last_seen_at: null
                    last_replied_at: null
                    last_contacted_at: null
                    last_email_opened_at: null
                    last_email_clicked_at: null
                    language_override: null
                    browser: null
                    browser_version: null
                    browser_language: null
                    os: null
                    location:
                      type: location
                      country: null
                      region: null
                      city: null
                      country_code: null
                      continent_code: null
                    android_app_name: null
                    android_app_version: null
                    android_device: null
                    android_os_version: null
                    android_sdk_version: null
                    android_last_seen_at: null
                    ios_app_name: null
                    ios_app_version: null
                    ios_device: null
                    ios_os_version: null
                    ios_sdk_version: null
                    ios_last_seen_at: null
                    custom_attributes: {}
                    tags:
                      type: list
                      data: []
                      url: /contacts/667d60b08a68186f43bafdbf/tags
                      total_count: 0
                      has_more: false
                    notes:
                      type: list
                      data: []
                      url: /contacts/667d60b08a68186f43bafdbf/notes
                      total_count: 0
                      has_more: false
                    companies:
                      type: list
                      data: []
                      url: /contacts/667d60b08a68186f43bafdbf/companies
                      total_count: 0
                      has_more: false
                    opted_out_subscription_types:
                      type: list
                      data: []
                      url: /contacts/667d60b08a68186f43bafdbf/subscriptions
                      total_count: 0
                      has_more: false
                    opted_in_subscription_types:
                      type: list
                      data: []
                      url: /contacts/667d60b08a68186f43bafdbf/subscriptions
                      total_count: 0
                      has_more: false
                    utm_campaign: null
                    utm_content: null
                    utm_medium: null
                    utm_source: null
                    utm_term: null
                    referrer: null
              schema:
                $ref: '#/components/schemas/contact'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: c18ca0d4-ad2c-41e7-9a71-1df806f9c954
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/create_contact_request'
            examples:
              successful:
                summary: successful
                value:
                  email: <EMAIL>
          
                  
#region MESSAGES
  /conversations/{conversation_id}/tags:
    post:
      summary: Add tag to a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: conversation_id
          in: path
          description: conversation_id
          example: '64619700005694'
          required: true
          schema:
            type: string
      tags:
        - Tags
        - Conversations
      operationId: attachTagToConversation
      description: >-
        You can tag a specific conversation. This will return a tag object for
        the tag that was added to the conversation.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: tag
                    id: '99'
                    name: Manual tag
              schema:
                $ref: '#/components/schemas/tag'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 95cacea0-4744-4de8-a2bf-da4419f75732
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Conversation not found
          content:
            application/json:
              examples:
                Conversation not found:
                  value:
                    type: error.list
                    request_id: 840d35aa-2414-402a-b3c6-763a410e0d16
                    errors:
                      - code: not_found
                        message: Conversation not found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - admin_id
              properties:
                id:
                  type: string
                  description: The unique identifier for the tag which is given by Intercom
                  example: '7522907'
                admin_id:
                  type: string
                  description: >-
                    The unique identifier for the admin which is given by
                    Intercom.
                  example: '780'
            examples:
              successful:
                summary: successful
                value:
                  id: 99
                  admin_id: 991267526
              conversation_not_found:
                summary: Conversation not found
                value:
                  id: 100
                  admin_id: 991267528
  /conversations/{conversation_id}/tags/{id}:
    delete:
      summary: Remove tag from a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: conversation_id
          in: path
          description: conversation_id
          example: '64619700005694'
          required: true
          schema:
            type: string
        - name: id
          in: path
          description: id
          example: '7522907'
          required: true
          schema:
            type: string
      tags:
        - Tags
        - Conversations
      operationId: detachTagFromConversation
      description: >-
        You can remove tag from a specific conversation. This will return a tag
        object for the tag that was removed from the conversation.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: tag
                    id: '102'
                    name: Manual tag
              schema:
                $ref: '#/components/schemas/tag'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: f083d6b1-e9d2-43b3-86df-67539007fc3e
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Tag not found
          content:
            application/json:
              examples:
                Conversation not found:
                  value:
                    type: error.list
                    request_id: f78f63ba-911d-47b8-a389-b33e3ccbe77e
                    errors:
                      - code: not_found
                        message: Conversation not found
                Tag not found:
                  value:
                    type: error.list
                    request_id: 0d00d069-2cf1-496f-b887-a4db74ee320d
                    errors:
                      - code: tag_not_found
                        message: Tag not found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - admin_id
              properties:
                admin_id:
                  type: string
                  description: >-
                    The unique identifier for the admin which is given by
                    Intercom.
                  example: '123'
            examples:
              successful:
                summary: successful
                value:
                  admin_id: 991267530
              conversation_not_found:
                summary: Conversation not found
                value:
                  admin_id: 991267532
              tag_not_found:
                summary: Tag not found
                value:
                  admin_id: 991267533
  /conversations:
    get:
      summary: List all conversations
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: per_page
          in: query
          schema:
            type: integer
            default: 20
            maximum: 150
          required: false
          description: How many results per page
        - name: starting_after
          in: query
          required: false
          description: String used to get the next page of conversations.
          schema:
            type: string
      tags:
        - Conversations
      operationId: listConversations
      description: >
        You can fetch a list of all conversations.


        You can optionally request the result page size and the cursor to start
        after to fetch the result.

        {% admonition type="warning" name="Pagination" %}
          You can use pagination to limit the number of results returned. The default is `20` results per page.
          See the [pagination section](https://developers.chat.com/docs/build-an-integration/learn-more/rest-apis/pagination/#pagination-for-list-apis) for more details on how to use the `starting_after` param.
        {% /admonition %}
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: conversation.list
                    pages:
                      type: pages
                      page: 1
                      per_page: 20
                      total_pages: 1
                    total_count: 1
                    conversations:
                      - type: conversation
                        id: '335'
                        created_at: 1719492795
                        updated_at: 1719492795
                        waiting_since: null
                        snoozed_until: null
                        source:
                          type: conversation
                          id: '403918241'
                          delivered_as: admin_initiated
                          subject: ''
                          body: <p>this is the message body</p>
                          author:
                            type: admin
                            id: '991267536'
                            name: Ciaran143 Lee
                            email: <EMAIL>
                          attachments: []
                          url: null
                          redacted: false
                        contacts:
                          type: contact.list
                          contacts:
                            - type: contact
                              id: 667d60bb8a68186f43bafdc5
                              external_id: '70'
                        first_contact_reply: null
                        admin_assignee_id: null
                        team_assignee_id: null
                        open: false
                        state: closed
                        read: false
                        tags:
                          type: tag.list
                          tags: []
                        priority: not_priority
                        sla_applied: null
                        statistics: null
                        conversation_rating: null
                        teammates: null
                        title: null
                        custom_attributes: {}
                        topics: {}
                        ticket: null
                        linked_objects:
                          type: list
                          data: []
                          total_count: 0
                          has_more: false
                        ai_agent: null
                        ai_agent_participated: false
              schema:
                $ref: '#/components/schemas/paginated_response'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 18db64a8-7a08-4967-9ec6-0416178306f9
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: a91eac55-8d70-454d-a01d-c6bb875aaa35
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
    post:
      summary: Creates a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Conversations
      operationId: createConversation
      description: >+
        You can create a conversation that has been initiated by a contact (ie.
        user or lead).

        The conversation can be an in-app message only.


        {% admonition type="info" name="Sending for visitors" %}

        You can also send a message from a visitor by specifying their `user_id`
        or `id` value in the `from` field, along with a `type` field value of
        `contact`.

        This visitor will be automatically converted to a contact with a lead
        role once the conversation is created.

        {% /admonition %}


        This will return the Message model that has been created.

      responses:
        '200':
          description: conversation created
          content:
            application/json:
              examples:
                conversation created:
                  value:
                    type: user_message
                    id: '403918251'
                    created_at: 1719492819
                    body: Hello there
                    message_type: inapp
                    conversation_id: '363'
              schema:
                $ref: '#/components/schemas/message'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: af9757fc-4e1d-463c-ac9d-788503f04a95
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: c7a35217-6720-48bd-a2ae-c2acb5adea30
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact Not Found
          content:
            application/json:
              examples:
                Contact Not Found:
                  value:
                    type: error.list
                    request_id: ed0ab0c5-57b8-4413-a7a9-bbc134b40876
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/create_conversation_request'
            examples:
              conversation_created:
                summary: conversation created
                value:
                  from:
                    type: user
                    id: 667d60d18a68186f43bafddd
                  body: Hello there
              contact_not_found:
                summary: Contact Not Found
                value:
                  from:
                    type: user
                    id: 123_doesnt_exist
                  body: Hello there
  /conversations/{id}:
    get:
      summary: Retrieve a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The id of the conversation to target
          example: 123
          schema:
            type: integer
        - name: display_as
          in: query
          required: false
          description: Set to plaintext to retrieve conversation messages in plain text.
          example: plaintext
          schema:
            type: string
      tags:
        - Conversations
      operationId: retrieveConversation
      description: >

        You can fetch the details of a single conversation.


        This will return a single Conversation model with all its conversation
        parts.


        {% admonition type="warning" name="Hard limit of 500 parts" %}

        The maximum number of conversation parts that can be returned via the
        API is 500. If you have more than that we will return the 500 most
        recent conversation parts.

        {% /admonition %}


        For AI agent conversation metadata, please note that you need to have
        the agent enabled in your workspace, which is a [paid
        feature](https://www.chat.com/help/en/articles/8205718-fin-resolutions#h_97f8c2e671).
      responses:
        '200':
          description: conversation found
          content:
            application/json:
              examples:
                conversation found:
                  value:
                    type: conversation
                    id: '367'
                    created_at: 1719492825
                    updated_at: 1719492825
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918255'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267553'
                        name: Ciaran153 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60d88a68186f43bafde1
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: false
                    state: closed
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts: []
                      total_count: 0
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: b1f6adfd-f7da-4880-8d11-d842235126ae
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: c7b0a10f-d482-4352-8d7b-1ad26b902473
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Not found
          content:
            application/json:
              examples:
                Not found:
                  value:
                    type: error.list
                    request_id: 978c1e65-1eba-4995-9acb-ff8f33b283e3
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
    put:
      summary: Update a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The id of the conversation to target
          example: 123
          schema:
            type: integer
        - name: display_as
          in: query
          required: false
          description: Set to plaintext to retrieve conversation messages in plain text.
          example: plaintext
          schema:
            type: string
      tags:
        - Conversations
      operationId: updateConversation
      description: >+

        You can update an existing conversation.


        {% admonition type="info" name="Replying and other actions" %}

        If you want to reply to a coveration or take an action such as assign,
        unassign, open, close or snooze, take a look at the reply and manage
        endpoints.

        {% /admonition %}

      responses:
        '200':
          description: conversation found
          content:
            application/json:
              examples:
                conversation found:
                  value:
                    type: conversation
                    id: '371'
                    created_at: 1719492832
                    updated_at: 1719492834
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918259'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267561'
                        name: Ciaran157 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60e08a68186f43bafde5
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: false
                    state: closed
                    read: true
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes:
                      issue_type: Billing
                      priority: High
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '96'
                          part_type: conversation_attribute_updated_by_admin
                          body: null
                          created_at: 1719492834
                          updated_at: 1719492834
                          notified_at: 1719492834
                          assigned_to: null
                          author:
                            id: '991267562'
                            type: bot
                            name: Operator
                            email: >-
                              <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                        - type: conversation_part
                          id: '97'
                          part_type: conversation_attribute_updated_by_admin
                          body: null
                          created_at: 1719492834
                          updated_at: 1719492834
                          notified_at: 1719492834
                          assigned_to: null
                          author:
                            id: '991267562'
                            type: bot
                            name: Operator
                            email: >-
                              <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 2
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 58e6b9ee-4a28-4597-9c20-faf34b6894dc
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: cf6fb162-88c9-45ec-9f97-c3fcad93b7c1
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Not found
          content:
            application/json:
              examples:
                Not found:
                  value:
                    type: error.list
                    request_id: e4c692dd-cccd-46bf-834a-cda7a3a9029c
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update_conversation_request'
            examples:
              conversation_found:
                summary: conversation found
                value:
                  read: true
                  custom_attributes:
                    issue_type: Billing
                    priority: High
              not_found:
                summary: Not found
                value:
                  read: true
                  custom_attributes:
                    issue_type: Billing
                    priority: High
  /conversations/search:
    post:
      summary: Search conversations
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Conversations
      operationId: searchConversations
      description: >
        You can search for multiple conversations by the value of their
        attributes in order to fetch exactly which ones you want.


        To search for conversations, you need to send a `POST` request to
        `https://api.chat.io/conversations/search`.


        This will accept a query object in the body which will define your
        filters in order to search for conversations.

        {% admonition type="warning" name="Optimizing search queries" %}
          Search queries can be complex, so optimizing them can help the performance of your search.
          Use the `AND` and `OR` operators to combine multiple filters to get the exact results you need and utilize
          pagination to limit the number of results returned. The default is `20` results per page and maximum is `150`.
          See the [pagination section](https://developers.chat.com/docs/build-an-integration/learn-more/rest-apis/pagination/#example-search-conversations-request) for more details on how to use the `starting_after` param.
        {% /admonition %}


        ### Nesting & Limitations


        You can nest these filters in order to get even more granular insights
        that pinpoint exactly what you need. Example: (1 OR 2) AND (3 OR 4).

        There are some limitations to the amount of multiple's there can be:

        - There's a limit of max 2 nested filters

        - There's a limit of max 15 filters for each AND or OR group


        ### Accepted Fields


        Most keys listed as part of the The conversation model is searchable,
        whether writeable or not. The value you search for has to match the
        accepted type, otherwise the query will fail (ie. as `created_at`
        accepts a date, the `value` cannot be a string such as `"foorbar"`).

        The `source.body` field is unique as the search will not be performed
        against the entire value, but instead against every element of the value
        separately. For example, when searching for a conversation with a `"I
        need support"` body - the query should contain a `=` operator with the
        value `"support"` for such conversation to be returned. A query with a
        `=` operator and a `"need support"` value will not yield a result.


        | Field                                     |
        Type                                                                                                                                                  
        |

        | :---------------------------------------- |
        :-----------------------------------------------------------------------------------------------------------------------------------------------------
        |

        | id                                        |
        String                                                                                                                                                
        |

        | created_at                                | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | updated_at                                | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | source.type                               | String<br>Accepted fields
        are `conversation`, `email`, `facebook`, `instagram`, `phone_call`,
        `phone_switch`, `push`, `sms`, `twitter` and `whatsapp`. |

        | source.id                                 |
        String                                                                                                                                                
        |

        | source.delivered_as                       |
        String                                                                                                                                                
        |

        | source.subject                            |
        String                                                                                                                                                
        |

        | source.body                               |
        String                                                                                                                                                
        |

        | source.author.id                          |
        String                                                                                                                                                
        |

        | source.author.type                        |
        String                                                                                                                                                
        |

        | source.author.name                        |
        String                                                                                                                                                
        |

        | source.author.email                       |
        String                                                                                                                                                
        |

        | source.url                                |
        String                                                                                                                                                
        |

        | contact_ids                               |
        String                                                                                                                                                
        |

        | teammate_ids                              |
        String                                                                                                                                                
        |

        | admin_assignee_id                         |
        String                                                                                                                                                
        |

        | team_assignee_id                          |
        String                                                                                                                                                
        |

        | channel_initiated                         |
        String                                                                                                                                                
        |

        | open                                      |
        Boolean                                                                                                                                               
        |

        | read                                      |
        Boolean                                                                                                                                               
        |

        | state                                     |
        String                                                                                                                                                
        |

        | waiting_since                             | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | snoozed_until                             | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | tag_ids                                   |
        String                                                                                                                                                
        |

        | priority                                  |
        String                                                                                                                                                
        |

        | statistics.time_to_assignment             |
        Integer                                                                                                                                               
        |

        | statistics.time_to_admin_reply            |
        Integer                                                                                                                                               
        |

        | statistics.time_to_first_close            |
        Integer                                                                                                                                               
        |

        | statistics.time_to_last_close             |
        Integer                                                                                                                                               
        |

        | statistics.median_time_to_reply           |
        Integer                                                                                                                                               
        |

        | statistics.first_contact_reply_at         | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.first_assignment_at            | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.first_admin_reply_at           | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.first_close_at                 | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_assignment_at             | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_assignment_admin_reply_at | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_contact_reply_at          | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_admin_reply_at            | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_close_at                  | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | statistics.last_closed_by_id              |
        String                                                                                                                                                
        |

        | statistics.count_reopens                  |
        Integer                                                                                                                                               
        |

        | statistics.count_assignments              |
        Integer                                                                                                                                               
        |

        | statistics.count_conversation_parts       |
        Integer                                                                                                                                               
        |

        | conversation_rating.requested_at          | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | conversation_rating.replied_at            | Date (UNIX
        timestamp)                                                                                                                                 
        |

        | conversation_rating.score                 |
        Integer                                                                                                                                               
        |

        | conversation_rating.remark                |
        String                                                                                                                                                
        |

        | conversation_rating.contact_id            |
        String                                                                                                                                                
        |

        | conversation_rating.admin_d               |
        String                                                                                                                                                
        |

        | ai_agent_participated                     |
        Boolean                                                                                                                                               
        |

        | ai_agent.resolution_state                 |
        String                                                                                                                                                
        |

        | ai_agent.last_answer_type                 |
        String                                                                                                                                                
        |

        | ai_agent.rating                           |
        Integer                                                                                                                                               
        |

        | ai_agent.rating_remark                    |
        String                                                                                                                                                
        |

        | ai_agent.source_type                      |
        String                                                                                                                                                
        |

        | ai_agent.source_title                     |
        String                                                                                                                                                
        |


        ### Accepted Operators


        The table below shows the operators you can use to define how you want
        to search for the value.  The operator should be put in as a string
        (`"="`). The operator has to be compatible with the field's type  (eg.
        you cannot search with `>` for a given string value as it's only
        compatible for integer's and dates).


        | Operator | Valid Types                    |
        Description                                                  |

        | :------- | :----------------------------- |
        :----------------------------------------------------------- |

        | =        | All                            |
        Equals                                                       |

        | !=       | All                            | Doesn't
        Equal                                                |

        | IN       | All                            | In  Shortcut for `OR`
        queries  Values most be in Array       |

        | NIN      | All                            | Not In  Shortcut for `OR
        !` queries  Values must be in Array |

        | >        | Integer  Date (UNIX Timestamp) | Greater (or equal)
        than                                      |

        | <       | Integer  Date (UNIX Timestamp) | Lower (or equal)
        than                                        |

        | ~        | String                         |
        Contains                                                     |

        | !~       | String                         | Doesn't
        Contain                                              |

        | ^        | String                         | Starts
        With                                                  |

        | $        | String                         | Ends
        With                                                    |
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: conversation.list
                    pages:
                      type: pages
                      page: 1
                      per_page: 5
                      total_pages: 1
                    total_count: 1
                    conversations:
                      - type: conversation
                        id: '378'
                        created_at: 1719492843
                        updated_at: 1719492843
                        waiting_since: null
                        snoozed_until: null
                        source:
                          type: conversation
                          id: '403918266'
                          delivered_as: admin_initiated
                          subject: ''
                          body: <p>this is the message body</p>
                          author:
                            type: admin
                            id: '991267591'
                            name: Ciaran180 Lee
                            email: <EMAIL>
                          attachments: []
                          url: null
                          redacted: false
                        contacts:
                          type: contact.list
                          contacts:
                            - type: contact
                              id: 667d60ea8a68186f43bafdec
                              external_id: '70'
                        first_contact_reply: null
                        admin_assignee_id: null
                        team_assignee_id: null
                        open: false
                        state: closed
                        read: false
                        tags:
                          type: tag.list
                          tags: []
                        priority: not_priority
                        sla_applied: null
                        statistics: null
                        conversation_rating: null
                        teammates: null
                        title: null
                        custom_attributes: {}
                        topics: {}
                        ticket: null
                        linked_objects:
                          type: list
                          data: []
                          total_count: 0
                          has_more: false
                        ai_agent: null
                        ai_agent_participated: false
              schema:
                $ref: '#/components/schemas/conversation_list'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/search_request'
            examples:
              successful:
                summary: successful
                value:
                  query:
                    operator: AND
                    value:
                      - field: created_at
                        operator: '>'
                        value: '**********'
                  pagination:
                    per_page: 5
  /conversations/{id}/reply:
    post:
      summary: Reply to a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: >-
            The Intercom provisioned identifier for the conversation or the
            string "last" to reply to the last part of the conversation
          example: 123 or "last"
          schema:
            type: string
      tags:
        - Conversations
      operationId: replyConversation
      description: >-
        You can reply to a conversation with a message from an admin or on
        behalf of a contact, or with a note for admins.
      responses:
        '200':
          description: User last conversation reply
          content:
            application/json:
              examples:
                User reply:
                  value:
                    type: conversation
                    id: '387'
                    created_at: 1719492849
                    updated_at: 1719492850
                    waiting_since: 1719492850
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918269'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267594'
                        name: Ciaran182 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60f18a68186f43bafdf4
                          external_id: '70'
                    first_contact_reply:
                      created_at: 1719492850
                      type: conversation
                      url: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: true
                    state: open
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '99'
                          part_type: open
                          body: <p>Thanks again :)</p>
                          created_at: 1719492850
                          updated_at: 1719492850
                          notified_at: 1719492850
                          assigned_to: null
                          author:
                            id: 667d60f18a68186f43bafdf4
                            type: user
                            name: Joe Bloggs
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
                Admin note reply:
                  value:
                    type: conversation
                    id: '388'
                    created_at: 1719492852
                    updated_at: 1719492853
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918270'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267596'
                        name: Ciaran183 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60f38a68186f43bafdf5
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: false
                    state: closed
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '100'
                          part_type: note
                          body: |-
                            <h2>An Unordered HTML List</h2>
                            <ul>
                            <li>Coffee</li>
                            <li>Tea</li>
                            <li>Milk</li>
                            </ul>
                            <h2>An Ordered HTML List</h2>
                            <ol>
                            <li>Coffee</li>
                            <li>Tea</li>
                            <li>Milk</li>
                            </ol>
                          created_at: 1719492853
                          updated_at: 1719492853
                          notified_at: 1719492853
                          assigned_to: null
                          author:
                            id: '991267596'
                            type: admin
                            name: Ciaran183 Lee
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
                User last conversation reply:
                  value:
                    type: conversation
                    id: '390'
                    created_at: 1719492855
                    updated_at: 1719492856
                    waiting_since: 1719492856
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918272'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267600'
                        name: Ciaran185 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60f78a68186f43bafdf7
                          external_id: '70'
                    first_contact_reply:
                      created_at: 1719492856
                      type: conversation
                      url: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: true
                    state: open
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '101'
                          part_type: open
                          body: <p>Thanks again :)</p>
                          created_at: 1719492856
                          updated_at: 1719492856
                          notified_at: 1719492856
                          assigned_to: null
                          author:
                            id: 667d60f78a68186f43bafdf7
                            type: user
                            name: Joe Bloggs
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 7435aa28-13bd-40b1-ba99-66009e92a1ba
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: 9fe5809c-cf0b-4a0f-af80-9913d3beb1eb
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Not found
          content:
            application/json:
              examples:
                Not found:
                  value:
                    type: error.list
                    request_id: 8193a639-aba8-4b0e-9fdd-ee48807e3ee7
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/reply_conversation_request'
            examples:
              user_reply:
                summary: User reply
                value:
                  message_type: comment
                  type: user
                  chat_user_id: 667d60f18a68186f43bafdf4
                  body: Thanks again :)
              admin_note_reply:
                summary: Admin note reply
                value:
                  message_type: note
                  type: admin
                  admin_id: 991267596
                  body: >-
                    <html> <body>  <h2>An Unordered HTML List</h2>  <ul>  
                    <li>Coffee</li>   <li>Tea</li>   <li>Milk</li> </ul>   
                    <h2>An Ordered HTML List</h2>  <ol>   <li>Coffee</li>  
                    <li>Tea</li>   <li>Milk</li> </ol>   </body> </html>
              user_last_conversation_reply:
                summary: User last conversation reply
                value:
                  message_type: comment
                  type: user
                  chat_user_id: 667d60f78a68186f43bafdf7
                  body: Thanks again :)
              not_found:
                summary: Not found
                value:
                  message_type: comment
                  type: user
                  chat_user_id: 667d60f98a68186f43bafdf8
                  body: Thanks again :)
  /conversations/{id}/parts:
    post:
      summary: Manage a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The identifier for the conversation as given by Intercom.
          example: '123'
          schema:
            type: string
      tags:
        - Conversations
      operationId: manageConversation
      description: |
        For managing conversations you can:
        - Close a conversation
        - Snooze a conversation to reopen on a future date
        - Open a conversation which is `snoozed` or `closed`
        - Assign a conversation to an admin and/or team.
      responses:
        '200':
          description: Assign a conversation
          content:
            application/json:
              examples:
                Close a conversation:
                  value:
                    type: conversation
                    id: '394'
                    created_at: 1719492862
                    updated_at: 1719492862
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918276'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267608'
                        name: Ciaran189 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60fd8a68186f43bafdfb
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: false
                    state: closed
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '102'
                          part_type: close
                          body: <p>Goodbye :)</p>
                          created_at: 1719492862
                          updated_at: 1719492862
                          notified_at: 1719492862
                          assigned_to: null
                          author:
                            id: '991267608'
                            type: admin
                            name: Ciaran189 Lee
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
                Snooze a conversation:
                  value:
                    type: conversation
                    id: '395'
                    created_at: 1719492864
                    updated_at: 1719492864
                    waiting_since: null
                    snoozed_until: 1719496464
                    source:
                      type: conversation
                      id: '403918277'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267610'
                        name: Ciaran190 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d60ff8a68186f43bafdfc
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: true
                    state: snoozed
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '103'
                          part_type: snoozed
                          body: null
                          created_at: 1719492864
                          updated_at: 1719492864
                          notified_at: 1719492864
                          assigned_to: null
                          author:
                            id: '991267610'
                            type: admin
                            name: Ciaran190 Lee
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
                Open a conversation:
                  value:
                    type: conversation
                    id: '400'
                    created_at: 1719492863
                    updated_at: 1719492873
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918278'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267612'
                        name: Ciaran191 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d61038a68186f43bafe01
                          external_id: '74'
                    first_contact_reply: null
                    admin_assignee_id: null
                    team_assignee_id: null
                    open: true
                    state: open
                    read: true
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: ''
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '105'
                          part_type: open
                          body: null
                          created_at: 1719492873
                          updated_at: 1719492873
                          notified_at: 1719492873
                          assigned_to: null
                          author:
                            id: '991267612'
                            type: admin
                            name: Ciaran191 Lee
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
                Assign a conversation:
                  value:
                    type: conversation
                    id: '405'
                    created_at: 1719492874
                    updated_at: 1719492875
                    waiting_since: null
                    snoozed_until: null
                    source:
                      type: conversation
                      id: '403918281'
                      delivered_as: admin_initiated
                      subject: ''
                      body: <p>this is the message body</p>
                      author:
                        type: admin
                        id: '991267615'
                        name: Ciaran193 Lee
                        email: <EMAIL>
                      attachments: []
                      url: null
                      redacted: false
                    contacts:
                      type: contact.list
                      contacts:
                        - type: contact
                          id: 667d610a8a68186f43bafe05
                          external_id: '70'
                    first_contact_reply: null
                    admin_assignee_id: 991267615
                    team_assignee_id: null
                    open: true
                    state: open
                    read: false
                    tags:
                      type: tag.list
                      tags: []
                    priority: not_priority
                    sla_applied: null
                    statistics: null
                    conversation_rating: null
                    teammates: null
                    title: null
                    custom_attributes: {}
                    topics: {}
                    ticket: null
                    linked_objects:
                      type: list
                      data: []
                      total_count: 0
                      has_more: false
                    ai_agent: null
                    ai_agent_participated: false
                    conversation_parts:
                      type: conversation_part.list
                      conversation_parts:
                        - type: conversation_part
                          id: '106'
                          part_type: assign_and_reopen
                          body: null
                          created_at: 1719492875
                          updated_at: 1719492875
                          notified_at: 1719492875
                          assigned_to:
                            type: admin
                            id: '991267615'
                          author:
                            id: '991267615'
                            type: admin
                            name: Ciaran193 Lee
                            email: <EMAIL>
                          attachments: []
                          external_id: null
                          redacted: false
                      total_count: 1
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: ae0581d2-199e-437c-bf51-1eb9fe2e12fc
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: a7e069bb-f013-45bc-8e0a-f58c3de4e034
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Not found
          content:
            application/json:
              examples:
                Not found:
                  value:
                    type: error.list
                    request_id: 1bfc14e1-07ef-4999-9448-f029b112cf1b
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/close_conversation_request'
                - $ref: '#/components/schemas/snooze_conversation_request'
                - $ref: '#/components/schemas/open_conversation_request'
                - $ref: '#/components/schemas/assign_conversation_request'
            examples:
              close_a_conversation:
                summary: Close a conversation
                value:
                  message_type: close
                  type: admin
                  admin_id: 991267608
                  body: Goodbye :)
              snooze_a_conversation:
                summary: Snooze a conversation
                value:
                  message_type: snoozed
                  admin_id: 991267610
                  snoozed_until: 1719496464
              open_a_conversation:
                summary: Open a conversation
                value:
                  message_type: open
                  admin_id: 991267612
              assign_a_conversation:
                summary: Assign a conversation
                value:
                  message_type: assignment
                  type: admin
                  admin_id: 991267615
                  assignee_id: 991267615
              not_found:
                summary: Not found
                value:
                  message_type: close
                  type: admin
                  admin_id: 991267617
                  body: Goodbye :)
  
  /conversations/{id}/customers:
    post:
      summary: Attach a contact to a conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The identifier for the conversation as given by Intercom.
          example: '123'
          schema:
            type: string
      tags:
        - Conversations
      operationId: attachContactToConversation
      description: >+
        You can add participants who are contacts to a conversation, on behalf
        of either another contact or an admin.


        {% admonition type="warning" name="Contacts without an email" %}

        If you add a contact via the email parameter and there is no user/lead
        found on that workspace with he given email, then we will create a new
        contact with `role` set to `lead`.

        {% /admonition %}

      responses:
        '200':
          description: Attach a contact to a conversation
          content:
            application/json:
              examples:
                Attach a contact to a conversation:
                  value:
                    customers:
                      - type: user
                        id: 667d61168a68186f43bafe0d
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 4f00c4c6-a8f7-436e-bf95-d1adfa315906
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: 3d1c3371-6ba4-4d5a-9368-ac983292136d
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Not found
          content:
            application/json:
              examples:
                Not found:
                  value:
                    type: error.list
                    request_id: 1fd64942-5bf0-4a51-a6cb-db4a778bb1f4
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/attach_contact_to_conversation_request'
            examples:
              attach_a_contact_to_a_conversation:
                summary: Attach a contact to a conversation
                value:
                  admin_id: 991267631
                  customer:
                    chat_user_id: 667d61168a68186f43bafe0d
              not_found:
                summary: Not found
                value:
                  admin_id: 991267633
                  customer:
                    chat_user_id: 667d61188a68186f43bafe0e
  /conversations/{conversation_id}/customers/{contact_id}:
    delete:
      summary: Detach a contact from a group conversation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: conversation_id
          in: path
          required: true
          description: The identifier for the conversation as given by Intercom.
          example: '123'
          schema:
            type: string
        - name: contact_id
          in: path
          required: true
          description: The identifier for the contact as given by Intercom.
          example: '123'
          schema:
            type: string
      tags:
        - Conversations
      operationId: detachContactFromConversation
      description: >+
        You can add participants who are contacts to a conversation, on behalf
        of either another contact or an admin.


        {% admonition type="warning" name="Contacts without an email" %}

        If you add a contact via the email parameter and there is no user/lead
        found on that workspace with he given email, then we will create a new
        contact with `role` set to `lead`.

        {% /admonition %}

      responses:
        '200':
          description: Detach a contact from a group conversation
          content:
            application/json:
              examples:
                Detach a contact from a group conversation:
                  value:
                    customers:
                      - type: user
                        id: 667d61228a68186f43bafe19
              schema:
                $ref: '#/components/schemas/conversation'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: d35f1b37-765c-4afe-8738-81c0560710a6
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: d30f18d4-2e0a-4528-a66b-4590b733713c
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Contact not found
          content:
            application/json:
              examples:
                Conversation not found:
                  value:
                    type: error.list
                    request_id: 579f0f7a-d773-41d6-9d36-8cc0b3fbcc41
                    errors:
                      - code: not_found
                        message: Resource Not Found
                Contact not found:
                  value:
                    type: error.list
                    request_id: 44531412-2973-4b92-b14d-80abac5c1b4d
                    errors:
                      - code: not_found
                        message: User Not Found
              schema:
                $ref: '#/components/schemas/error'
        '422':
          description: Last customer
          content:
            application/json:
              examples:
                Last customer:
                  value:
                    type: error.list
                    request_id: 35a0444f-8a1e-40e9-a5fc-dd1ae2df8bc6
                    errors:
                      - code: parameter_invalid
                        message: Removing the last customer is not allowed
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/detach_contact_from_conversation_request'
            examples:
              detach_a_contact_from_a_group_conversation:
                summary: Detach a contact from a group conversation
                value:
                  admin_id: 991267639
                  customer:
                    chat_user_id: 667d611c8a68186f43bafe11
              conversation_not_found:
                summary: Conversation not found
                value:
                  admin_id: 991267642
                  customer:
                    chat_user_id: 667d61248a68186f43bafe1a
              contact_not_found:
                summary: Contact not found
                value:
                  admin_id: 991267645
                  customer:
                    chat_user_id: 667d612b8a68186f43bafe22
              last_customer:
                summary: Last customer
                value:
                  admin_id: 991267648
                  customer:
                    chat_user_id: 667d61338a68186f43bafe2a
 
  
  /events:
    post:
      summary: Submit a data event
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Data Events
      operationId: createDataEvent
      description: >+

        You will need an Access Token that has write permissions to send Events.
        Once you have a key you can submit events via POST to the Events
        resource, which is located at https://api.chat.io/events, or you can
        send events using one of the client libraries. When working with the
        HTTP API directly a client should send the event with a `Content-Type`
        of `application/json`.


        When using the JavaScript API, [adding the code to your
        app](http://docs.chat.io/configuring-Intercom/tracking-user-events-in-your-app)
        makes the Events API available. Once added, you can submit an event
        using the `trackEvent` method. This will associate the event with the
        Lead or currently logged-in user or logged-out visitor/lead and send it
        to Intercom. The final parameter is a map that can be used to send
        optional metadata about the event.


        With the Ruby client you pass a hash describing the event to
        `Intercom::Event.create`, or call the `track_user` method directly on
        the current user object (e.g. `user.track_event`).


        **NB: For the JSON object types, please note that we do not currently
        support nested JSON structure.**


        | Type            |
        Description                                                                                                                                                                                                    
        |
        Example                                                                          
        |

        | :-------------- |
        :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
        |
        :--------------------------------------------------------------------------------
        |

        | String          | The value is a JSON
        String                                                                                                                                                                                     
        |
        `"source":"desktop"`                                                             
        |

        | Number          | The value is a JSON
        Number                                                                                                                                                                                     
        | `"load":
        3.67`                                                                   
        |

        | Date            | The key ends with the String `_date` and the value
        is a [Unix timestamp](http://en.wikipedia.org/wiki/Unix_time), assumed
        to be in the
        [UTC](http://en.wikipedia.org/wiki/Coordinated_Universal_Time) timezone.
        | `"contact_date":
        1392036272`                                                      |

        | Link            | The value is a HTTP or HTTPS
        URI.                                                                                                                                                                              
        | `"article":
        "https://example.org/ab1de.html"`                                     |

        | Rich Link       | The value is a JSON object that contains `url` and
        `value`
        keys.                                                                                                                                               
        | `"article": {"url": "https://example.org/ab1de.html", "value":"the
        dude abides"}` |

        | Monetary Amount | The value is a JSON object that contains `amount`
        and `currency` keys. The `amount` key is a positive integer representing
        the amount in cents. The price in the example to the right denotes
        €349.99.          | `"price": {"amount": 34999, "currency":
        "eur"}`                                   |


        **Lead Events**


        When submitting events for Leads, you will need to specify the Lead's
        `id`.


        **Metadata behaviour**


        - We currently limit the number of tracked metadata keys to 10 per
        event. Once the quota is reached, we ignore any further keys we receive.
        The first 10 metadata keys are determined by the order in which they are
        sent in with the event.

        - It is not possible to change the metadata keys once the event has been
        sent. A new event will need to be created with the new keys and you can
        archive the old one.

        - There might be up to 24 hrs delay when you send a new metadata for an
        existing event.


        **Event de-duplication**


        The API may detect and ignore duplicate events. Each event is uniquely
        identified as a combination of the following data - the Workspace
        identifier, the Contact external identifier, the Data Event name and the
        Data Event created time. As a result, it is **strongly recommended** to
        send a second granularity Unix timestamp in the `created_at` field.


        Duplicated events are responded to using the normal `202 Accepted` code
        - an error is not thrown, however repeat requests will be counted
        against any rate limit that is in place.


        ### HTTP API Responses


        - Successful responses to submitted events return `202 Accepted` with an
        empty body.

        - Unauthorised access will be rejected with a `401 Unauthorized` or `403
        Forbidden` response code.

        - Events sent about users that cannot be found will return a `404 Not
        Found`.

        - Event lists containing duplicate events will have those duplicates
        ignored.

        - Server errors will return a `500` response code and may contain an
        error message in the body.

      responses:
        '202':
          description: successful
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: c6b3dcbd-33be-4a80-abb4-c5b3315250d0
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/create_data_event_request'
    get:
      summary: List all data events
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - in: query
          name: filter
          required: true
          style: form
          explode: true
          schema:
            type: object
            oneOf:
              - title: user_id query parameter
                properties:
                  user_id:
                    type: string
                required:
                  - user_id
                additionalProperties: false
              - title: chat_user_id query parameter
                properties:
                  chat_user_id:
                    type: string
                required:
                  - chat_user_id
                additionalProperties: false
              - title: email query parameter
                properties:
                  email:
                    type: string
                required:
                  - email
                additionalProperties: false
        - name: type
          in: query
          required: true
          description: The value must be user
          schema:
            type: string
        - name: summary
          in: query
          required: false
          description: summary flag
          schema:
            type: boolean
      tags:
        - Data Events
      operationId: lisDataEvents
      description: >

        > 🚧

        >

        > Please note that you can only 'list' events that are less than 90 days
        old. Event counts and summaries will still include your events older
        than 90 days but you cannot 'list' these events individually if they are
        older than 90 days


        The events belonging to a customer can be listed by sending a GET
        request to `https://api.chat.io/events` with a user or lead
        identifier along with a `type` parameter. The identifier parameter can
        be one of `user_id`, `email` or `chat_user_id`. The `type` parameter
        value must be `user`.


        - `https://api.chat.io/events?type=user&user_id={user_id}`

        - `https://api.chat.io/events?type=user&email={email}`

        - `https://api.chat.io/events?type=user&chat_user_id={id}` (this
        call can be used to list leads)


        The `email` parameter value should be [url
        encoded](http://en.wikipedia.org/wiki/Percent-encoding) when sending.


        You can optionally define the result page size as well with the
        `per_page` parameter.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              examples:
                Successful response:
                  value:
                    type: event.summary
                    events: []
                    pages:
                      next: http://api.chat.test/events?next page
                    email: <EMAIL>
                    chat_user_id: 667d61648a68186f43bafe4b
                    user_id: 3ecf64d0-9ed1-4e9f-88e1-da7d6e6782f3
              schema:
                $ref: '#/components/schemas/data_event_summary'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: bfdcc6de-2dcb-4725-acc7-232c10838586
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
 
  /messages:
    post:
      summary: Create a message
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Messages
      description: You can create a message to be sent to a room. The recipient of a message is always a room.


       
      responses:
        '200':
          description: message created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/message'
        '400':
          description: No body supplied for email message
          content:
            application/json:
              examples:
                No body and files supplied for message:
                  value:
                    type: error.list
                    request_id: 4a52a34f-c7e2-4e70-adf2-ea7f41beb2a1
                    errors:
                      - code: parameter_invalid
                        message: Body is required if files array is empty 
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: ee3ea56e-d0ce-47db-871a-57740e165e5c
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '403':
          description: API plan restricted
          content:
            application/json:
              examples:
                API plan restricted:
                  value:
                    type: error.list
                    request_id: fab7034a-78bd-4413-a652-8f38783e7bf1
                    errors:
                      - code: api_plan_restricted
                        message: Active subscription needed.
              schema:
                $ref: '#/components/schemas/error'
        '422':
          description: No subject supplied for email message
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createMessage'
            examples:
              example_message:
                summary: An example of message
                value:
                  parentId: null
                  roomId: 667d616e8a68186f43bafe55
                  content:
                    text: 'Hello there'
                    files: []
                  from:
                    id: 667d616e8a68186f43bafe55
                    type: guest
                  to:
                    type: guest
                    id: 667d616e8a68186f43bafe55
                  mentions:
                    type: group
                    mentionedUserIds: []
  
  /messages/{id}:
    put:
      summary: Update a mesage in a room
      parameters:
        - name: id
          in: path
          description: The unique identifier of a room
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      tags:
        - Rooms
      description: |
          You can use this endpoint to update a room.

          **NOTE**: If the room duplicates the name of an existing room, a duplicate name
          will be created e.g If there is a room with the name `Joan's Launch Party - August 2025`,
          a duplicate name will be created for the room as `Joan's Launch Party - August 2025 (1)`.
      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/room'
        '400':
          description: Invalid parameters
          content:
            application/json:
              examples:
                Invalid parameters:
                  value:
                    type: error.list
                    request_id: a7afe3c5-be52-4b69-9268-50ef1d917a1b
                    errors:
                      - code: parameter_invalid
                        message: invalid tag parameters
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3609e8b1-a6aa-4c57-a994-3d95743f20a2
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/updateRoom'

# region ROOMS
  /rooms:
    get:
      summary: List all Rooms in an application
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Rooms
      description: You can fetch a list of all rooms for a given application.

      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/room'
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: c83a3efe-433a-4555-a5e2-e393588be29f
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
    post:
      summary: Create a room in an application
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Rooms
      description: |
          You can use this endpoint to create a new room.

          **NOTE**: If the new room duplicates the name of an existing room, a duplicate name
          will be created e.g If there is a room with the name `Joan's Launch Party - August 2025`,
          a duplicate name will be created for the new room as `Joan's Launch Party - August 2025 (1)`.
      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/room'
        '400':
          description: Invalid parameters
          content:
            application/json:
              examples:
                Invalid parameters:
                  value:
                    type: error.list
                    request_id: a7afe3c5-be52-4b69-9268-50ef1d917a1b
                    errors:
                      - code: parameter_invalid
                        message: invalid tag parameters
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3609e8b1-a6aa-4c57-a994-3d95743f20a2
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/createRoom'
    
  /rooms/{id}:
    put:
      summary: Update a room in an application
      parameters:
        - name: id
          in: path
          description: The unique identifier of a room
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      tags:
        - Rooms
      description: |
          You can use this endpoint to update a room.

          **NOTE**: If the room duplicates the name of an existing room, a duplicate name
          will be created e.g If there is a room with the name `Joan's Launch Party - August 2025`,
          a duplicate name will be created for the room as `Joan's Launch Party - August 2025 (1)`.
      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/room'
        '400':
          description: Invalid parameters
          content:
            application/json:
              examples:
                Invalid parameters:
                  value:
                    type: error.list
                    request_id: a7afe3c5-be52-4b69-9268-50ef1d917a1b
                    errors:
                      - code: parameter_invalid
                        message: invalid tag parameters
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3609e8b1-a6aa-4c57-a994-3d95743f20a2
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/updateRoom'
    get:
      summary: Get a room in an application
      parameters:
        - name: id
          in: path
          description: The unique identifier of a room
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      tags:
        - Rooms
      description: You can use this endpoint to retrieve a room.
      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/room'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
    delete:
      summary: Delete a room in an application
      parameters:
        - name: id
          in: path
          description: The unique identifier of a room
          example: 5ba682d23d7cf92bef87bfd4
          required: true
          schema:
            type: string
      tags:
        - Rooms
      description: You can use this endpoint to delete a room.
      responses:
        '200':
          description: Action successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'


  /tags/{id}:
    get:
      summary: Find a specific tag
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: The unique identifier of a given tag
          example: '123'
          required: true
          schema:
            type: string
      tags:
        - Tags
      operationId: findTag
      description: |
        You can fetch the details of tags that are on the workspace by their id.
        This will return a tag object.
      responses:
        '200':
          description: Tag found
          content:
            application/json:
              examples:
                Tag found:
                  value:
                    type: tag
                    id: '126'
                    name: Manual tag
              schema:
                $ref: '#/components/schemas/tag'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 76a1d79d-3f0d-49bb-8d15-38d1ae6df738
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Tag not found
          content:
            application/json:
              examples:
                Tag not found:
                  value:
                    type: error.list
                    request_id: 20b89fb6-f224-4f81-98ca-4cb4b36df959
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
    delete:
      summary: Delete tag
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: The unique identifier of a given tag
          example: '123'
          required: true
          schema:
            type: string
      tags:
        - Tags
      operationId: deleteTag
      description: >-
        You can delete the details of tags that are on the workspace by passing
        in the id.
      responses:
        '200':
          description: Successful
        '400':
          description: Tag has dependent objects
          content:
            application/json:
              examples:
                Tag has dependent objects:
                  value:
                    type: error.list
                    request_id: 41086388-9b3b-4e07-9633-502b9b10c926
                    errors:
                      - code: tag_has_dependent_objects
                        message: >-
                          Unable to delete Tag with dependent objects. Segments:
                          Seg 1.
              schema:
                $ref: '#/components/schemas/error'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 3dcedf54-ed30-4337-8992-94e36900e695
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Resource not found
          content:
            application/json:
              examples:
                Resource not found:
                  value:
                    type: error.list
                    request_id: 111586bb-ad78-43b9-b0a0-bf864d9a3744
                    errors:
                      - code: not_found
                        message: Resource Not Found
              schema:
                $ref: '#/components/schemas/error'
  /teams:
    get:
      summary: List all teams
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Teams
      operationId: listTeams
      description: This will return a list of team objects for the App.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: team.list
                    teams: []
              schema:
                $ref: '#/components/schemas/team_list'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 006b2c8f-9a29-463e-be69-ad213576aee6
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
  /teams/{id}:
    get:
      summary: Retrieve a team
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          required: true
          description: The unique identifier of a given team.
          example: '123'
          schema:
            type: string
      tags:
        - Teams
      operationId: retrieveTeam
      description: >-
        You can fetch the details of a single team, containing an array of
        admins that belong to this team.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    type: team
                    id: '991267802'
                    name: team 1
                    admin_ids: []
              schema:
                $ref: '#/components/schemas/team'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 110ad8e4-99ed-461a-bd93-92a5cdbaa6b2
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Team not found
          content:
            application/json:
              examples:
                Team not found:
                  value:
                    type: error.list
                    request_id: 4745dfce-7275-4864-abfd-44e0d84bf52a
                    errors:
                      - code: team_not_found
                        message: Team not found
              schema:
                $ref: '#/components/schemas/error'
   
# region GUESTS
  /guests:
    post:
      summary: Create a guest
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Guests
      description: >
        Sending a POST request to `/guests` will result in the creation
        of a new guest.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    id: 667d61cc8a68186f43bafe95
                    externalId: 5ba682d23d7cf92bef87bfd4
                    companyId: 5ba682d23d7cf92bef87bfd4
                    anonymous: false
                    email: <EMAIL>
                    phone: null
                    name: Gareth Bale
                    pseudonym: Indigo Ghost
                    avatar:
                      fileUrl: https://static.chatassets.com/app/pseudonym_avatars_2019/indigo-ghost.png
                      filename: 'indigo.png'
                    lastRepliedAt: '2024-12-31T16:43:37.724Z'
                    location:
                      country: Ghana
                      region: Kuwait
                      city: Paris
                    lastSeenAt: '2024-12-31T16:43:37.724Z'
                    createdAt: '2024-12-31T16:43:37.724Z'
                    updatedAt: '2024-12-31T16:43:37.724Z'
                    markedEmailAsSpam: false
                    hasHardBounced: false
              schema:
                $ref: '#/components/schemas/guest'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update_visitor_request'
            examples:
              successful:
                summary: successful
                value:
                  id: 667d61cc8a68186f43bafe95
                  name: Gareth Bale
              visitor_not_found:
                summary: visitor Not Found
                value:
                  user_id: fail
                  name: Christian Fail
    get:
      summary: Get guests in an applcation
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
      tags:
        - Guests
      description: Get guests in an application
      responses:
        '200':
          description: successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/guest'
                  prevCursor:
                    type: string
                  nextCursor:
                    type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 82df3971-fb7c-410d-a919-e8bc1b3d991a
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
  
  /guests/{id}:
    put:
      summary: Update a guest
      parameters:
        - name: Intercom-Version
          in: header
          schema:
            $ref: '#/components/schemas/chat_version'
        - name: id
          in: path
          description: The id of the Guest you want to update.
          required: true
          schema:
            type: string
      tags:
        - Guests
      description: >
        Sending a PUT request to `/guests` will result in an update of an
        existing Guest.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    id: 667d61cc8a68186f43bafe95
                    externalId: 5ba682d23d7cf92bef87bfd4
                    companyId: 5ba682d23d7cf92bef87bfd4
                    anonymous: false
                    email: <EMAIL>
                    phone: null
                    name: Gareth Bale
                    pseudonym: Indigo Ghost
                    avatar:
                      fileUrl: https://static.chatassets.com/app/pseudonym_avatars_2019/indigo-ghost.png
                      filename: 'indigo.png'
                    lastRepliedAt: '2024-12-31T16:43:37.724Z'
                    location:
                      country: Ghana
                      region: Kuwait
                      city: Paris
                    lastSeenAt: '2024-12-31T16:43:37.724Z'
                    createdAt: '2024-12-31T16:43:37.724Z'
                    updatedAt: '2024-12-31T16:43:37.724Z'
                    markedEmailAsSpam: false
                    hasHardBounced: false
              schema:
                $ref: '#/components/schemas/guest'
        '401':
          description: Unauthorized
          content:
            application/json:
              examples:
                Unauthorized:
                  value:
                    type: error.list
                    request_id: 82df3971-fb7c-410d-a919-e8bc1b3d991a
                    errors:
                      - code: unauthorized
                        message: Access Token Invalid
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: visitor Not Found
          content:
            application/json:
              examples:
                visitor Not Found:
                  value:
                    type: error.list
                    request_id: 1f35dc86-17d2-4bfe-8cb1-9afa74adc24c
                    errors:
                      - code: not_found
                        message: Visitor Not Found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update_visitor_request'

    get:
      summary: Retrieve a guest
      parameters:
        - name: id
          in: path
          description: The id of the Visitor you want to retrieve.
          required: true
          schema:
            type: string
      tags:
        - Guests
      description: You can fetch the details of a single guest.
      responses:
        '200':
          description: successful
          content:
            application/json:
              examples:
                successful:
                  value:
                    id: 667d61cc8a68186f43bafe95
                    externalId: 5ba682d23d7cf92bef87bfd4
                    companyId: 5ba682d23d7cf92bef87bfd4
                    anonymous: false
                    email: <EMAIL>
                    phone: null
                    name: Gareth Bale
                    pseudonym: Indigo Ghost
                    avatar:
                      fileUrl: https://static.chatassets.com/app/pseudonym_avatars_2019/indigo-ghost.png
                      filename: 'indigo.png'
                    lastRepliedAt: '2024-12-31T16:43:37.724Z'
                    location:
                      country: Ghana
                      region: Kuwait
                      city: Paris
                    lastSeenAt: '2024-12-31T16:43:37.724Z'
                    createdAt: '2024-12-31T16:43:37.724Z'
                    updatedAt: '2024-12-31T16:43:37.724Z'
                    markedEmailAsSpam: false
                    hasHardBounced: false
              schema:
                $ref: '#/components/schemas/guest'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Visitor not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
  /guests/{id}/away:
    put:
      summary: Set a guest to away
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of a given guest
          schema:
            type: string
      tags:
        - Guest
      description: You can set a Guest as away.
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  awayModeEnabled:
                    type: boolean
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
        '404':
          description: Admin not found
          content:
            application/json:
              examples:
                Admin not found:
                  value:
                    type: error.list
                    request_id: 9818bd03-9cc6-4ab8-8e7c-20a45ac58e97
                    errors:
                      - code: admin_not_found
                        message: Admin for admin_id not found
              schema:
                $ref: '#/components/schemas/error'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - away_mode_enabled
                - away_mode_reassign
              properties:
                away_mode_enabled:
                  type: boolean
                  description: Set to "true" to change the status of the admin to away.
                  example: true
                  default: true
                away_mode_reassign:
                  type: boolean
                  description: >-
                    Set to "true" to assign any new conversation replies to your
                    default inbox.
                  example: false
                  default: false
            examples:
              successful_response:
                summary: Successful response
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
              admin_not_found:
                summary: Admin not found
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
              unauthorized:
                summary: Unauthorized
                value:
                  away_mode_enabled: true
                  away_mode_reassign: true
 
#region COMPONENTS
components:
  schemas:
    
    activity_log:
      title: Activity Log
      type: object
      description: Activities performed by Users.
      nullable: true
      properties:
        id:
          type: string
          description: The id representing the activity.
          example: '6'
        performedBy:
          type: object
          description: Details about the Admin involved in the activity.
          properties:
            type:
              type: string
              description: >-
                String representing the object's type. Always has the value
                `user`.
              example: user
            id:
              type: string
              description: The id representing the admin.
              example: '1295'
            email:
              type: string
              description: The email of the admin.
              example: <EMAIL>
            ip:
              type: string
              description: The IP address of the admin.
              example: **************
        metadata:
          $ref: '#/components/schemas/activity_log_metadata'
        createdAt:
          type: integer
          format: date-time
          description: The time the activity was created.
          example: 1671028894
        activity_type:
          type: string
          enum:
            - user_away_mode_change
            - user_deletion
            - user_deprovisioned
            - user_impersonation_end
            - user_impersonation_start
            - user_invite_change
            - user_invite_creation
            - user_invite_deletion
            - user_login_failure
            - user_login_success
            - user_logout
            - user_password_reset_request
            - user_password_reset_success
            - user_permission_change
            - user_provisioned
            - user_two_factor_auth_change
            - user_unauthorized_sign_in_method
            - app_user_join
            - app_authentication_method_change
            - app_data_deletion
            - app_data_export
            - app_google_sso_domain_change
            - app_identity_verification_change
            - app_name_change
            - app_outbound_address_change
            - app_package_installation
            - app_package_token_regeneration
            - app_package_uninstallation
            - app_team_creation
            - app_team_deletion
            - app_team_membership_modification
            - app_timezone_change
            - app_webhook_creation
            - app_webhook_deletion
            - articles_in_messenger_enabled_change
            - bulk_delete
            - bulk_export
            - room_name_change
            - conversation_topic_change
            - conversation_topic_creation
            - conversation_topic_deletion
            - help_center_settings_change
            - message_deletion
            - message_state_change
            - messenger_look_and_feel_change
            - messenger_search_required_change
            - messenger_spaces_change
            - office_hours_change
            - role_change
            - role_creation
            - role_deletion
            - ruleset_activation_title_preview
            - ruleset_creation
            - ruleset_deletion
            - search_browse_enabled_change
            - search_browse_required_change
            - security_settings_change
            - temporary_expectation_change
            - upfront_email_collection_change
            - welcome_message_change
          example: app_name_change
        activity_description:
          type: string
          description: A sentence or two describing the activity.
          example: Admin updated the app's name to "My App".
    
    activity_log_list:
      title: Paginated Response
      type: object
      description: A paginated list of activity logs.
      properties:
        type:
          type: string
          description: >-
            String representing the object's type. Always has the value
            `activity_log.list`.
          example: activity_log.list
        pages:
          $ref: '#/components/schemas/cursorPages'
        activity_logs:
          type: array
          description: An array of activity logs
          items:
            $ref: '#/components/schemas/activity_log'
    
    activity_log_metadata:
      title: Activity Log Metadata
      type: object
      description: Additional data provided about Admin activity.
      nullable: true
      properties:
        sign_in_method:
          type: string
          nullable: true
          description: The way the admin signed in.
          example: email_password
        external_id:
          type: string
          nullable: true
          description: >-
            The unique identifier for the contact which is provided by the
            Client.
          example: f3b87a2e09d514c6c2e79b9a
        away_mode:
          type: boolean
          nullable: true
          description: >-
            The away mode status which is set to true when away and false when
            returned.
          example: true
        away_status_reason:
          type: string
          nullable: true
          description: The reason the Admin is away.
          example: 😌 On a break
        reassign_conversations:
          type: boolean
          nullable: true
          description: >-
            Indicates if conversations should be reassigned while an Admin is
            away.
          example: false
        source:
          type: string
          nullable: true
          description: The action that initiated the status change.
          example: 'admin update from web - Admin id: 93'
        auto_changed:
          type: string
          nullable: true
          description: Indicates if the status was changed automatically or manually.
          example: false
        update_by:
          type: integer
          nullable: true
          description: The ID of the Admin who initiated the activity.
          example: 93
        update_by_name:
          type: string
          nullable: true
          description: The name of the Admin who initiated the activity.
          example: Joe Bloggs
    
    account:
      title: Account
      type: object
      x-tags:
        - Accounts
      description: >
        An account is an entity on ChatApp that describes a business or an organization. It is the highest
        level that other entities, Applications, Users, and Guests, are contained in.
      nullable: false
      properties:
        id:
          type: string
          description: The id representing the account.
          example: 65ea80ee37ac531683834248
        name:
          type: string
          description: The name of the account.
          example: SportyExpats
        email:
          type: string
          description: The email of the account.
          example: <EMAIL>
        subscriptionType:
          type: string
          enum: [free, basic, standard, premimum]
        companiesCount:
          type: integer
          description: The total number of companies under the account.
          example: 3
        applicationsCount:
          type: integer
          description: The total number of applications under the account.
          example: 4
        usersCount:
          type: integer
          description: The total number of users under the account.
          example: 5
        guestsCount:
          type: integer
          description: The total number of guests under the account.
          example: 78
    
    accountUpdate:
      title: Account
      type: object
      x-tags:
        - Accounts
      description: Object to update an account
      nullable: false
      properties:
        name:
          type: string
          description: The name of the account.
          example: SportyExpats
        companiesCount:
          type: integer
          description: The total number of companies under the account.
          example: 3
        applicationsCount:
          type: integer
          description: The total number of applications under the account.
          example: 4
        usersCount:
          type: integer
          description: The total number of users under the account.
          example: 5
        guestsCount:
          type: integer
          description: The total number of guests under the account.
          example: 78
    
    user:
      title: Users
      type: object
      x-tags:
        - Users
      description: >
        Users are members of a company with high level access to an account. They can be admininistrators
        or chat-agents
      nullable: true
      properties:
        role:
          type: string
          enum:
            - admin
            - chatAgent
          description: String representing the user's role.
          example: admin
        id:
          type: string
          description: The id representing the user.
          example: 65ea80ee37ac531683834248
        name:
          type: string
          description: The name of the user.
          example: Joe Examplee
        email:
          type: string
          description: The email of the user.
          example: <EMAIL>
        pseudonym:
          type: string
          nullable: true
          description: The nickname of the user
          example: Super User One
        companyId:
          type: string
          description: The id of the company to which the user belongs.
          example: 65ea80ee37ac531683834248
        externalId:
          type: string
          nullable: true
          description: The unique identifier for the user which is provided by the Client.
          example: 680c53ab6c842dbac3e5ba21
        awayModeEnabled:
          type: boolean
          description: Identifies if this user is currently set in away mode.
          example: false
        avatar:
          type: object
          properties:
            filename:
              type: string
            fileUrl:
              type: string
              format: uri
          nullable: true
          description: Image for the associated user
          example: https://picsum.photos/200/300
       
        
    user_with_company:
      title: User
      type: object
      description: Users are the teammate accounts that have access to a company
      nullable: true
      properties:
        id:
          type: string
          description: The id representing the admin.
          example: '1295'
        name:
          type: string
          description: The name of the admin.
          example: Joe Examplee
        email:
          type: string
          description: The email of the admin.
          example: <EMAIL>
        jobTitle:
          type: string
          description: The job title of the user.
          example: Philosopher
        awayModeEnabled:
          type: boolean
          description: Identifies if this user is currently set in away mode.
          example: false
        away_mode_reassign:
          type: boolean
          description: >-
            Identifies if this admin is set to automatically reassign new
            conversations to the apps default inbox.
          example: false
        has_inbox_seat:
          type: boolean
          description: >-
            Identifies if this admin has a paid inbox seat to restrict/allow
            features that require them.
          example: true
        avatar:
          type: object
          description: This object represents the avatar associated with the user.
          properties:
            filename:
              type: string
              example: mydogImage
            fileUrl:
              type: string
              format: uri
              nullable: true
              description: This object represents the avatar associated with the admin.
              example: https://example.com/avatar.png
        emailVerified:
          type: boolean
          description: Identifies if this user's email is verified.
          nullable: true
          example: true
        app:
          $ref: '#/components/schemas/app'
          nullable: true
          description: App that the user belongs to.
    
   
    app:
      title: App
      type: object
      description: >
        App is a workspace on ChatApp. An app is connected to a company.
        A company can have multiple apps.
      nullable: true
      properties:
        type:
          type: string
          description: The type of application
          default: app
          example: app
          enum:
            - personal
            - room
        id:
          type: string
          description: The id of the app.
          example: 65ea80ee37ac531683834248
        name:
          type: string
          description: The name of the app.
          example: SportyExpats App One

        createdAt:
          type: string
          format: date-time
          description: When the app was created.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: string
          format: date-time
          description: When the app was last updated.
          example: '2024-12-31T16:43:37.724Z'
        companyId:
          type: string
          description: The id of the company which the application belongs to.
          example: 65ea80ee37ac531683834248
        usersCount:
          type: integer
          description: The total number of Users under the application.
          example: 5
        guestsCount:
          type: integer
          description: The total number of Guests under the application.
          example: 5
    
    createApp:
      title: App
      type: object
      description: Object to create an app
      nullable: true
      properties:
        type:
          type: string
          description: The type of application
          default: app
          example: app
          enum:
            - personal
            - room
        name:
          type: string
          description: The name of the app.
          example: SportyExpats App One
        companyId:
          type: string
          description: The id of the company which the application belongs to.
          example: 65ea80ee37ac531683834248
  
    
    company:
      title: Company
      type: object
      x-tags:
        - Companies
      description: >-
        Companies allow you to represent organizations using your product. Each
        company will have its own description and be associated with users, guests, and applications.
        You can fetch, create, update and list companies.
        A company is linked to an `Account` on ChatApp.
      properties:
        id:
          type: string
          description: The ChatApp defined id representing the company.
          example: 65ea80ee37ac531683834248
        name:
          type: string
          description: The name of the company.
          example: TaxDone Company Inc.
        externalId:
          type: string
          description: >
            An external unique ID you have defined for the company.
          example: '65ea80ee37ac531683834248'
        accountId:
          type: string
          description: >
            ID of the account on ChatApp the company is connected to.
          example: '65ea80ee37ac531683834248'
        applicationsCount:
          type: integer
          description: >
            The total number of applications under the company.
          example: 5
        usersCount:
          type: integer
          description: The total number of Users under the company.
          example: 10
        guestsCount:
          type: integer
          description: The total number of Guests under the company.
          example: 120
        
        createdAt:
          type: integer
          description: The time the company was added in ChatApp.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: integer
          description: The last time the company was updated.
          example: '2024-12-31T16:43:37.724Z'
        lastRequestAt:
          type: integer
          description: The time the company last recorded making a request.
          example: '2024-12-31T16:43:37.724Z'
        website:
          type: string
          nullable: true
          description: The URL for the company website.
          example: https://www.chat.com
        industry:
          type: string
          description: The industry that the company operates in.
          example: Software
        monthlySpend:
          type: integer
          description: How much revenue the company generates for your business.
          example: 100
       
        tags:
          type: object
          description: The list of tags associated with the company
          properties:
            type:
              type: string
              description: The type of the object
              enum:
                - tag.list
            tags:
              type: array
              items:
                items:
                  $ref: '#/components/schemas/tag'
    
    guest:
      title: Guest
      type: object
      x-tags:
        - Contacts
      description: >
        Guests are the objects that represent customers/visitors in ChatApp.
        They are part of a company.
      properties:
        id:
          type: string
          description: The unique identifier for the contact which is given by ChatApp.
          example: 5ba682d23d7cf92bef87bfd4
        externalId:
          type: string
          nullable: true
          description: >-
            The unique identifier for the contact which is provided by the
            Company.
          example: 680c53ab6c842dbac3e5ba21
        applicationId:
          type: string
          description: The id of the application which the guest belongs to.
          example: 65ea80ee37ac531683834248
        email:
          type: string
          format: email
          description: The contact's email.
          example: <EMAIL>
        phone:
          type: string
          nullable: true
          description: The contacts phone.
          example: '+1123456789'
        name:
          type: string
          nullable: false
          description: The guest's name.
          example: John Doe
        anonymous:
          type: boolean
          description: Identifies if this visitor is anonymous.
          example: false
        pseudonym:
          type: string
          nullable: true
          description: A unique nickname of the guest in the chat.
          example: Red Duck from Dublin
        hasHardBounced:
          type: boolean
          description: Whether the guest has had an email sent to them hard bounce.
          example: true
        markedEmailAsSpam:
          type: boolean
          description: Whether the admin/chat-agent has marked an email of a guest as spam.
          example: true
        createdAt:
          type: string
          format: date-time
          description: The time when the guest was created.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: string
          format: date-time
          description: The time when the guest was last updated.
          example: '2024-12-31T16:43:37.724Z'
        lastSeenAt:
          type: string
          format: date-time
          nullable: true
          description: >-
            The time when the guest was last seen (either
            where the Intercom Messenger was installed or when specified
            manually).
          example: '2024-12-31T16:43:37.724Z'
        language:
          type: string
          nullable: true
          description: >-
            A preferred language setting for the guest, used by the ChatApp
            Messenger even if their browser settings change.
          example: en
        browser:
          type: string
          nullable: true
          description: The name of the browser which the guest is using.
          example: Chrome
        browserVersion:
          type: string
          nullable: true
          description: The version of the browser which the guest is using.
          example: 80.0.3987.132
        browserLanguage:
          type: string
          nullable: true
          description: The language set by the browser which the contact is using.
          example: en-US
        os:
          type: string
          nullable: true
          description: The operating system which the guest is using.
          example: Mac OS X
        avatar:
          type: object
          description: This object represents the avatar associated with the guest.
          properties:
            filename:
              type: string
              description: The name of the file
              example: avatar
              nullable: false
            fileUrl:
              type: string
              format: uri
              nullable: false
              description: This url of the file.
              example: https://example.com/avatar.png
        location:
          $ref: '#/components/schemas/location'
    
    location:
      title: Location
      type: object
      nullable: false
      description: An object containing location meta data about a ChatApp guest.
      properties:
        country:
          type: string
          nullable: true
          description: The country that the guest is located in
          example: Ireland
        region:
          type: string
          nullable: true
          description: The overal region that the gust is located in
          example: Paris
        city:
          type: string
          nullable: true
          description: The city that the guest is located in
          example: Paris
    
    conversation:
      title: Conversation
      type: object
      x-tags:
        - Conversations
      description: >-
        Conversations are how you can communicate with users in ChatApp in a controlled way. They
        are created when a guest replies to an outbound message, or when one
        admin directly sends a message to a single guest.
      properties:
        type:
          type: string
          description: Always conversation.
          example: conversation
        id:
          type: string
          description: The id representing the conversation.
          example: 67df5cd9288d56b51ef53276
        title:
          type: string
          nullable: true
          description: The title given to the conversation.
          example: Event Planning and Payment
        createdAt:
          type: string
          format: date-time
          description: The time the conversation was created.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: atring
          format: date-time
          description: The last time the conversation was updated.
          example: '2024-12-31T16:43:37.724Z'
        waitingSince:
          type: string
          format: date-time
          nullable: true
          description: >-
            The last time a Guest responded to an Admin. In other words, the
            time a customer started waiting for a response. Set to null if last
            reply is from an Admin/ChatAgent.
          example: '2024-12-31T16:43:37.724Z'
        snoozedUntil:
          type: string
          format: date-time
          nullable: true
          description: >-
            If set this is the time in the future when this conversation will be
            marked as open. i.e. it will be in a snoozed state until this time.
            i.e. it will be in a snoozed state until this time.
          example: '2024-12-31T16:43:37.724Z'
        open:
          type: boolean
          description: Indicates whether a conversation is open (true) or closed (false).
          example: true
        state:
          type: string
          enum:
            - open
            - closed
            - snoozed
          description: Can be set to "open", "closed" or "snoozed".
          example: snoozed
        read:
          type: boolean
          description: Indicates whether a conversation has been read.
          example: true
        priority:
          type: string
          enum:
            - priority
            - not_priority
          description: If marked as priority, then it is an important conversation. it will return priority or else not_priority.
          example: priority
        adminAssigneeId:
          type: integer
          nullable: true
          description: >-
            The id of the admin assigned to the conversation. If it's not
            assigned to an admin it will return null.
          example: 0
        team_assignee_id:
          type: string
          nullable: true
          description: >-
            The id of the team assigned to the conversation. If it's not
            assigned to a team it will return null.
          example: '5017691'
        tags:
          $ref: '#/components/schemas/tags'
        conversationRating:
          $ref: '#/components/schemas/conversationRating'
        source:
          $ref: '#/components/schemas/conversation_source'
        contacts:
          $ref: '#/components/schemas/conversation_contacts'
        teammates:
          $ref: '#/components/schemas/conversation_teammates'
        custom_attributes:
          $ref: '#/components/schemas/custom_attributes'
        first_contact_reply:
          $ref: '#/components/schemas/conversation_first_contact_reply'
        sla_applied:
          $ref: '#/components/schemas/sla_applied'
        statistics:
          $ref: '#/components/schemas/conversation_statistics'
        conversation_parts:
          $ref: '#/components/schemas/conversation_parts'
        linked_objects:
          $ref: '#/components/schemas/linked_object_list'
        aiAgentParticipated:
          type: boolean
          description: Indicates whether the AI Agent participated in the conversation.
          example: true
        aiAgent:
          $ref: '#/components/schemas/aiAgent'
          nullable: true
   
    conversation_part:
      title: Conversation Part
      type: object
      description: A Conversation Part represents a message in the conversation.
      properties:
        type:
          type: string
          description: Always conversation_part
          example: conversation_part
        id:
          type: string
          description: The id representing the conversation part.
          example: '3'
        part_type:
          type: string
          description: The type of conversation part.
          example: comment
        body:
          type: string
          nullable: true
          description: >-
            The message body, which may contain HTML. For Twitter, this will
            show a generic message regarding why the body is obscured.
          example: <p>Okay!</p>
        created_at:
          type: integer
          format: date-time
          description: The time the conversation part was created.
          example: 1663597223
        updated_at:
          type: integer
          format: date-time
          description: The last time the conversation part was updated.
          example: 1663597260
        notified_at:
          type: integer
          format: date-time
          description: The time the user was notified with the conversation part.
          example: 1663597260
        assigned_to:
          $ref: '#/components/schemas/reference'
          nullable: true
          description: >-
            The id of the admin that was assigned the conversation by this
            conversation_part (null if there has been no change in assignment.)
        author:
          $ref: '#/components/schemas/conversation_part_author'
        attachments:
          title: Conversation part attachments
          type: array
          description: A list of attachments for the part.
          items:
            $ref: '#/components/schemas/part_attachment'
        external_id:
          type: string
          nullable: true
          description: The external id of the conversation part
          example: abcd1234
        redacted:
          type: boolean
          description: Whether or not the conversation part has been redacted.
          example: false
    
            
    conversationRating:
      title: Conversation Rating
      type: object
      nullable: true
      description: >-
        The Conversation Rating object which contains information on the rating
        and/or remark added by a Guest and the Admin assigned to the
        conversation.
      properties:
        rating:
          type: integer
          description: The rating, between 1 and 5, for the conversation.
          example: 5
        remark:
          type: string
          nullable: true
          description: An optional field to add a remark to correspond to the number rating
          example: ''
        createdAt:
          type: integer
          format: date-time
          description: The time the rating was requested in the conversation being rated.
          example: '2024-12-31T16:43:37.724Z'
        guest:
          $ref: '#/components/schemas/contact_reference'
        teammate:
          $ref: '#/components/schemas/reference'
    
    conversation_source:
      title: Conversation source
      type: object
      description: >-
        The Conversation Part that originated this conversation, which can be
        Contact, Admin, Campaign, Automated or Operator initiated.
      properties:
        type:
          type: string
          description: >-
            This includes conversation, email, facebook, instagram, phone_call,
            phone_switch, push, sms, twitter and whatsapp.
          example: conversation
        id:
          type: string
          description: The id representing the message.
          example: '3'
        delivered_as:
          type: string
          description: >-
            The conversation's initiation type. Possible values are
            customer_initiated, campaigns_initiated (legacy campaigns),
            operator_initiated (Custom bot), automated (Series and other
            outbounds with dynamic audience message) and admin_initiated (fixed
            audience message, ticket initiated by an admin, group email).
          example: operator_initiated
        subject:
          type: string
          description: >-
            Optional. The message subject. For Twitter, this will show a generic
            message regarding why the subject is obscured.
          example: ''
        body:
          type: string
          description: >-
            The message body, which may contain HTML. For Twitter, this will
            show a generic message regarding why the body is obscured.
          example: <p>Hey there!</p>
        author:
          $ref: '#/components/schemas/conversation_part_author'
        attachments:
          type: array
          description: A list of attachments for the part.
          items:
            $ref: '#/components/schemas/part_attachment'
        url:
          type: string
          nullable: true
          description: >-
            The URL where the conversation was started. For Twitter, Email, and
            Bots, this will be blank.
          example: null
        redacted:
          type: boolean
          description: >-
            Whether or not the source message has been redacted. Only applicable
            for contact initiated messages.
          example: false


    createGuestRequest:
      description: Payload to create a guuest
      type: object
      title: Create Guest Request Payload
      properties:
        externalId:
          type: string
          description: A unique identifier for the guest which is given to ChatApp
        email:
          type: string
          description: The guest's email
          example: <EMAIL>
        phone:
          type: string
          nullable: true
          description: The guuest's phone
          example: '+353871234567'
        name:
          type: string
          nullable: true
          description: The guest's name
          example: John Doe
        avatar:
          type: object
          description: The cover image of the room.
          properties:
            filename:
              type: string
              description: The name of the file
              example: avatar
              nullable: false
            fileUrl:
              type: string
              format: uri
              nullable: false
              description: This url of the file.
              example: https://s3.sparkchat.example.com/avatar.png
        signedUpAt:
          type: string
          format: date-time
          nullable: true
          description: The time specified for when a contact signed up
          example: '2025-01-06T09:15:08.470Z'
        lastSeenAt:
          type: integer
          format: date-time
          nullable: true
          description: >-
            The time when the guest was last seen (either where the ChatApp
            Messenger was opened or when specified manually)
          example: '2025-01-06T09:15:08.470Z'

               
    createMessageRequest:
      description: You can create a message
      type: object
      title: Create Message Request Payload
      nullable: true
      properties:
        messageType:
          type: string
          description: 'The kind of message being created.'
          enum:
            - group
            - direct
          example: direct
        parentId:
          type: string
          nullable: true
          description: The id of the parent (Useful for threads).
          example: 680c53ab6c842dbac3e5ba21
        roomId:
          type: string
          nullable: true
          description: The id of the room. Null for direct messages.
          example: 680c53ab6c842dbac3e5ba21
        content:
          type: object
          description: The message content.
          properties:
            text:
              type: string
              nullable: true
              description: The text content of the message. This cannot be null if there are no files in the content.
              example: Hello Sandra.
            files:
              nullable: true
              description: The files in the message.
              type: array
              items:
                type: object
                properties:
                  filename:
                    type: string
                    description: The name of the file
                    example: avatar
                    nullable: false
                  fileUrl:
                    type: string
                    format: uri
                    nullable: false
                    description: This url of the file.
                    example: https://example.com/avatar.png
        from:
          type: object
          description: The sender of the message
          properties:
            id:
              type: string
              description: The id of the sender.
              example: 680c53ab6c842dbac3e5ba21
            type:
              type: string
              description: The type of sender
              enum:
                - admin
                - chat-agent
                - guest
        to:
          type: object
          description: The recipient of the message
          properties:
            id:
              type: string
              description: The id of the recipient.
              example: 680c53ab6c842dbac3e5ba21
            type:
              type: string
              description: The type of recipient. The recipient can be an individual or a group chat.
              enum:
                - individual
                - group
        createdAt:
          type: string
          description: >-
            The time the message was created. If not provided, the current time
            will be used.
          example: '2025-01-06T09:15:08.470Z'
      required:
        - messageType
        - content
        - to
        - from

      
    cursorPages:
      title: Cursor based pages
      type: object
      description: >
        Cursor-based pagination is a technique used in the Intercom API to
        navigate through large amounts of data.

        A "cursor" or pointer is used to keep track of the current position in
        the result set, allowing the API to return the data in small chunks or
        "pages" as needed.
      nullable: true
      properties:
        page:
          type: integer
          description: The current page
          example: 1
        next:
          $ref: '#/components/schemas/starting_after_paging'
        per_page:
          type: integer
          description: Number of results per page
          example: 2
        total_pages:
          type: integer
          description: Total number of pages
          example: 13
    
    data_event:
      title: Data Event
      type: object
      x-tags:
        - Data Events
      description: Data events are used to notify Intercom of changes to your data.
      properties:
        type:
          type: string
          description: The type of the object
          enum:
            - event
          example: event
        event_name:
          type: string
          description: >-
            The name of the event that occurred. This is presented to your App's
            admins when filtering and creating segments - a good event name is
            typically a past tense 'verb-noun' combination, to improve
            readability, for example `updated-plan`.
          example: invited-friend
        created_at:
          type: integer
          format: date-time
          description: The time the event occurred as a UTC Unix timestamp
          example: 1671028894
        user_id:
          type: string
          description: Your identifier for the user.
          example: '314159'
        id:
          type: string
          description: Your identifier for a lead or a user.
          example: 8a88a590-e1c3-41e2-a502-e0649dbf721c
        chat_user_id:
          type: string
          description: The Intercom identifier for the user.
          example: 63a0979a5eeebeaf28dd56ba
        email:
          type: string
          description: >-
            An email address for your user. An email should only be used where
            your application uses email to uniquely identify users.
          example: <EMAIL>
        metadata:
          type: object
          description: Optional metadata about the event.
          additionalProperties:
            type: string
          example:
            invite_code: ADDAFRIEND
      required:
        - event_name
        - created_at
    
        
    data_event_summary:
      title: Data Event Summary
      type: object
      description: This will return a summary of data events for the App.
      properties:
        type:
          type: string
          description: The type of the object
          enum:
            - event.summary
          example: event.summary
        email:
          type: string
          description: The email address of the user
          example: <EMAIL>
        chat_user_id:
          type: string
          description: The Intercom user ID of the user
          example: 63a0979a5eeebeaf28dd56ba
        user_id:
          type: string
          description: The user ID of the user
          example: 62b997f288e14803c5006932
        events:
          type: array
          description: A summary of data events
          items:
            $ref: '#/components/schemas/data_event_summary_item'
    
    data_event_summary_item:
      title: Data Event Summary Item
      type: object
      description: This will return a summary of a data event for the App.
      nullable: true
      properties:
        name:
          type: string
          description: The name of the event
          example: placed-order
        first:
          type: string
          description: The first time the event was sent
          example: '2014-01-16T23:12:21.000+00:00'
        last:
          type: string
          description: The last time the event was sent
          example: '2014-01-16T23:12:21.000+00:00 '
        count:
          type: integer
          description: The number of times the event was sent
          example: 1
        description:
          type: string
          description: The description of the event
          example: A user placed an order
   
   
    message:
      type: object
      title: Message
      x-tags:
        - Messages
      description: >-
        Message are how you reach out to people in ChatApp.
      properties:
        id:
          type: string
          description: The id representing the message.
          example: 680c53ab6c842dbac3e5ba21
        parentId:
          type: string
          nullable: true
          description: The id of the parent (Useful for threads).
          example: 680c53ab6c842dbac3e5ba21
        roomId:
          type: string
          nullable: true
          description: The id of the room.
          example: 680c53ab6c842dbac3e5ba21
        createdAt:
          type: string
          format: date-time
          description: The time the conversation was created.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: integer
          format: date-time
          description: The time the conversation was created.
          example: '2024-12-31T16:43:37.724Z'
        content:
          type: object
          description: The message content.
          properties:
            text:
              type: string
              nullable: true
              description: The text content of the message. This cannot be null if there are no files in the content.
              example: Hello Sandra.
            files:
              nullable: true
              description: The files in the message.
              type: array
              items:
                type: object
                properties:
                  filename:
                    type: string
                    description: The name of the file
                    example: avatar
                    nullable: false
                  fileUrl:
                    type: string
                    format: uri
                    nullable: false
                    description: This url of the file.
                    example: https://example.com/avatar.png
        read:
          type: boolean
          description: Indicates the read status for a recipient. This may be supported in group chats.
          example: false
        from:
          type: object
          description: The sender of the message
          properties:
            id:
              type: string
              description: The id of the sender.
              example: 680c53ab6c842dbac3e5ba21
            type:
              type: string
              description: The type of sender
              enum:
                - admin
                - chat-agent
                - ai-agent
                - guest
        to:
          type: string
          description: The recipient of the message. The recipient is always a room.
          example: 680c53ab6c842dbac3e5ba21
        mentions:
          type: object
          description: >
            This specifies the guests mentioned in the message. Specific members can be mentioned
            or the entire group.
          properties:
            type:
              type: string
              description: >
                Specifies whether the mention is for individual users or a group/chat-room.
              enum:
                - individual
                - group
            mentionedUserIds:
              type: array
              description: The specific guests mentioned in the message.
              items:
                type: string
            groupType:
              type: string
              enum:
                - all
              description: >
                This infers that all the members of a group or chat-room are mentioned.
                In a group chat named 'Swimming With Adele in Paris' refers to all members.
              example: all
      required:
        - type
        - id
        - created_at
        - body
        - message_type
    
    createMessage:
      type: object
      title: Message
      x-tags:
        - Messages
      description: Create Message object.
      properties:
        parentId:
          type: string
          nullable: true
          description: The id of the parent (Useful for threads).
          example: 680c53ab6c842dbac3e5ba21
        roomId:
          type: string
          description: The id of the room.
          example: 680c53ab6c842dbac3e5ba21
        content:
          type: object
          description: The message content.
          properties:
            text:
              type: string
              nullable: true
              description: The text content of the message. This cannot be null if there are no files in the content.
              example: Hello Sandra.
            files:
              nullable: true
              description: The files in the message.
              type: array
              items:
                type: object
                properties:
                  filename:
                    type: string
                    description: The name of the file
                    example: avatar
                    nullable: false
                  fileUrl:
                    type: string
                    format: uri
                    nullable: false
                    description: This url of the file.
                    example: https://example.com/avatar.png
        from:
          type: object
          description: The sender of the message
          properties:
            id:
              type: string
              description: The id of the sender.
              example: 680c53ab6c842dbac3e5ba21
            type:
              type: string
              description: The type of sender
              enum:
                - admin
                - chat-agent
                - ai-agent
                - guest
        to:
          type: object
          description: The recipient of the message. The recipient is always a room.
          properties:
            id:
              type: string
              description: The id of the recipient.
              example: 680c53ab6c842dbac3e5ba21
        mentions:
          type: object
          description: >
            This specifies the guests mentioned in the message. Specific members can be mentioned
            or the entire group.
          properties:
            type:
              type: string
              description: >
                Specifies whether the mention is for individual users or a group/chat-room.
              enum:
                - individual
                - group
            mentionedUserIds:
              type: array
              description: The specific guests mentioned in the message.
              items:
                type: string
    
    updateMessage:
      type: object
      title: Message
      x-tags:
        - Messages
      description: Update Message object.
      properties:
        content:
          type: object
          description: The message content.
          properties:
            text:
              type: string
              nullable: true
              description: The text content of the message. This cannot be null if there are no files in the content.
              example: Hello Sandra.
            files:
              nullable: true
              description: The files in the message.
              type: array
              items:
                type: object
                properties:
                  filename:
                    type: string
                    description: The name of the file
                    example: avatar
                    nullable: false
                  fileUrl:
                    type: string
                    format: uri
                    nullable: false
                    description: This url of the file.
                    example: https://example.com/avatar.png
        mentions:
          type: object
          description: >
            This specifies the guests mentioned in the message. Specific members can be mentioned
            or the entire group.
          properties:
            type:
              type: string
              description: |
                Specifies whether the mention is for individual users or a group/chat-room.

                If the `type` is `group`, any item in the `mentionedUserIds` array should be ignored.
                This is because all the members of that chat room will be notified. Otherwise, if the
                `type` is `individual`, there should be at least one userId in the `mentionedUserIds`.
              enum:
                - individual
                - group
            mentionedUserIds:
              type: array
              description: The specific guests mentioned in the message.
              items:
                type: string
    
    paginatedResponse:
      title: Paginated Response
      type: object
      description: Paginated Response
      properties:
        startingAfter:
          type: string
          description: >-
            The cursor to use in the next request to get the next page of
            results.
          nullable: true
          example: 68b5163af407f75cd5bb6b40
        endingBefore:
          type: string
          description: >-
            The cursor to use in the next request to get the previous page of
            results.
          nullable: true
          example: 68b5163af407f75cd5bb6b40
       
    
    
    subscription_type:
      title: Subscription Types
      type: object
      x-tags:
        - Subscription Types
      description: >-
        A subscription type lets customers easily opt out of non-essential
        communications without missing what's important to them.
      properties:
        type:
          type: string
          description: The type of the object - subscription
          example: subscription
        id:
          type: string
          description: The unique identifier representing the subscription type.
          example: '123456'
        state:
          type: string
          description: The state of the subscription type.
          enum:
            - live
            - draft
            - archived
          example: live
        default_translation:
          $ref: '#/components/schemas/translation'
        translations:
          type: array
          description: >-
            An array of translations objects with the localised version of the
            subscription type in each available locale within your translation
            settings.
          items:
            $ref: '#/components/schemas/translation'
        consent_type:
          type: string
          description: Describes the type of consent.
          enum:
            - opt_out
            - opt_in
          example: opt_in
        content_types:
          type: array
          description: >-
            The message types that this subscription supports - can contain
            `email` or `sms_message`.
          items:
            type: string
            enum:
              - email
              - sms_message
            example: email
    

    tag:
      title: Tag
      type: object
      x-tags:
        - Tags
      description: >-
        A tag allows you to label Guests, Companies, Messages and Rooms and list them using that tag.
      properties:
        id:
          type: string
          description: The tag ID
          example: 68b5163af407f75cd5bb6b40
        name:
          type: string
          description: The name of the tag
          example: Test tag
        appliedAt:
          type: string
          format: date-time
          description: The time when the tag was first applied to the object
          example: '2025-01-07T09:08:01.304Z'
        applied_by:
          $ref: '#/components/schemas/reference'
    
    
    room:
      title: Rooms
      type: object
      description: >
        The Room object describes a chat room for an application
      properties:
        id:
          type: string
          description: The id representing the room.
          example: 680c53ab6c842dbac3e5ba21
        name:
          type: string
          description: The room title
          example: Peter's Graduation Room - May 2025
        description:
          type: string
          nullable: true
          description: A long text describing what the room is about.
          example: >
            Group chat for peter's graduattion. All peter's friends are welcome to contribute.
        avatar:
          type: object
          description: The cover image of the room.
          properties:
            filename:
              type: string
              description: The name of the file
              example: avatar
              nullable: false
            fileUrl:
              type: string
              format: uri
              nullable: false
              description: This url of the file.
              example: https://s3.sparkchat.example.com/avatar.png
        applicationId:
          type: string
          description: The id of the app the room is associated with.
          example: 680c53ab6c842dbac3e5ba21
        archived:
          type: boolean
          description: Indicates if a room has been archived.
          example: false
          default: false
        createdAt:
          type: string
          format: date-time
          description: The time the room was created.
          example: '2024-12-31T16:43:37.724Z'
        updatedAt:
          type: string
          format: date-time
          description: The time the conversation was updated.
          example: '2024-12-31T16:43:37.724Z'
        membersCount:
          type: integer
          description: The total number of guests in the room.
          example: 20
        onlineMembersCount:
          type: integer
          description: The number of guests currently online in the room.
          example: 15
        tags:
          type: array
          items:
            $ref: '#/components/schemas/tag'
    
    createRoom:
      title: Rooms
      type: object
      description: The object to create a new room.
      properties:
        name:
          type: string
          description: The room title
          example: Peter's Graduation Room - May 2025
        description:
          type: string
          nullable: true
          description: A long text describing what the room is about.
          example: >
            Group chat for peter's graduattion. All peter's friends are welcome to contribute.
        avatar:
          type: object
          description: The cover image of the room.
          properties:
            filename:
              type: string
              description: The name of the file
              example: avatar
              nullable: false
            fileUrl:
              type: string
              format: uri
              nullable: false
              description: This url of the file.
              example: https://s3.sparkchat.example.com/avatar.png
        applicationId:
          type: string
          description: The id of the app the room is associated with.
          example: 680c53ab6c842dbac3e5ba21
        tags:
          type: array
          items:
            $ref: '#/components/schemas/tag'
    
    updateRoom:
      title: Rooms
      type: object
      description: The object to update a room.
      properties:
        name:
          type: string
          description: The room title
          example: Peter's Graduation Room - May 2025
        description:
          type: string
          nullable: true
          description: A long text describing what the room is about.
          example: >
            Group chat for peter's graduattion. All peter's friends are welcome to contribute.
        avatar:
          type: object
          description: The cover image of the room.
          properties:
            filename:
              type: string
              description: The name of the file
              example: avatar
              nullable: false
            fileUrl:
              type: string
              format: uri
              nullable: false
              description: This url of the file.
              example: https://s3.sparkchat.example.com/avatar.png
        applicationId:
          type: string
          description: The id of the app the room is associated with.
          example: 680c53ab6c842dbac3e5ba21
        tags:
          type: array
          items:
            type: string


    
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
servers:
  - url: https://api.chat.io
    description: The production API server
  - url: https://api.eu.chat.io
    description: The european API server
  - url: https://api.au.chat.io
    description: The australian API server
security:
  - bearerAuth: []
tags:
  - name: Accounts
    description: Everything about your Account
  - name: Users
    description: Everything about your Users
  - name: Companies
    description: Everything about your Companies
  - name: Guests
    description: Everything about your guests
  - name: Messages
    description: Everything about your messages
  - name: Subscription Types
    description: Everything about subscription types
  - name: Tags
    description: Everything about tags
  - name: Rooms
    description: Everything about your Rooms
