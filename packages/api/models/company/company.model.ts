import { z } from "zod";
import { Database, DBLogger } from "../../utils/database";
import { ICreateCompanyData, ICompany, AccessKey, IAccessKey, ICreateCompanyAccesskey } from "../../models";
import { IDeleteCompanyAccesskey, IUpdateCompanyAccesskey } from "./company.types";


interface ICheckUserRoleInACompanyData {
  ownerId: string;
  companies: [{ userRoles: [{ role: string }] }];
} 

export class Company extends Database<"Company">{
    private model = 'company' as const;

    locationSchema = z.object({
        country: z.string().optional(),
        region: z.string().optional(),
        city: z.string().optional(),
        longitude: z.string().optional(),
        latitude: z.string().optional(),
      }).optional();
    
    createSchema = z.object({
        name: z.string().min(3, {message: "name must be atleast 3 characters"}).max(50, {message: "name must not exceed 50 characters"}),
        website: z.string().optional(),
        industry: z.string().optional(),
        domains: z.array(z.string()).default(['http://localhost:3000', 'http://localhost:3001']),
        monthlySpend: z.number().default(0),
        location: this.locationSchema,
        accountId: this.idSchema,
      });
    
      updateSchema = z.object({
        id: this.idSchema,
        name:  z.string().min(3, {message: "name must be atleast 3 characters"}).max(50, {message: "name must not exceed 50 characters"}).optional(),
        website: z.string().optional(),
        industry: z.string().optional(),
        domains: z.array(z.string()).optional(),
        monthlySpend: z.number().optional(),
        location: this.locationSchema,
        accountId: this.idSchema,
      });
    
    constructor(private readonly accessKeyModel: AccessKey) {
        super();
    }

    private hasAccessToUpdateRole(userRoles: { role: string }[], requiredRole: string): boolean {
      if (!userRoles || userRoles.length === 0) return false; 
    
      const userRoleSet = new Set(userRoles.map(ur => ur.role));

      //const allowedRoles = new Set(["Admin", "Owner", "Manager"]); 
    
      return userRoleSet.has(requiredRole) //&& allowedRoles.has(requiredRole);
    }

    /**
     * Fetches the user's role in a company within an account.
     * This query ensures the user is associated with the company and fetches their role.
     * 
     * @param companyId 
     * @param accountId 
     * @param userId 
     * @returns {Promise<ICheckUserRoleInACompanyData | null>}
     */
    private getUserRoleData(companyId: string, accountId: string, userId: string): Promise<ICheckUserRoleInACompanyData> {
      return this.findFirst({
        model: "account",
        where: {
          AND: [
            { id: accountId },
            {
              companies: {
                some: {
                  AND: [
                    { id: companyId }, 
                    { userIds: { has: userId } },
                  ],
                },
              },
            },
          ],
        },
        select: {
          ownerId: true, 
          companies: {
            where: { id: companyId },
            take: 1,
            select: {
              userRoles: {
                where: { userId }, 
                select: { role: true },
              },
            },
          },
        },
      }) as any;
    }
    

    async createCompany(data: ICreateCompanyData, user: {id: string}): Promise<ICompany> {
      try {
        const accountId = this.validateId(data.accountId);
        const account = await this.prisma.user.findUnique({
          where: { id: user.id },
          select: { accountId: true },
        });
    
        if (!account || !account?.accountId) {
          throw new Error("Access denied: Only an account owner can create a company under this account");
        }
    
        const validatedData = this.validateCreate({ ...data, accountId });
    
        const [createdCompany, createdDefaultAppInstance] = await this.prisma.$transaction(async (prisma) => {
          const createdCompany = await prisma.company.create({data: {
            ...validatedData, 
            applicationsCount: 0, 
            usersCount: 1, 
            guestsCount: 0,
            guestIds: [],
            userIds: [user.id],
            anonymousIds: [],
            tagIds: []

          }});
          const createdDefaultAppInstance = await prisma.application.create({data: {companyId: createdCompany.id, name: `${createdCompany.name}-GeneralApp`} })
          await prisma.account.update({where: { id: account.accountId }, data: { companiesCount: { increment: 1 } }});
          return [createdCompany, createdDefaultAppInstance];
        });
    
        return { ...createdCompany, applications: [createdDefaultAppInstance]  } as ICompany;
      } catch (error) {
        DBLogger.error(`Company not created, operationContext: Company.createCompany, message: ${error.message || error}`);
        throw error;
      }
    }
    
      
    async updateCompany(data: Partial<ICompany>, user: { id: string; email: string }, roleName: string): Promise<ICompany> {
      try {
        const validatedData = this.validateUpdate(data);
        const checkUserRoleData = await this.getUserRoleData(data.id, data.accountId, user.id);
  
        if (!checkUserRoleData) {
          throw new Error("Unable to update company: company not found or user does not have access.");
        }
  
        const company = checkUserRoleData.companies[0];
        const isOwner = checkUserRoleData?.ownerId === user.id;
        const hasRoleAccess = this.hasAccessToUpdateRole(company.userRoles, roleName);
  
        if (!isOwner && !hasRoleAccess) {
          throw new Error("Access denied: You do not have permission to change this company record.");
        }
  
        const updatePayload = {
          ...validatedData,
          id: undefined,
          accountId: undefined,
          applicationsCount: data.applicationsCount ? { increment: data.applicationsCount } : undefined,
          usersCount: data.usersCount ? { increment: data.usersCount } : undefined,
          guestsCount: data.guestsCount ? { increment: data.guestsCount } : undefined,
          monthlySpend: data.monthlySpend !== undefined ? data.monthlySpend : undefined,
          location: data.location !== undefined ? data.location : undefined,
          tagIds: data.tagIds ? { set: data.tagIds } : undefined,
          domains: data.domains ? { set: data.domains } : undefined,
          userIds: data.userIds ? { set: data.userIds } : undefined,
          guestIds: data.guestIds ? { set: data.guestIds } : undefined,
          anonymousIds: data.anonymousIds ? { set: data.anonymousIds } : undefined,
        };
  
        Object.keys(updatePayload).forEach((key) => updatePayload[key] === undefined && delete updatePayload[key]);
  
        const updatedCompany = await this.update({ model: this.model, where: { id: validatedData.id }, data: updatePayload });
        return updatedCompany;
      } catch (error) {
        DBLogger.error(`Company not updated, operationContext: Company.updateCompany, message: ${error.message || error}`);
        throw error;
      }
    }


    async updateCompanyGuests(
      companyId: string,
      guestIdsToAdd: string[],
      guestIdsToRemove: string[],
    ): Promise<ICompany> {
      try {
        this.validateId(companyId);
        guestIdsToAdd.forEach(this.validateId);
        guestIdsToRemove.forEach(this.validateId);

        const company = await this.prisma.company.findUnique({
          where: { id: companyId },
          select: { guestIds: true },
        });

        if (!company) {
          throw new Error('Unable to update: Company not found');
        }

        const updatedIds = [
          ...new Set([
            ...company.guestIds.filter(id => !guestIdsToRemove.includes(id)),
            ...guestIdsToAdd,
          ]),
        ];

        const count = updatedIds.length;

        const updatedCompany = await this.prisma.company.update({
          where: { id: companyId },
          data: {
            guestsCount: count,
            guestIds: {
              set: updatedIds,
            },
          },
        });

        return updatedCompany;
        
      } catch(error: any){
        DBLogger.error(`Company not updated, operationContext: Company.updateCompanyGuests, message: ${error.message || error}`);
        throw error;
      }
    }

    async updateCompanyDomains(
      companyId: string,
      domainsToAdd: string[],
      domainsToRemove: string[],
    ): Promise<ICompany> {
      try {
        this.validateId(companyId);
        domainsToAdd.forEach(this.validateId);
        domainsToRemove.forEach(this.validateId);
  
        const company = await this.prisma.company.findUnique({
          where: { id: companyId },
          select: { domains: true },
        });
  
        if (!company) {
          throw new Error('Unable to update: Company not found');
        }
  
        const updatedDomains = [
          ...new Set([
            ...company.domains.filter(id => !domainsToRemove.includes(id)),
            ...domainsToAdd,
          ]),
        ];
  
        const updatedCompany = await this.prisma.company.update({
          where: { id: companyId },
          data: {
            domains: {
              set: updatedDomains,
            },
          },
        });
  
        return updatedCompany;
      } catch (error) {
        DBLogger.error(`Company not updated, operationContext: Company.updateCompanyDomains, message: ${error.message || error}`);
        throw error;
      }
    }

    async updateCompanyUsers(
      companyId: string,
      userIdsToAdd: string[],
      userIdsToRemove: string[],
    ): Promise<ICompany> {
      try{
        this.validateId(companyId);
        userIdsToAdd.forEach(this.validateId);
        userIdsToRemove.forEach(this.validateId);

        const company = await this.prisma.company.findUnique({where: {id: companyId}});
        if(!company){
          throw new Error('Unable to update: Company not found');
        }

        const updatedIds = [
          ...new Set([
            ...company.userIds.filter(id => !userIdsToRemove.includes(id)),
            ...userIdsToAdd,
          ]),
        ];

        const count = updatedIds.length;
        
        const updatedCompany = await this.prisma.company.update({
          where: { id: companyId },
          data: {
            guestsCount: count,
            guestIds: {
              set: updatedIds,
            },
          },
        });

        return updatedCompany;

      }catch(error: any){
        DBLogger.error(`Company not updated, operationContext: Company.updateCompanyUsers, message: ${error.message || error}`);
        throw error;
      }
    }

    async getCompanyById(companyId: string): Promise<ICompany | null>{
      try{
        this.validateId(companyId);
        return this.prisma.company.findUnique({
          where: {id: companyId},
          include: {
            applications: true
          }
        });

      }catch(error: any){
        DBLogger.error(`Company not updated, operationContext: Company.getCompanyById, message: ${error.message || error}`)
        throw error;
      }
    }
   
    async createCompanyAccessKey(data: ICreateCompanyAccesskey, user: { id: string; email: string }): Promise<IAccessKey> {
      try {
        if (!data.companyId || !data.accountId || !data.roleName || !data.name) {
          throw new Error("Validation failed: Missing required fields.");
        }
        this.validateId(data.companyId);
        this.validateId(data.accountId);
  
        if (data?.status && data?.status !== "Active") {
          throw new Error("Validation failed: status value must be Active as this is a new key");
        }
  
        const checkUserRoleData = await this.getUserRoleData(data.companyId, data.accountId, user.id);
        
        if (!checkUserRoleData) {
          throw new Error("Unable to create Api key: Permission Denied");
        }
  
        const isOwner = checkUserRoleData?.ownerId === user.id;
        const hasRoleAccess = this.hasAccessToUpdateRole(checkUserRoleData?.companies[0]?.userRoles, data.roleName);
  
        if (!isOwner && !hasRoleAccess) {
          throw new Error("Access denied: You do not have permission to create api key for this company.");
        }
  
        return await this.accessKeyModel.createAccessKey({
          name: data.name,
          creatorId: data.accountId,
          companyId: data.companyId,
          expiresAt:   new Date("2026-12-25") 
        });
  
      } catch (error) {
        DBLogger.error(`Access key not created, operationContext: Company.createCompanyAccessKey, message: ${error.message || error}`);
        throw error;
      }
    }

    async updateCompanyAccessKey(data: IUpdateCompanyAccesskey, user: { id: string; email: string }): Promise<IAccessKey> {
      try {
        if (!data.companyId || !data.accountId || !data.roleName || !data.apiKey || !data.apiKeySecret) {
          throw new Error("Validation failed: Missing required fields.");
        }
        this.validateId(data.companyId);
        this.validateId(data.accountId);
  
        if (data.status && !["Active", "Revoked"].includes(data.status)) {
          throw new Error("Validation failed: Status must be one of: Active, Revoked");
        }
  
        const checkUserRoleData = await this.getUserRoleData(data.companyId, data.accountId, user.id);
  
        const isOwner = checkUserRoleData?.ownerId === user.id;
        const hasRoleAccess = this.hasAccessToUpdateRole(checkUserRoleData?.companies[0]?.userRoles, data.roleName);
  
        if (!checkUserRoleData || !isOwner || !hasRoleAccess) {
          throw new Error("Access denied: You do not have permission to update api key record.");
        }

        return await this.accessKeyModel.updateAccessKey(data.apiKey, data.apiKeySecret, { name: data.name, status: data.status });
      } catch (error) {
        DBLogger.error(`Access Key not updated, operationContext: Company.updateCompanyAccessKey, message: ${error.message || error}`);
        throw error
      }

    }

    // async getCompanyAccessKey(data: {key: string, companyId: string, accountId: string}, user: {id: string}): Promise<IAccessKey | null> {
    //   try {
    //     if (!data.key || !data.companyId) {
    //       throw new Error("Validation failed: Missing required fields.");
    //     }
    //     this.validateId(data.companyId);
    //     this.validateId(data.accountId);
    //     const checkUserRoleData = await this.getUserRoleData(data.companyId, data.accountId, user.id);
        
    //     if (!checkUserRoleData) {
    //       throw new Error("Unable to fetch access key: company not found");
    //     }
    //     return await this.accessKeyModel.getAccessKey(data);
    //   } catch (error) {
    //     DBLogger.error(`Access Key not found, operationContext: Company.getCompanyAccessKey, message: ${error.message || error}`);
    //     throw error;
    //   }
    // }

    async deleteCompanyAccessKey(data: IDeleteCompanyAccesskey, user: {id: string}): Promise<null> {
      try {
        if ((!data?.apiKey && !data?.apiKeySecret ) || (!data?.companyId || !data?.accountId || !data?.roleName)) {
          throw new Error("Validation failed: Missing required fields.");
        }
        this.validateId(data.companyId);
        this.validateId(data.accountId);

        const checkUserRoleData = await this.getUserRoleData(data.companyId, data.accountId, user.id);

        const isOwner = checkUserRoleData?.ownerId === user.id;
        const hasRoleAccess = this.hasAccessToUpdateRole(checkUserRoleData?.companies[0]?.userRoles, data.roleName);
  
        if (! checkUserRoleData || !isOwner || !hasRoleAccess) {
          throw new Error("Access denied: You do not have permission to delete api key for this company.");
        }
        await this.accessKeyModel.deleteAccessKey(data.apiKey, data.apiKeySecret);
        return null;
      } catch (error) {
        DBLogger.error(`Access Key not deleted, operationContext: Company.deleteCompanyAccessKey, message: ${error.message || error}`);
        throw error;
      }
    }
}
