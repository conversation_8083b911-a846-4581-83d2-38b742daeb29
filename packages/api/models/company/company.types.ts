import { $Enums, Application, CloudProviderType } from "@prisma/client";
import { ILocation } from "../../models";


export interface ICompany {
    id: string;
    name: string;
    website?: string;
    industry?: string;
    applicationsCount: number;
    usersCount: number;
    guestsCount: number;
    monthlySpend?: number;
    createdAt?: Date;
    updatedAt?: Date;
    location?: ILocation;
    tagIds: string[];
    domains?: string[];
  
    accountId: string;
  
    userIds: string[];
  
    guestIds: string[];
  
    anonymousIds: string[];
    applications?: Application[]
  }
  

  export interface ICreateCompanyData {
    name: string;
    website?: string;
    industry?: string;
    applicationsCount?: number;
    usersCount?: number;
    guestsCount?: number;
    monthlySpend?: number;
    location?: ILocation;
    tagIds?: string[];
    accountId: string;
    userIds?: string[];
    guestIds?: string[];
    anonymousIds?: string[];
  }
  
  export interface IUpdateCompanyData {
    name?: string;
    website?: string;
    industry?: string;
    applicationsCount?: number;
    usersCount?: number;
    guestsCount?: number;
    monthlySpend?: number;
    location?: ILocation;
    tagIds?: string[];
    accountId?: string;
    userIds?: string[];
    guestIds?: string[];
    anonymousIds?: string[];
  }
  export interface ICheckUserRoleInACompanyData {
    ownerId: string;
    companies: [{ userRoles: [{ role: string }] }];
  } 
  export interface ICreateCompanyAccesskey {
    name: string;
    companyId: string;
    accountId: string;
    status?: $Enums.KeyStatus;
    roleName: string;
  }

  export interface IUpdateCompanyAccesskey {
    companyId: string;
    accountId: string;
    roleName: string;
    apiKey: string;
    apiKeySecret: string;
    name?: string;
    status?: $Enums.KeyStatus;
  }
  export interface IDeleteCompanyAccesskey {
    companyId: string, 
    accountId: string, 
    roleName: string, 
    apiKey: string,
    apiKeySecret: string, 
  }

  export interface IAwsCloudStorage {
    bucketName: string;
    region: string;
    keyId: string;
    secretKey: string;
    companyId: string;
  }


  export interface IGoogleCloudStorage {
    bucketName: string;
    projectId: string;
    keyId: string;
    secretKey: string;
    companyId: string;
  }


  export interface IAzureCloudStorage {
    containerName: string;
    accountName: string;
    keyId: string;
    secretKey: string;
    companyId: string;
  }


  



