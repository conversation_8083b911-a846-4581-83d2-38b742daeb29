import {  RoomPermission  } from '@prisma/client';


export interface RoomPermissionWithRelations extends RoomPermission {

}

export enum IPermissionEntityType {
  MODERATOR = 'MODERATOR',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  ROLE = 'ROLE',
  ANONYMOUS = 'ANONYMOUS',
}

export enum IRoomPermissionType {
  // Default Room permissions
  CAN_SEND_MESSAGES = 'CAN_SEND_MESSAGES',       // can post new messages,
  CAN_DELETE_OWN_MESSAGES = 'CAN_DELETE_OWN_MESSAGES',      // can delete their own messages,
  CAN_EDIT_OWN_MESSAGES = 'CAN_EDIT_OWN_MESSAGES',      // can edit their own messages,
  CAN_UPLOAD_FILES = 'CAN_UPLOAD_FILES',          // can upload files,
  CAN_DELETE_OWN_FILES = 'CAN_DELETE_OWN_FILES',         // can delete their own files,

  // Moderator permissions
  CAN_DELETE_OTHER_MESSAGES = 'CAN_DELETE_OTHER_MESSAGES',    // can delete other people's messages,
  CAN_PIN_MESSAGES  = 'CAN_PIN_MESSAGES',       // can pin/unpin messages,
  CAN_MODERATE_USERS = 'CAN_MODERATE_USERS',      // can mute/kick/ban users,
  CAN_CHANGE_ROOM_NAME = 'CAN_CHANGE_ROOM_NAME',    // can change room name,
  CAN_CHANGE_ROOM_DESCRIPTION = 'CAN_CHANGE_ROOM_DESCRIPTION', // can change room description,
  
  // Admin permissions
  CAN_MANAGE_PERMISSIONS = 'CAN_MANAGE_PERMISSIONS',  // can manage room permissions,
  CAN_MANAGE_SETTINGS   = 'CAN_MANAGE_SETTINGS',   // can manage room settings,
  CAN_GRANT_MODERATOR_ROLE = 'CAN_GRANT_MODERATOR', // can grant moderator role,
  CAN_UPDATE_ROOM_DATA = 'CAN_UPDATE_ROOM_DATA',         // can update room details,
  CAN_GRANT_ADMIN_ROLE = 'CAN_GRANT_ADMIN_ROLE',
  CAN_SEND_MESSAGE = "CAN_SEND_MESSAGE", // can grant admin role
}

export interface IRoomPermission {
  id: string,
  entityType: IPermissionEntityType,
  entityId: string,
  roomId: string,
  permission: IRoomPermissionType,
  isAllowed: boolean,
  createdAt?: string | Date,
  updatedAt?: string | Date,
}