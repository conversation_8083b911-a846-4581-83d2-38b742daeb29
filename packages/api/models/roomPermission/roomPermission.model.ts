import {  z } from "zod";
import { <PERSON>, DBLogger } from "../../utils/database";
import {  IPermissionEntityType, IRoomPermissionType, IRoomPermission } from "./roomPermission.types";
import { PermissionEntityType, RoomPermissionType } from "@prisma/client";


export class RoomPermission extends Database<"RoomPermission"> {
    private model = 'roomPermission' as const;

    createSchema = z.object({
        roomId: z.string().min(1, { message: 'Room ID is required' }),
        entityType: z.enum(["ADMIN", "MODERATOR", "MEMBER", "ROLE", "ANONYMOUS"]),
        entityId: z.string().min(1, { message: 'Entity ID is required' }),
        permission: z.enum(["CAN_SEND_MESSAGES", "CAN_DELETE_OWN_MESSAGES", "CAN_EDIT_OWN_MESSAGES", "CAN_UPLOAD_FILES", "CAN_DELETE_OWN_FILES", "CAN_DELETE_OTHER_MESSAGES", "CAN_PIN_MESSAGES", "CAN_MODERATE_USERS", "CAN_CHANGE_ROOM_NAME", "CAN_CHANGE_ROOM_DESCRIPTION", "CAN_MANAGE_PERMISSIONS", "CAN_MANAGE_SETTINGS", "CAN_GRANT_MODERATOR_ROLE", "CAN_UPDATE_ROOM_DATA", "CAN_GRANT_ADMIN_ROLE"]),
        isAllowed: z.boolean().optional().default(true),
    });

    updateSchema = z.object({
        isAllowed: z.boolean().optional().default(true),
    });

    constructor() {
        super();
    }
    // Utility function to transform Prisma RoomPermission to IRoomPermission
    private transformRoomPermission(permission: any): IRoomPermission {
        return {
            id: permission.id,
            entityType: permission.entityType, 
            entityId: permission.entityId,
            permission: permission.permission,
            isAllowed: permission.isAllowed,
            createdAt: permission.createdAt,
            updatedAt: permission.updatedAt,
            roomId: permission.roomId,
        };
    };

        
    /**
     * Creates default permissions for a new room
     * @param roomId - The ID of the room
     * @param creatorId - The ID of the user creating the room
     * @returns Array of created IRoomPermission objects
     */
    async createDefaultPermissions(roomId: string, creatorId: string): Promise<boolean> {
        const defaultPermissions: Partial<IRoomPermission>[] = [
            // Admin permissions for the creator
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: RoomPermissionType.CAN_SEND_MESSAGES as any,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_DELETE_OWN_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_EDIT_OWN_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_UPLOAD_FILES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_DELETE_OWN_FILES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_DELETE_OTHER_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_PIN_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_MODERATE_USERS,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_CHANGE_ROOM_NAME,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_CHANGE_ROOM_DESCRIPTION,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_MANAGE_PERMISSIONS,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_MANAGE_SETTINGS,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_GRANT_MODERATOR_ROLE,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_UPDATE_ROOM_DATA,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.ADMIN,
                entityId: creatorId,
                permission: IRoomPermissionType.CAN_GRANT_ADMIN_ROLE,
                isAllowed: true,
            },
            // Default permissions for all members
            {
                roomId,
                entityType: IPermissionEntityType.MEMBER,
                entityId: 'ALL',
                permission: IRoomPermissionType.CAN_SEND_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.MEMBER,
                entityId: 'ALL',
                permission: IRoomPermissionType.CAN_DELETE_OWN_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.MEMBER,
                entityId: 'ALL',
                permission: IRoomPermissionType.CAN_EDIT_OWN_MESSAGES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.MEMBER,
                entityId: 'ALL',
                permission: IRoomPermissionType.CAN_UPLOAD_FILES,
                isAllowed: true,
            },
            {
                roomId,
                entityType: IPermissionEntityType.MEMBER,
                entityId: 'ALL',
                permission: IRoomPermissionType.CAN_DELETE_OWN_FILES,
                isAllowed: true,
            },
        ];
        try{
            this.validateId(roomId);
            this.validateId(creatorId);
            
            const count = await this.prisma.roomPermission.createMany({ 
                data: defaultPermissions as any, 
            });
           
            return count.count === defaultPermissions.length;
        } catch (error) {
            console.log(error);
            DBLogger.error('Default permissions not created, operationContext: RoomPermission.createDefaultPermissions, message:', error.message);
            throw error;
        }
    }

        
    /**
     * Grants admin permissions to a member in a room
     * @param roomId - The ID of the room
     * @param memberId - The ID of the member to make an admin
     * @param callerId - The ID of the user performing the action (Optional)
     * @returns Array of created/updated IRoomPermission objects
     */
    async  makeMemberAnAdmin(roomId: string, memberId: string, callerId?: string): Promise<IRoomPermission[]> {
        this.validateId(roomId);
        this.validateId(memberId);
        const adminPermissions: Partial<IRoomPermission>[] = [
            { permission: IRoomPermissionType.CAN_SEND_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OWN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_EDIT_OWN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_UPLOAD_FILES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OWN_FILES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OTHER_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_PIN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_MODERATE_USERS, isAllowed: true },
            { permission: IRoomPermissionType.CAN_CHANGE_ROOM_NAME, isAllowed: true },
            { permission: IRoomPermissionType.CAN_CHANGE_ROOM_DESCRIPTION, isAllowed: true },
            { permission: IRoomPermissionType.CAN_MANAGE_PERMISSIONS, isAllowed: true },
            { permission: IRoomPermissionType.CAN_MANAGE_SETTINGS, isAllowed: true },
            { permission: IRoomPermissionType.CAN_GRANT_MODERATOR_ROLE, isAllowed: true },
            { permission: IRoomPermissionType.CAN_UPDATE_ROOM_DATA, isAllowed: true },
            { permission: IRoomPermissionType.CAN_GRANT_ADMIN_ROLE, isAllowed: true },
        ];
        try {
            if (callerId) {
                this.validateId(callerId);
                const hasPermission = await this.checkRoomPermission(
                    roomId,
                    callerId,
                    IPermissionEntityType.ADMIN,
                    IRoomPermissionType.CAN_MANAGE_PERMISSIONS
                );
                if (!hasPermission) {
                    throw new Error("Forbidden: You do not have permission to grant admin role");
                }
            }
            const upsertPromises = adminPermissions.map((perm) =>
                this.prisma.roomPermission.upsert({
                where: {
                    roomId_entityType_entityId_permission: {
                    roomId,
                    entityType: IPermissionEntityType.ADMIN,
                    entityId: memberId,
                    permission: perm.permission as any,
                    },
                },
                create: {
                    roomId,
                    entityType: IPermissionEntityType.ADMIN,
                    entityId: memberId,
                    permission: perm.permission as any,
                    isAllowed: perm.isAllowed,
                },
                update: {
                    isAllowed: perm.isAllowed,
                },
                })
            );

            const permissions = await Promise.all(upsertPromises);
            return permissions.map(this.transformRoomPermission);
        } catch (error) {
            DBLogger.error('Error making member an admin, operationContext: RoomPermission.makeMemberAnAdmin, message:', error.message);
            throw error;
        }
    }

    /**
     * Grants moderator permissions to a member in a room
     * @param roomId - The ID of the room
     * @param memberId - The ID of the member to make a moderator
     * @param callerId - The ID of the user performing the action (Optional)
     * @returns Array of created/updated IRoomPermission objects
     */
    async  makeMemberAModerator(roomId: string, memberId: string, callerId?: string): Promise<IRoomPermission[]> {
        const moderatorPermissions: Partial<IRoomPermission>[] = [
            { permission: IRoomPermissionType.CAN_SEND_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OWN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_EDIT_OWN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_UPLOAD_FILES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OWN_FILES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_DELETE_OTHER_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_PIN_MESSAGES, isAllowed: true },
            { permission: IRoomPermissionType.CAN_MODERATE_USERS, isAllowed: true },
            { permission: IRoomPermissionType.CAN_CHANGE_ROOM_NAME, isAllowed: true },
            { permission: IRoomPermissionType.CAN_CHANGE_ROOM_DESCRIPTION, isAllowed: true },
        ];
        try {
            this.validateId(roomId);
            this.validateId(memberId);
            if (callerId) {
                this.validateId(callerId);
                const hasPermission = await this.checkRoomPermission(
                    roomId,
                    callerId,
                    IPermissionEntityType.ADMIN,
                    IRoomPermissionType.CAN_MANAGE_PERMISSIONS
                );
                if (!hasPermission) {
                    throw new Error("Forbidden: You do not have permission to grant moderator role");
                }
            }
            const upsertPromises = moderatorPermissions.map((perm) =>
                this.prisma.roomPermission.upsert({
                where: {
                    roomId_entityType_entityId_permission: {
                    roomId,
                    entityType: IPermissionEntityType.MODERATOR,
                    entityId: memberId,
                    permission: perm.permission as any
                    },
                },
                create: {
                    roomId,
                    entityType: IPermissionEntityType.MODERATOR,
                    entityId: memberId,
                    permission: perm.permission as any,
                    isAllowed: perm.isAllowed,
                },
                update: {
                    isAllowed: perm.isAllowed,
                },
                })
            );

            const permissions = await Promise.all(upsertPromises);
            return permissions.map(this.transformRoomPermission);
        } catch (error) {
            DBLogger.error('Error making member a moderator, operationContext: RoomPermission.makeMemberAModerator, message:', error.message);
            throw error;
        }
    }

        
    /**
     * Checks if a member has a specific permission in a room
     * @param roomId - The ID of the room
     * @param entityId - The ID of the member or role
     * @param entityType - The type of entity (ADMIN, MODERATOR, MEMBER, ROLE, ANONYMOUS)
     * @param permission - The permission to check
     * @returns Boolean indicating if the permission is allowed
     */
    async  checkRoomPermission(
        roomId: string,
        entityId: string,
        entityType: IPermissionEntityType,
        permission: IRoomPermissionType
    ): Promise<boolean> {
        try {
            this.validateId(roomId);
            this.validateId(entityId);
            // Check specific permission for the entity
            const specificPermission = await this.prisma.roomPermission.findUnique({
                where: {
                roomId_entityType_entityId_permission: {
                    roomId,
                    entityType,
                    entityId,
                    permission: permission as any,
                },
                },
            });

            if (specificPermission) {
                return specificPermission.isAllowed;
            }

            // If no specific permission, check if the entity has admin or moderator role
            if (entityType === IPermissionEntityType.MEMBER || entityType === IPermissionEntityType.ANONYMOUS) {
                const rolePermission = await this.prisma.roomPermission.findFirst({
                where: {
                    roomId,
                    entityType: {
                    in: [IPermissionEntityType.ADMIN, IPermissionEntityType.MODERATOR],
                    },
                    entityId,
                    permission: permission as any,
                },
                });

                if (rolePermission) {
                    return rolePermission.isAllowed;
                }

                // Check for 'ALL' members permission
                const allMembersPermission = await this.prisma.roomPermission.findUnique({
                where: {
                    roomId_entityType_entityId_permission: {
                    roomId,
                    entityType: IPermissionEntityType.MEMBER,
                    entityId: 'ALL',
                    permission: permission as any,
                    },
                },
                });

                return allMembersPermission?.isAllowed ?? false;
            }

            return false;
        } catch (error) {
            DBLogger.error('Error checking room permission, operationContext: RoomPermission.checkRoomPermission, message:', error.message);
            throw error;
        }
    }

    /**
     * Revokes specific or all permissions for a member in a room
     * @param roomId - The ID of the room
     * @param entityId - The ID of the member or role
     * @param entityType - The type of entity (ADMIN, MODERATOR, MEMBER, ROLE, ANONYMOUS)
     * @param permissions - Optional array of specific permissions to revoke; if not provided, revokes all permissions
     * @param callerId - The ID of the user performing the action (Optional)
     * @returns Array of updated IRoomPermission objects
     */
    async revokeRoomPermissions(
        roomId: string,
        entityId: string,
        entityType: IPermissionEntityType,
        permissions?: IRoomPermissionType[],
        callerId?: string
    ): Promise<IRoomPermission[]> {
        try {
            this.validateId(roomId);
            this.validateId(entityId);

            // Check if caller has permission to manage permissions
            if (callerId) {
                this.validateId(callerId);
                const hasPermission = await this.checkRoomPermission(
                    roomId,
                    callerId,
                    IPermissionEntityType.ADMIN,
                    IRoomPermissionType.CAN_MANAGE_PERMISSIONS
                );
                if (!hasPermission) {
                    throw new Error("Forbidden: You do not have permission to manage this room permissions");
                }
            }

            // Build the where clause for permissions to revoke
            const whereClause: any = { roomId, entityType, entityId };

            if (permissions && permissions.length > 0) {
                whereClause.permission = { in: permissions };
            }

            // Update permissions to set isAllowed to false
            await this.prisma.roomPermission.updateMany({
                where: whereClause,
                data: {
                    isAllowed: false,
                },
            });

            // Fetch the updated permissions to return them
            const fetchedPermissions = await this.prisma.roomPermission.findMany({
                where: whereClause,
            });

            return fetchedPermissions.map(this.transformRoomPermission);
        
        } catch (error) {
            DBLogger.error('Error revoking room permissions, operationContext: RoomPermission.revokeRoomPermissions, message:', error.message);
            throw error;
        }
    }

        
    /**
     * Reinstates specific or all previously revoked permissions for a member in a room
     * @param roomId - The ID of the room
     * @param entityId - The ID of the member or role
     * @param entityType - The type of entity (ADMIN, MODERATOR, MEMBER, ROLE, ANONYMOUS)
     * @param permissions - Optional array of specific permissions to reinstate; if not provided, reinstates all permissions
     * @param callerId - The ID of the user performing the action (Optional)
     * @returns Array of updated IRoomPermission objects
     */
    async reinstateRoomPermissions(
        roomId: string,
        entityId: string,
        entityType: IPermissionEntityType,
        permissions?: IRoomPermissionType[],
        callerId?: string
    ): Promise<IRoomPermission[]> {
        try {
            this.validateId(roomId);
            this.validateId(entityId);

            // Check if caller has permission to manage permissions
            if (callerId) {
                this.validateId(callerId);
                const hasPermission = await this.checkRoomPermission(
                    roomId,
                    callerId,
                    IPermissionEntityType.ADMIN,
                    IRoomPermissionType.CAN_MANAGE_PERMISSIONS
                );
                if (!hasPermission) {
                    throw new Error("Forbidden: You do not have permission to manage this room permissions");
                }
            }

            // Build the where clause for permissions to reinstate
            const whereClause: any = { roomId, entityType, entityId, };

            if (permissions && permissions.length > 0) {
                whereClause.permission = { in: permissions };
            }

            // Update permissions to set isAllowed to true
            await this.prisma.roomPermission.updateMany({
                where: whereClause,
                data: {
                    isAllowed: true,
                },
            });

            // Fetch the updated permissions to return them
            const fetchedPermissions = await this.prisma.roomPermission.findMany({ where: whereClause,});

            return fetchedPermissions.map(this.transformRoomPermission);
        } catch (error) {
            DBLogger.error('Error reinstate room permissions, operationContext: RoomPermission.reinstateRoomPermissions, message:', error.message);
            throw error;
        }
    }

    /**
     * Deletes specific or all permissions for a member in a room
     * @param roomId - The ID of the room
     * @param entityId - The ID of the member or role
     * @param entityType - The type of entity (ADMIN, MODERATOR, MEMBER, ROLE, ANONYMOUS)
     * @param permissions - Optional array of specific permissions to delete; if not provided, deletes all permissions
     * @param callerId - The ID of the user performing the action
     * @returns Array of deleted IRoomPermission objects
     */
    async deleteRoomPermissions(
        roomId: string,
        entityId: string,
        entityType: IPermissionEntityType,
        permissions?: IRoomPermissionType[],
        callerId?: string
    ): Promise<IRoomPermission[]> {
        try {
            this.validateId(roomId);
            this.validateId(entityId);

            // Check if caller has permission to manage permissions
            if (callerId) {
                this.validateId(callerId);
                const hasPermission = await this.checkRoomPermission(
                    roomId,
                    callerId,
                    IPermissionEntityType.ADMIN,
                    IRoomPermissionType.CAN_MANAGE_PERMISSIONS
                );
                if (!hasPermission) {
                    throw new Error("Forbidden: You do not have permission to manage this room permissions");
                }
            }

            // Build the where clause for permissions to delete
            const whereClause: any = { roomId, entityType, entityId, };

            if (permissions && permissions.length > 0) {
                whereClause.permission = { in: permissions };
            }

            // Fetch permissions before deleting to return them
            const permissionsToDelete = await this.prisma.roomPermission.findMany({ where: whereClause,});

            if (permissionsToDelete.length === 0) {
                throw new Error('Not Found: No permissions found to delete');
            }

            // Delete the permissions
            await this.prisma.roomPermission.deleteMany({
                where: whereClause,
            });

            return permissionsToDelete.map(this.transformRoomPermission);
        } catch (error) {
            DBLogger.error('Error deleting room permissions, operationContext: RoomPermission.deleteRoomPermissions, message:', error.message);
            throw error;
        }
    }

    /**
     * Checks if the caller has a specific permission in a room
     * @param roomId - The ID of the room
     * @param callerId - The ID of the user performing the action
     * @param permission - The permission to check
     * @returns Boolean indicating if the caller has the permission
     */
    async checkCallerPermission(
        roomId: string,
        callerId: string,
        permission: IRoomPermissionType
    ): Promise<boolean> {
        try {
            this.validateId(roomId);
            this.validateId(callerId);

            // Check permission for the caller as ADMIN or MEMBER
            const hasPermission = await this.checkRoomPermission(
                roomId,
                callerId,
                IPermissionEntityType.ADMIN,
                permission
            ) || await this.checkRoomPermission(
                roomId,
                callerId,
                IPermissionEntityType.MEMBER,
                permission
            );

            return hasPermission;
        } catch (error) {
            DBLogger.error('Error checking caller permission, operationContext: RoomPermission.checkCallerPermission, message:', error.message);
            throw error;
        }
    }

}