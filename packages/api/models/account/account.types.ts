interface IAccountCompany {
  id: string;
  name: string;
  website?: string;
  industry?: string;
  applicationsCount: number;
  usersCount: number;
  guestsCount: number;
  monthlySpend?: number;
  createdAt: Date;
  updatedAt: Date;
  accountId: string;
}

interface IAccountAccessKey {
  id: string;
  name: string;
  key: string;
  status: "Active" | "Revoked" | "Expired";
  expiresAt: Date;
  lastRequestAt: Date;
  createdAt: Date;
  updatedAt: Date;
  creatorId: string;
  companyId: string;
}

interface IAccountSubscription {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  endedAt?: Date;
  active: boolean;
  accountId: string;
  subscriptionId: string;
}
export interface IGenerateAccountData {
  id: string;
  name: string;
  companiesCount: number;
  applicationsCount: number;
  usersCount: number;
  guestsCount: number;
  createdAt: Date;
  updatedAt: Date;
  companies: IAccountCompany[];
  accountSubscriptions: IAccountSubscription[];
  ownerId: string;
  accessKeys: IAccountAccessKey[];
}


export interface IAccount {
    name: string;
    companiesCount: number;
    applicationsCount: number;
    usersCount: number;
    guestsCount: number;
    ownerId: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
}


  export interface ICreateAccountData {
    name: string;
    ownerId: string;
    companiesCount?: number;
    applicationsCount?: number;
    usersCount?: number;
    guestsCount?: number;
    companies?: IAccountCompany[];
    accountSubscriptions?: IAccountSubscription[];
    accessKeys?: IAccountAccessKey[];
  }
  
  export interface IUpdateAccountData {
    id: string;
    name?: string;
    ownerId: string;
    companiesCount?: number;
    applicationsCount?: number;
    usersCount?: number;
    guestsCount?: number;
    companies?: IAccountCompany[];
    accountSubscriptions?: IAccountSubscription[];
    accessKeys?: IAccountAccessKey[];
  }
 
  