import { z } from "zod";
import { <PERSON>, DBLogger } from "../../utils";
import { ICreateAccountData, IUpdateAccountData, IAccount, IGenerateAccountData } from "./account.types"; 


export class Account extends Database<"Account">{
    private model = 'account' as const;
    
    createSchema = z.object({
        name: z.string().min(3, {message: "name must be atleast 3 characters"}).max(50, {message: "name must not exceed 50 characters"}),
        companiesCount: z.number().int().nonnegative().default(0),
        applicationsCount: z.number().int().nonnegative().default(0),
        usersCount: z.number().int().nonnegative().default(1), // default to 1 because the owner is a user
        guestsCount: z.number().int().nonnegative().default(0),
        ownerId: this.idSchema,
        companies: z.array(this.idSchema).optional(), 
        accountSubscriptions: z.array(this.idSchema).optional(), 
        accessKeys: z.array(z.string()).optional(), 
      });
    
      updateSchema = z.object({
        id: this.idSchema.optional(),
        name: z.string().min(3, {message: "name must be atleast 3 characters"}).max(50, {message: "name must not exceed 50 characters"}).optional(),
        companiesCount: z.number().int().nonnegative().optional(),
        applicationsCount: z.number().int().nonnegative().optional(),
        usersCount: z.number().int().nonnegative().optional(),
        guestsCount: z.number().int().nonnegative().optional(),
        ownerId: this.idSchema.optional(),
        companies: z.array(this.idSchema).optional(), 
        accountSubscriptions: z.array(this.idSchema).optional(),
        accessKeys: z.array(z.string()).optional(),
      });
    
    constructor() {
        super();
    }

    async createAccount(data: ICreateAccountData): Promise<IGenerateAccountData> {
        try {
          const validatedData = this.validateCreate(data);
          const existingAccount = await this.findUnique({ where: { ownerId: data.ownerId }, model: "account" });
          if (existingAccount) {
            throw new Error("You are already an account owner.");
          }
          const result = await this.prisma.$transaction(async (_prisma) => {  
            const newAccount = await this.create( { model: this.model, data: validatedData, include: { companies: true, accessKeys: true, accountSubscriptions: true } });
            await this.update({ model: 'user', where: { id: data.ownerId }, data: { accountId: newAccount.id } });
            return newAccount as IGenerateAccountData;
          });
          return result;          
        } catch (error) {
          DBLogger.error(`Account not created, operationContext: Account.createAccount, message: ${error.message || error}`);
          throw error;
        }
      }
      

    async updateAccount(data: IUpdateAccountData): Promise<IAccount> {
        try {
            const validatedData = this.validateUpdate(data);
            const { id, ...rest } = validatedData;
            const account: IAccount  = await this.findFirst({ where: {AND: [{id }, {ownerId: data.ownerId }]}, model: this.model });
            if (!account) {
              throw new Error("Unable to update account, account not found");
            }
            const result: IAccount = await this.update({ model: this.model, where: {id: account.id }, data: rest });
            return result;
        } catch (error) {
            DBLogger.error(`Account not updated, operationContext: Account.updateAccount, message: ${error.message || error}`);
            throw error;
        }
      }

    async deleteAccount(data: {id: string, ownerId: string}): Promise<null> {
        try {
            const validateAccountId = this.validateId(data.id);
            const validateOwnerId = this.validateId(data.ownerId);
            const account = await this.findFirst({ where: {AND: [{id: validateAccountId, }, {ownerId: validateOwnerId }]}, model: this.model });
            if (!account) {
              throw new Error("Unable to delete account, account not found");
            }
            await this.prisma.$transaction(async (_prisma) => { 
              await this.delete({ model: this.model, where: {id: account.id } });
              const user = await this.update({model: 'user', where: {id: validateOwnerId}, data: {role: 'User', accountId: null}});
              return user;
            });
            return null;
        } catch (error) {
            DBLogger.error(`Account not deleted, operationContext: Account.deleteAccount, message: ${error.message || error}`);
            throw error;
        }
      }
}