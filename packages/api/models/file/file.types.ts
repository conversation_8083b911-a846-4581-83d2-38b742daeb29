export interface IFile {
  id: string;
  key: string;
  filename: string;
  fileUrl: string;
  fileType?: string;
  size: number;
  createdAt: Date;
  updatedAt: Date;
  uploadedBy: string;
  messageId?: string;
  roomId?: string;
  providerId?: string;
}

export interface ICreateFile {
  key: string;
  filename: string;
  fileUrl: string;
  fileType?: string;
  size: number;
  uploadedBy: string;
  messageId?: string;
  roomId?: string;
  providerId?: string;
}

export interface IUpdateFile {
  filename?: string;
  fileUrl?: string;
  size?: number;
  key?: string;
  fileType?: string;
  uploadedBy?: string;
  messageId?: string;
  roomId?: string;
  providerId?: string;
}


export interface ICreateMessageFile {
  filename: string;
  fileUrl: string;
  fileType: string;
  size: number;
  uploadedBy: string;
  messageId: string;
  roomId: string;
}

export interface IMessageFile {
  id: string;
  filename: string;
  fileUrl: string;
  fileType?: string;
  size: number;
  createdAt: Date;
  updatedAt: Date;
  uploader: { id: string; name: string; username: string };
  messageId: string;
  roomId: string;
}



export interface IUpdateMessageFile {
  filename?: string;
  uploadedBy?: string;
  messageId?: string;
  roomId?: string;
}

export interface  IUploadedFile {
id: string,
filename: string,
fileUrl: string,
fileType?: string,
size: number,
createdAt: Date,
updatedAt: Date,
uploadedBy: string,
messageId?: string,
roomId?: string,
}