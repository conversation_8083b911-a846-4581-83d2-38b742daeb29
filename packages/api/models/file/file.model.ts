import { z } from "zod";
import { Database, DBLogger } from "../../utils/database";
import { IFile, ICreateFile, IUpdateFile, ICreateMessageFile, IMessageFile, IUpdateMessageFile, IUploadedFile } from "./file.types";

export class File extends Database<"File"> {
  private model = "file" as const;

  createSchema = z.object({
    key: z.string().min(1, "Key is required"),
    filename: z.string().min(1, "Filename is required"),
    fileUrl: z.string().min(1, "File URL is required"),
    size: z.number().min(1, "Size is required"),
    fileType: z.string().min(1, "File type is required").optional(),
    uploadedBy: z.string().min(1, "Uploaded by is required"),
    messageId: z.string().min(1, "Message ID is required").optional(),
    roomId: z.string().min(1, "Room ID is required").optional(),
    providerId: z.string().min(1, "Provider ID is required").optional(),

  });

  updateSchema = z.object({
    filename: z.string().min(1, "Filename is required").optional(),
    fileUrl: z.string().min(1, "File URL is required").optional(),
    size: z.number().min(1, "Size is required").optional(),
    fileType: z.string().min(1, "File type is required").optional(),
    uploadedBy: z.string().min(1, "Uploaded by is required").optional(),
    messageId: z.string().min(1, "Message ID is required").optional(),
    roomId: z.string().min(1, "Room ID is required").optional(),
    providerId: z.string().min(1, "Provider ID is required").optional(),
  });

  constructor() {
    super();
  }

  async createFile(data: ICreateFile[]): Promise<IFile[]> {
    try {
      const validatedData = data.map(file => this.validateCreate(file));
      const result = await this.prisma.$transaction(
        validatedData.map(file => 
          this.prisma.file.create({
            data: file,
          })
        )
      );

      return result;
    } catch (error) {
      DBLogger.error(`File not created, operationContext: File.createFile, message: ${error.message || error}`);
      throw error;
    }
  }

  async updateFile(id: string, data: IUpdateFile): Promise<IFile> {
    try {
      this.validateId(id);
      const validatedData = this.validateUpdate(data);
      const result = await this.prisma.file.update({
        where: { id },
        data: validatedData,
      });
      return result;
    } catch (error) {
      DBLogger.error(`File not updated, operationContext: File.updateFile, message: ${error.message || error}`);
      throw error;
    }
  }

  async getFileById(id: string): Promise<IFile | null> {
    try {
      this.validateId(id);
      const result = await this.prisma.file.findUnique({
        where: { id },
      });
      return result;
    } catch (error) {
      DBLogger.error(`File not found, operationContext: File.getFileById, message: ${error.message || error}`);
      throw error;
    }
  }

  async createMessageFile(data: ICreateMessageFile): Promise<IMessageFile> {
    try {
      const validatedData = this.validateCreate(data);
      const result = await this.prisma.file.create({
        data: validatedData,
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      const { guest, key, providerId, ...rest } = result;      
      return { uploader: guest, ...rest };
    } catch (error) {
      DBLogger.error(`File not created, operationContext: File.createMessageFile, message: ${error.message || error}`);
      throw error;
    }
  }

  async updateMessageFile(id: string, data: IUpdateMessageFile): Promise<IMessageFile> {
    try {
      this.validateId(id);
      const validatedData = this.validateUpdate(data);
      const result = await this.prisma.file.update({
        where: { id },
        data: validatedData,
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      const { guest, key, providerId, ...rest } = result;
      return { uploader: guest, ...rest };
    } catch (error) {
      DBLogger.error(`File not updated, operationContext: File.updateMessageFile, message: ${error.message || error}`);
      throw error;
    }
  }
  
  async getRoomFiles(roomId: string): Promise<IMessageFile[]> {
    try {
      this.validateId(roomId);
      const result = await this.prisma.file.findMany({
        where: { roomId },
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      return result.map((file) => {
        const { guest, key, providerId, ...rest } = file;
        return { uploader: guest, ...rest };
      });
    } catch (error) {
      DBLogger.error(`Files not found, operationContext: File.getRoomFiles, message: ${error.message || error}`);
      throw error;
    }
  }

  async getGuestFiles(guestId: string): Promise<IMessageFile[]> {
    try {
      this.validateId(guestId);
      const result = await this.prisma.file.findMany({
        where: { uploadedBy: guestId },
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      return result.map((file) => {
        const { guest, key, providerId, ...rest } = file;
        return { uploader: guest, ...rest };
      });
    } catch (error) {
      DBLogger.error(`Files not found, operationContext: File.getGuestFiles, message: ${error.message || error}`);
      throw error;
    }
  }

  async getGuestFilesInRoom(guestId: string, roomId: string): Promise<IMessageFile[]> {
    try {
      this.validateId(guestId);
      this.validateId(roomId);
      const result = await this.prisma.file.findMany({
        where: { uploadedBy: guestId, roomId },
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      return result.map((file) => {
        const { guest, key, providerId, ...rest } = file;
        return { uploader: guest, ...rest };
      });
    } catch (error) {
      DBLogger.error(`Files not found, operationContext: File.getGuestFilesInRoom, message: ${error.message || error}`);
      throw error;
    }
  }

  async getMessageFiles(messageId: string): Promise<IMessageFile[]> {
    try {
      this.validateId(messageId);
      const result = await this.prisma.file.findMany({
        where: { messageId },
        include: { guest: { select: { id: true, name: true, username: true } } }
      });
      return result.map((file) => {
        const { guest, key, providerId, ...rest } = file;
        return { uploader: guest, ...rest };
      });
    } catch (error) {
      DBLogger.error(`Files not found, operationContext: File.getMessageFiles, message: ${error.message || error}`);
      throw error;
    }
  }

  async deleteFile(id: string): Promise<void> {
    try {
      this.validateId(id);
      await this.prisma.file.delete({
        where: { id }
      });
      return;
    } catch (error) {
      DBLogger.error(`File not deleted, operationContext: File.deleteFile, message: ${error.message || error}`);
      throw error;
    }
  }

  async uploadGuestFile(data: ICreateFile[]): Promise<IUploadedFile[]> {
    try {
      const validatedData = data.map(file => this.validateCreate(file));
      const result = await this.prisma.$transaction(
        validatedData.map(file => 
          this.prisma.file.create({
            data: file,
          })
        )
      );
      return result.map((file) => {
        const fileUrl = `${process.env.BASE_URL}/api/v1/file/view/${file.id}`
        const { key, providerId, ...rest } = file;
        return { ...rest, fileUrl };
      });
    } catch (error) {
      DBLogger.error(`File not created, operationContext: File.uploadFileInRoom, message: ${error.message || error}`);
      throw error;
    }
  }
}