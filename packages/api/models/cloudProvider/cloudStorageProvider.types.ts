import { CloudProviderType } from '@prisma/client';
import { IFile } from '../file/file.types';

export interface ICloudStorageProvider {
  id: string;
  name: string;
  provider: CloudProviderType;
  bucketName?: string | null;
  containerName?: string | null;
  region?: string | null;
  projectId?: string | null;
  accountName?: string | null;
  isActive: boolean;
  companyId: string;
  keyId?: string | null;
  secretKey?: string | null;
  credentialsJson?: string | null;
  accountKey?: string | null;
  connectionString?: string | null;
  description?: string | null;
  isPrimary: boolean;
  isRevoked: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateCloudStorageProvider {
  name: string;
  provider: CloudProviderType;
  bucketName?: string;
  containerName?: string;
  region?: string;
  projectId?: string;
  accountName?: string;
  isActive?: boolean;
  companyId: string;
  keyId?: string;
  secretKey?: string;
  credentialsJson?: string;
  accountKey?: string;
  connectionString?: string;
  description?: string;
  isPrimary?: boolean;
  isRevoked?: boolean;
}

export interface IUpdateCloudStorageProvider {
  name?: string;
  provider?: CloudProviderType;
  bucketName?: string | null;
  containerName?: string | null;
  region?: string | null;
  projectId?: string | null;
  accountName?: string | null;
  isActive?: boolean;
  keyId?: string | null;
  secretKey?: string | null;
  credentialsJson?: string | null;
  accountKey?: string | null;
  connectionString?: string | null;
  description?: string | null;
  isPrimary?: boolean;
  isRevoked?: boolean;
  updatedAt?: Date;
}

export interface IFileCloudStorageProvider {
  provider : ICloudStorageProvider | null;
  file: IFile;
}
