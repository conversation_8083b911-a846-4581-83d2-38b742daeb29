import { z } from 'zod';
import { Database, DBLogger, IResponse } from '../../utils/database';
import { 
  ICloudStorageProvider, 
  ICreateCloudStorageProvider, 
  IFileCloudStorageProvider, 
  IUpdateCloudStorageProvider
} from './cloudStorageProvider.types';
import { CloudStorageProvider as PrismaCloudStorageProvider } from '@prisma/client';

export class CloudStorageProvider extends Database<'CloudStorageProvider'> {
  private model = 'CloudStorageProvider' as const;

  createSchema = z.object({
    name: z.string().min(1, { message: 'Name is required' }),
    provider: z.enum(['AWS', 'GCP', 'AZURE'], { message: 'Invalid provider type' }),
    bucketName: z.string().optional().nullable(),
    containerName: z.string().optional().nullable(),
    region: z.string().optional().nullable(),
    projectId: z.string().optional().nullable(),
    accountName: z.string().optional().nullable(),
    isActive: z.boolean().optional().default(true),
    companyId: z.string().min(1, { message: 'Company ID is required' }),
    keyId: z.string().optional().nullable(),
    secretKey: z.string().optional().nullable(),
    credentialsJson: z.string().optional().nullable(),
    accountKey: z.string().optional().nullable(),
    connectionString: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
    isPrimary: z.boolean().optional().default(false),
    isRevoked: z.boolean().optional().default(false),
  });

  updateSchema = z.object({
    name: z.string().min(1, { message: 'Name is required' }).optional(),
    provider: z.enum(['AWS', 'GCP', 'AZURE']).optional(),
    bucketName: z.string().optional().nullable(),
    containerName: z.string().optional().nullable(),
    region: z.string().optional().nullable(),
    projectId: z.string().optional().nullable(),
    accountName: z.string().optional().nullable(),
    isActive: z.boolean().optional(),
    keyId: z.string().optional().nullable(),
    secretKey: z.string().optional().nullable(),
    credentialsJson: z.string().optional().nullable(),
    accountKey: z.string().optional().nullable(),
    connectionString: z.string().optional().nullable(),
    description: z.string().optional().nullable(),
    isPrimary: z.boolean().optional(),
    isRevoked: z.boolean().optional(),
    updatedAt: z.date().optional(),
  });

  // Transform Prisma model to interface
  static TransformCloudStorageProviderToICloudStorageProvider(
    provider: PrismaCloudStorageProvider
  ): ICloudStorageProvider {
    return {
      id: provider.id,
      name: provider.name,
      provider: provider.provider,
      bucketName: provider.bucketName,
      containerName: provider.containerName,
      region: provider.region,
      projectId: provider.projectId,
      accountName: provider.accountName,
      isActive: provider.isActive,
      companyId: provider.companyId,
      keyId: provider.keyId,
      secretKey: provider.secretKey,
      credentialsJson: provider.credentialsJson,
      accountKey: provider.accountKey,
      connectionString: provider.connectionString,
      description: provider.description,
      isPrimary: provider.isPrimary,
      isRevoked: provider.isRevoked,
      createdAt: provider.createdAt,
      updatedAt: provider.updatedAt,
    };
  }

  // Create a CloudStorageProvider
  async createCloudStorageProvider(data: ICreateCloudStorageProvider): Promise<ICloudStorageProvider> {
    try {
      const validatedData = this.validateCreate(data);

      const result = await this.prisma.cloudStorageProvider.create({
        data: {
          name: validatedData.name,
          provider: validatedData.provider,
          bucketName: validatedData.bucketName,
          containerName: validatedData.containerName,
          region: validatedData.region,
          projectId: validatedData.projectId,
          accountName: validatedData.accountName,
          isActive: validatedData.isActive ?? true,
          company: { connect: { id: validatedData.companyId } },
          keyId: validatedData.keyId,
          secretKey: validatedData.secretKey,
          credentialsJson: validatedData.credentialsJson,
          accountKey: validatedData.accountKey,
          connectionString: validatedData.connectionString,
          description: validatedData.description,
          isPrimary: validatedData.isPrimary ?? false,
          isRevoked: validatedData.isRevoked ?? false,
        },
      });

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not created, operationContext: CloudStorageProvider.createCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  // Get a CloudStorageProvider by ID
  async getCloudStorageProvider(id: string): Promise<PrismaCloudStorageProvider> {
    try {
      this.validateId(id);
      const result = await this.prisma.cloudStorageProvider.findUnique({
        where: { id },
      });

      if (!result) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      return result;
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  // Update a CloudStorageProvider
  async updateCloudStorageProvider(id: string, data: IUpdateCloudStorageProvider): Promise<ICloudStorageProvider> {
    try {
      this.validateId(id);
      const validatedData = this.validateUpdate(data);

      const existingProvider = await this.prisma.cloudStorageProvider.findUnique({
        where: { id },
      });

      if (!existingProvider) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      const result = await this.prisma.cloudStorageProvider.update({
        where: { id },
        data: {
          ...validatedData,
        },
      });

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not updated, operationContext: CloudStorageProvider.updateCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getCloudStorageProviderByCompanyId(companyId: string): Promise<ICloudStorageProvider> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findFirst({
        where: { companyId, isActive: true, isPrimary: true, isRevoked: false },
      });

      if (!result) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getCloudStorageProviderByCompanyId, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async activateCloudStorageProvider(id: string): Promise<ICloudStorageProvider> {
    try {
      this.validateId(id);
      const existingProvider = await this.prisma.cloudStorageProvider.findUnique({
        where: { id },
      });

      if (!existingProvider) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      const result = await this.prisma.cloudStorageProvider.update({
        where: { id },
        data: {
          isActive: true,
          isPrimary: true,
          isRevoked: false,
        },
      });

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not activated, operationContext: CloudStorageProvider.activateCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async revokeCloudStorageProvider(id: string): Promise<ICloudStorageProvider> {
    try {
      this.validateId(id);
      const existingProvider = await this.prisma.cloudStorageProvider.findUnique({
        where: { id },
      });

      if (!existingProvider) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      const result = await this.prisma.cloudStorageProvider.update({
        where: { id },
        data: {
          isActive: false,
          isPrimary: false,
          isRevoked: true,
        },
      });

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not revoked, operationContext: CloudStorageProvider.revokeCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async deleteCloudStorageProvider(id: string): Promise<void> {
    try {
      this.validateId(id);
      const existingProvider = await this.prisma.cloudStorageProvider.findUnique({
        where: { id },
      });

      if (!existingProvider) {
        throw new Error('Not Found: CloudStorageProvider not found');
      }

      await this.prisma.cloudStorageProvider.delete({
        where: { id },
      });
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not deleted, operationContext: CloudStorageProvider.deleteCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getListOfCloudStorageProviders(companyId: string): Promise<ICloudStorageProvider[]> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findMany({
        where: { companyId },
      });

      return result.map((provider) =>
        CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(provider)
      );
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getListOfCloudStorageProviders, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getPrimaryCloudStorageProvider(companyId: string): Promise<ICloudStorageProvider | null> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findFirst({
        where: { companyId, isActive: true, isPrimary: true, isRevoked: false },
      });

      if (!result) {
        return null;
      }

      return CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(result);
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getPrimaryCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getActiveCloudStorageProviders(companyId: string): Promise<ICloudStorageProvider[]> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findMany({
        where: { companyId, isActive: true, isRevoked: false },
      });

      return result.map((provider) =>
        CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(provider)
      );
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getActiveCloudStorageProviders, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getRevokedCloudStorageProviders(companyId: string): Promise<ICloudStorageProvider[]> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findMany({
        where: { companyId, isRevoked: true },
      });

      return result.map((provider) =>
        CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(provider)
      );
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getRevokedCloudStorageProviders, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getCloudStorageProvidersByCompanyId(companyId: string): Promise<ICloudStorageProvider[]> {
    try {
      this.validateId(companyId);
      const result = await this.prisma.cloudStorageProvider.findMany({
        where: { companyId },
      });

      return result.map((provider) =>
        CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(provider)
      );
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getCloudStorageProvidersByCompanyId, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

  async getFileCloudStorageProvider(fileId: string): Promise<IFileCloudStorageProvider | null> {
    try {
      const file = await this.prisma.file.findUnique({
        where: { id: fileId },
      });

      if (!file) {
        throw new Error('Not Found: File not found');
      }

      const provider = await this.prisma.cloudStorageProvider.findUnique({
        where: { id: file.providerId },
      });

      if (!provider) {
        throw new Error('Not Available: No provider found for this file');
      }
      if(!provider.isActive) {
        throw new Error('Not Available: This file storage provider is not active');
      }

      return { file, provider: CloudStorageProvider.TransformCloudStorageProviderToICloudStorageProvider(provider) };
    } catch (error) {
      DBLogger.error(
        `CloudStorageProvider not found, operationContext: CloudStorageProvider.getFileCloudStorageProvider, message: ${
          error.message || error
        }`
      );
      throw error;
    }
  }

}