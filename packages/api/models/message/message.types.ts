import { Guest, Message } from "@prisma/client";

export interface MessageWithRelations extends Message {
  sender: Guest;
  files: Array<{
    id: string;
    filename: string;
    fileUrl: string;
    fileType: string | null;
    size: number;
    createdAt: Date;
    uploadedBy: string;
    guest?: Guest | null;
  }>;
}

export interface Avatar {
  /** Name of the file (e.g. avatar.png) */
  filename: string;

  /** Public URL or path to access the file */
  fileUrl: string;
}

/**
 * Represents the sender of a message
 */
export interface IMessageSender {
  /** Unique identifier of the sender */
  id: string;

  /** Full name of the sender */
  name: string;

  /** Username (e.g. @john_doe) */
  username: string;

  /** Avatar metadata of the sender */
  avatar: Avatar;
}

/**
 * Represents a file uploaded by a user
 */
export interface IMessageFileUploader {
  /** Uploader ID */
  id: string;

  /** Uploader name */
  name: string;

  /** Uploader username */
  username: string;
}

/**
 * Represents a file attached to a message
 */
export interface IMessageFileAttachement {
  /** Unique file ID */
  id: string;

  /** File name (e.g. document.pdf) */
  filename: string;

  /** URL or path to access the file */
  fileUrl: string;

  /** MIME type of the file (e.g. image/png, application/pdf) */
  fileType?: string;

  /** File size in bytes */
  size: number;

  /** When the file was uploaded */
  createdAt: Date;

  /** Uploader information */
  uploader: IMessageFileUploader;
}

/**
 * Represents a chat message
 */
export interface IMessage {
  /** Unique ID of the message */
  id: string;

  /** Text content of the message (optional if files are attached) */
  text?: string;

  /** Whether the message is pinned */
  isPinned?: boolean;

  /** Whether the message is marked as an answer */
  isAnswered?: boolean;

  /** Whether the message is encrypted */
  isEncrypted?: boolean;

  /** Delivery status of the message */
  status?: 'Sent' | 'Delivered' | 'Read';

  /** ID of the parent message (for threads/replies) */
  parentId?: string;

  /** When the message was created */
  createdAt: Date;

  /** When the message was last updated */
  updatedAt: Date;

  /** Whether the message has been read */
  read?: boolean;

  /** Whether the message has been edited */
  edited?: boolean;

  /** The sender of the message */
  sender: IMessageSender;

  /** room Id */
  to: string;

  /** Files attached to the message */
  files?: IMessageFileAttachement[];
}

/**
 * Payload to create a new message
 */
export interface ICreateMessage {
  /** Message content (optional for file-only messages) */
  text?: string;

  /** Whether the message is pinned */
  isPinned?: boolean;

  /** Whether the message is marked as an answer */
  isAnswered?: boolean;

  /** Whether the message is encrypted */
  isEncrypted?: boolean;

  /** Initial delivery status of the message */
  status?: 'Sent' | 'Delivered' | 'Read';

  /** Parent message ID for replies or threads */
  parentId?: string;

  /** Sender's user ID */
  senderId: string;

  /** Room Id */
  to: string;

  /** Array of file IDs to attach */
  files?: string[];
}

/**
 * Payload to update an existing message
 */
export interface IUpdateMessage {
  /** Updated text content */
  text?: string;

  /** Updated pinned status */
  isPinned?: boolean;

  /** Updated answered status */
  isAnswered?: boolean;

  /** Updated encrypted status */
  isEncrypted?: boolean;

  /** Updated delivery status */
  status?: 'Sent' | 'Delivered' | 'Read';

  /** Updated time */
  updatedAt?: Date;

  /** Whether the message was read */
  read?: boolean;

  /** Whether the message has been edited */
  edited?: boolean;
}


