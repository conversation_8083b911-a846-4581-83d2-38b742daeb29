import { z } from "zod";
import { Database, IDatabaseMethodParams, DBLogger } from "../../utils/database";
import { IMessage, ICreateMessage, IUpdateMessage } from "./message.types"


export class Message extends Database<"Message"> {
    private model = 'message' as const;

    createSchema = z.object({
        text: z.string().min(1, { message: 'Text is required' }).optional(),
        isPinned: z.boolean().optional(),
        isAnswered: z.boolean().optional(),
        isEncrypted: z.boolean().optional(),
        status: z.enum(["Sent", "Delivered", "Read"]).optional(),
        parentId: z.string().min(1, { message: 'Parent ID is required' }).optional(),
        senderId: z.string().min(1, { message: 'Sender ID is required' }),
        to: z.string().min(1, { message: 'To is required' }),
        files: z.array(z.string()).optional(),
    });

    updateSchema = z.object({
        text: z.string().min(1, { message: 'Text is required' }).optional(),
        isPinned: z.boolean().optional(),
        isAnswered: z.boolean().optional(),
        isEncrypted: z.boolean().optional(),
        status: z.enum(["Sent", "Delivered", "Read"]).optional(),
        updatedAt: z.date().optional(),
        read: z.boolean().optional(),
        edited: z.boolean().optional(),
    });

    async createMessage(data: ICreateMessage): Promise<IMessage> {
        try {
            const validatedData = this.validateCreate(data);

            const { files: fileIds } = data;

            const result = await this.prisma.message.create({
                data: {
                    ...validatedData,
                    files: fileIds?.length ? {
                        connect: fileIds.map(fileId => ({ id: fileId }))
                    } : undefined
                },
                include: { 
                    files: { 
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                     },
                     sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    } 
                }
            });
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not created, operationContext: Message.createMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async updateMessage(id: string, data: IUpdateMessage): Promise<IUpdateMessage> {
        try {
            this.validateId(id);
            const validatedData = this.validateUpdate(data);
            const result = await this.prisma.message.update({
                where: { id },
                data: validatedData,
                select: {
                    text: true,
                    isPinned: true,
                    isAnswered: true,
                    isEncrypted: true,
                    status: true,
                    updatedAt: true,
                    read: true,
                }
            });
            return result;
        } catch (error) {
            DBLogger.error(`Message not updated, operationContext: Message.updateMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async deleteMessage(id: string): Promise<boolean> {
        try {
            this.validateId(id);
            await this.prisma.message.delete({
                where: { id }
            });
            return true;
        } catch (error) {
            DBLogger.error(`Message not deleted, operationContext: Message.deleteMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async editMessage(id: string, text: string): Promise<IMessage> {
        try {
            this.validateId(id);
            const validatedData = this.validateUpdate({ text });
            const result = await this.prisma.message.update({
                where: { id },
                data: {
                    ...validatedData,
                    edited: true
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not edited, operationContext: Message.editMessage, message: ${error.message || error}`);
            throw error;
        }
    }

    async markMessageRead(ids: string[]): Promise<boolean> {
        try {
            ids.map(id => this.validateId(id));
            const result = await this.prisma.message.updateMany({
                where: { id: { in: ids } },
                data: {
                    read: true
                }
            });
            return result.count === ids.length;
        } catch (error) {
            DBLogger.error(`Message not marked as read, operationContext: Message.markMessageRead, message: ${error.message || error}`);
            throw error;
        }
    }

    async createThread(parentId: string, data: ICreateMessage): Promise<IMessage> {
        try {
            this.validateId(parentId);
            const validatedData = this.validateCreate(data);
            const result = await this.prisma.message.create({
                data: {
                    ...validatedData,
                    parentId
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });

            return  Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Thread not created, operationContext: Message.createThread, message: ${error.message || error}`);
            throw error;
        }
    }

    public static TransformMessageToIMessage(message: any): IMessage {
        return {
            ...message,
            files: message.files.map((file: any) => {
                const { id, filename, fileUrl, fileType, size, createdAt, uploadedBy } = file;
                return { id, filename, fileUrl, fileType, size, createdAt, uploader: uploadedBy };
            }),
            sender: {
                id: message.sender.id,
                name: message.sender.name,
                username: message.sender.username,
                avatar: message.sender.avatar
            }
        }
    }

    async getThread(id: string): Promise<IMessage[]> {
        try {
            this.validateId(id);
            const result = await this.prisma.message.findMany({
                where: {
                    parentId: id
                },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });
            return result.map(Message.TransformMessageToIMessage);
        } catch (error) {
            DBLogger.error(`Thread not found, operationContext: Message.getThread, message: ${error.message || error}`);
            throw error;
        }
    }

    async getMessageById(id: string): Promise<IMessage | null> {
        try {
            this.validateId(id);
            const result = await this.prisma.message.findUnique({
                where: { id },
                include: {
                    files: {
                        select: {id: true, filename: true, fileUrl: true, fileType: true, size: true, createdAt: true, uploadedBy: true}
                    },
                    sender: {
                        select: { id: true, name: true, username: true, avatar: true }
                    }
                }
            });
            return Message.TransformMessageToIMessage(result);
        } catch (error) {
            DBLogger.error(`Message not found, operationContext: Message.getMessageById, message: ${error.message || error}`);
            throw error;
        }
    }
}