import { $Enums } from "@prisma/client";
export interface IAvatar {
  filename: string;
  fileUrl: string;
}

export interface ILocation {
  country:   string
  region:    string
  city:      String
  longitude?: String
  latitude?:  String
}

export type IOwnerRole = $Enums.OwnerRole;

export interface ICreateUser {
  displayName: string;
  name: string;
  username: string;
  email: string;
  password: string;
  awayModeEnabled?: boolean;
  avatar?: IAvatar;
  location?: ILocation;
  role?: IOwnerRole
  accountId?: string;
  companyIds?: string[];
  roomIds?: string[];
  analyticId: string;
}

export interface IUpdateUser {
  id?: string;
  displayName?: string;
  name?: string;
  username?: string;
  email?: string;
  awayModeEnabled?: boolean;
  avatar?: IAvatar;
  location?: ILocation;
  role?: IOwnerRole;
  accountId?: string;
  companyIds?: string[];
  roomIds?: string[];
  analyticId?: string;
  createdAt?: Date;
}

export interface IUserGeneralData {
  id: string;
  displayName: string;
  name: string;
  username: string;
  email: string;
  awayModeEnabled: boolean;
  avatar: IAvatar;
  location: ILocation;
  role: IOwnerRole;
  accountId: string;
  companyIds: string[];
  roomIds: string[];
  analyticId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface IUserLoginData {
  email: string;
  password: string;
}

export interface IUserDataWithPassword extends IUserGeneralData {
  password: string;
}

export interface IDelete {
  data: null
}

