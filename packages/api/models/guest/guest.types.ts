import { Location, Avatar, GuestStatus } from "@prisma/client";

export interface IGuest {
  id: string;
  displayName?: string;
  name: string;
  username: string;
  externalId: string;
  email?: string;
  phone?: string;
  awayModeEnabled: boolean;
  avatar?: Avatar;
  location?: Location;
  lastSeenAt: Date;
  createdAt: Date;
  updatedAt: Date;
  applicationIds?: string[];
  currentRoomId?: string;
  status: GuestStatus;
  hasHardBounced: boolean;
  markedEmailAsSpam: boolean;
  analyticId?: string;
  roomIds?: string[];
}


export interface IGuestMetaData {
  name?: string;
  externalId?: string;
  email?: string;
  phone?: string;
  displayName?: string;
  username?: string;
  applicationId?: string;
  companyId?: string;
  avatar?: { filename?: string; fileUrl?: string };
  location?: Location;
  lastSeenAt?: Date;
}


export interface IUpdateGuest {
  displayName?: string;
  name?: string;
  username?: string;
  email?: string;
  phone?: string;
  avatar?: Avatar;
  location?: Location;
  awayModeEnabled?: boolean;
  hasHardBounced?: boolean;
  markedEmailAsSpam?: boolean;
  currentRoomId?: string;
}