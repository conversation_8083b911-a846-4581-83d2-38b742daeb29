import { Room, RoomType } from "@prisma/client";
import { IMessage, IMessageFileUploader, MessageWithRelations } from "../message/message.types";
export { RoomType } from "@prisma/client";
import {  Guest, RoomPermission } from '@prisma/client';
import { IPermissionEntityType, IRoomPermission } from "../roomPermission/roomPermission.types";

export interface RoomWithRelations extends Room {
  messages: MessageWithRelations[];
  guests: Guest[];
  permissions: RoomPermission[];
}

export interface ICreateRoom {
  name: string;
  description?: string;
  avatar?: {
    filename: string;
    fileUrl: string;
  };
  applicationId?: string;
  userIds?: string[];
  guestIds?: string[];
  tagIds?: string[];
  anonymousIds?: string[];
  type?: RoomType;
  setting?: any,
  creatorId?: string;
  metaData?: any;
  expiresAt?: Date;
}

export interface IUpdateRoom {
  name?: string;
  description?: string;
  avatar?: {
    filename: string;
    fileUrl: string;
  };
  archived?: boolean;
  userIds?: string[];
  guestIds?: string[];
  tagIds?: string[];
  anonymousIds?: string[];
  membersCount?: number;
  onlineMembersCount?: number;
  type?: RoomType;
  setting?: Record<string, any>;
  updatorId?: string;
}

export interface IRoomGuests {
  id: string,
  name: string,
  username: string,
  avatar: {
    filename: string;
    fileUrl: string;
  },
  lastSeenAt?: Date,
  status: string,
  role?: IPermissionEntityType,
}


export interface IRoom {
  id: string,
  name: string,
  description?: string,
  avatar?: {
    filename: string;
    fileUrl: string;
  },
  archived?: boolean,
  expiresAt?: string | Date | null,
  type: RoomType,
  membersCount?: number,
  applicationId?: string,
  onlineMembersCount?: number,
  createdAt?: string | Date,
  updatedAt?: string | Date,
  setting?: any,
  metaData?: any,
  guests: {
    admins: IRoomGuests[],
    moderators: IRoomGuests[],
    members: IRoomGuests[],
  }
  messages: IMessage[],
}
export interface IRoomMedia {
  id: string,
  filename: string,
  fileUrl: string,
  fileType: string,
  size: number,
  createdAt: Date,
  uploader: IMessageFileUploader,
  messageId: string,
}
