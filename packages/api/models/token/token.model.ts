import { z } from "zod";
import { AuthService, Database, DBLogger } from "../../utils";
import { IToken, ICreateToken } from "./token.types"; 
import { TokenType } from "@prisma/client";

export class Token extends Database<"Token"> {
    private model = "token" as const;

    createSchema = z.object({
        type: z.enum(["ACCESS", "REFRESH", "INVITATION", "VERIFICATION"], {
            errorMap: () => ({ message: "type must be one of: ACCESS, REFRESH, INVITATION, VERIFICATION" })
        }),
        metaData: z.record(z.any()).optional(),
        expiresAt: z.date().optional()
    });

    updateSchema = z.object({
        token: z.string().min(1, "Token is required"),
        metaData: z.record(z.any()).optional()
    });

    async createToken(data: ICreateToken): Promise<IToken> {
        try {
            const validatedData = this.validateCreate(data);
            const token = AuthService.generateKey();
            if(validatedData?.metaData.externalId && validatedData?.metaData?.companyId){
                validatedData.externalId = `${validatedData.metaData.externalId}+${validatedData.metaData.companyId}`;
            }
            
            const result = await this.create({
                model: this.model,
                data: { 
                    ...validatedData, 
                    token,
                    expiresAt: data?.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) 
                }
            }) as IToken;
            return {...result, externalId: undefined}
        } catch (error) {
            DBLogger.error(`Token creation failed, operationContext: Token.createToken, message: ${error.message || error}`);
            throw error;
        }
    }

    async verifyToken(token: string, type: TokenType, companyId?: string): Promise<IToken | null> {
        try {
            const tokenRecord = await this.findUnique({
                model: this.model,
                where: { token }
            }) as IToken | null;

            if (!tokenRecord) {
                return null;
            }
            if (tokenRecord.type !== type) {
                return null;
            }
            if(companyId && companyId !== tokenRecord.metaData?.companyId)
                return null;
            if (
                tokenRecord.isDelete || 
                tokenRecord.isRevoke || 
                (tokenRecord.expiresAt && new Date(tokenRecord.expiresAt) < new Date())
            ) {
                return null;
            }

            return tokenRecord;
        } catch (error) {
            DBLogger.error(`Token verification failed, operationContext: Token.verifyToken, message: ${error.message || error}`);
            throw error;
        }
    }

    async markTokenAsUsed(id?: string, token?: string): Promise<IToken> {
        try {
            const where: any = {};
            if(id){
                where.id = id
            }else if(token){
                where.token = token
            }else{
                throw new Error("Validation failed: either token id or token is required")
            }
            return await this.update({
                model: this.model,
                where,
                data: { isDelete: true, deleteAt: new Date() }
            }) as IToken;
        } catch (error) {
            DBLogger.error(`Marking token as used failed, operationContext: Token.markTokenAsUsed, message: ${error.message || error}`);
            throw error;
        }
    }

    async findToken(companyId: string, token?: string, externalId?: string, type?: TokenType): Promise<IToken | null> {
        try {
            const where: any = {};
            
            if (token) where.token = token;
            if (externalId) where.externalId = `${externalId}+${companyId}`
            
            return await this.findUnique({
                model: this.model,
                where
            }) as IToken | null;

        } catch (error) {
            DBLogger.error(`Finding token failed, operationContext: Token.findToken, message: ${error.message || error}`);
            return null;
        }
    }
}
