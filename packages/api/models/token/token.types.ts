import { $Enums } from "@prisma/client";

export interface IToken {
    id: string;
    type: string;
    token: string;
    externalId?: string;
    isDelete: boolean;
    isRevoke: boolean;
    isExpired: boolean;
    metaData: any;
    deleteAt: Date;
    revokeAt: Date;
    expiresAt: Date;
    createdAt: Date;
    updatedAt: Date;
}

export interface ICreateToken {
    type: $Enums.TokenType;
    metaData?: Record<string, any>;
    expiresAt?: Date;
}

export interface IUpdateTokenMetaData {
    token: string;
    metaData: Object;
}
 