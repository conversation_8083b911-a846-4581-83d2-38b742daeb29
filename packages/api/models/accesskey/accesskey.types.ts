import { $Enums } from "@prisma/client";

export interface ICreateAccessKey {
    name: string;
    status?: $Enums.KeyStatus; 
    expiresAt: Date;
    creatorId: string; 
    companyId: string; 
  }
  
  export interface IUpdateAccessKey {
    name?: string;
    status?: $Enums.KeyStatus;
    expiresAt?: Date;
  }
  
  export interface IAccessKey {
    id?: string,
    name: string;
    apiKey: string;
    apiKeySecret: string;
    status: $Enums.KeyStatus;
    creatorId: string;
    companyId: string;
    expiresAt: Date;
    lastRequestAt: Date;
    createdAt: Date;
    updatedAt: Date;
  }
  