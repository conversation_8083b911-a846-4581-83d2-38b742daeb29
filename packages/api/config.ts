import { z } from "zod";
import dotenv from "dotenv";

dotenv.config();

// Define schema for environment variables
const envSchema = z.object({
  DATABASE_URL: z.string({ message: "DATABASE_URL must be a valid URL" }),
  BASE_URL: z.string().url({ message: "BASE_URL must be a valid URL" }),
  BASE_API_PATH: z.string().startsWith("/", { message: "BASE_API_PATH must start with '/'" }),
  JWT_SECRET: z.string().min(24, { message: "JWT_SECRET must be at least 24 characters" }),
  PORT: z.string().regex(/^\d+$/, { message: "PORT must be a valid number" }),
  NODE_ENV: z.enum(["development", "production", "test"]),
  ALLOWED_ORIGIN: z.string(), 
  AWS_REGION: z.string().nonempty({ message: "AWS_REGION is required" }),
  AWS_ACCESS_KEY_ID: z.string().nonempty({ message: "AWS_ACCESS_KEY_ID is required" }),
  AWS_SECRET_ACCESS_KEY: z.string().nonempty({ message: "AWS_SECRET_ACCESS_KEY is required" }),
  AWS_S3_BUCKET_NAME: z.string().nonempty({ message: "AWS_S3_BUCKET_NAME is required" }),
});

// Validate process.env and exit if invalid
export function validateEnv() {
  const parsedEnv = envSchema.safeParse(process.env);

  if (!parsedEnv.success) {
    console.error("❌ Invalid environment variables:", parsedEnv.error.format());
    process.exit(1); 
  }
  console.log("\n✅ Environment variables are valid\n");
}


export const config = envSchema.parse(process.env);


export const allowedOrigins = config.ALLOWED_ORIGIN.split(",").map(origin => origin.trim());
