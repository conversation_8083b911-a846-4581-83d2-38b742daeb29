# chat-node

> Node bindings to the [Chat API](https://api.chat.sparkstand.com/docs)



## Overview

It's  a TypeScript client wrapper for api backend of chat application, can be used both on frontend and backend:
*it's still development*

```typescript
import { SocketClient, SocketEvent, RoomType } from '@sparkstrand/chat-api-client';

// Create a new client
const client = new SocketClient({
  url: 'https://api.example.com',
  token: 'jwt-token or cookie token'
  debug: true
});

// Listen for events
client.on(SocketEvent.CONNECT, () => {
  console.log('Connected to server');
});

client.on(SocketEvent.NEW_MESSAGE, (data) => {
  console.log('New message:', data);
});

// Send a message
client.sendMessage({
  text: 'Hello, world!',
  roomId: 'room-id'
});

// Join a room
client.joinRoom('room-id');
```

## Implementation Notes

1. **Error Handling**: All socket events now have proper error handling with detailed logging.

2. **Reconnection Strategy**: The client automatically reconnects with exponential backoff.

3. **Message Queueing**: Messages sent while offline are queued and sent when the connection is restored.

4. **Type Safety**: All events and data structures are properly typed for better developer experience.

5. **Security**: Input validation and sanitization are performed on all user inputs.

## How to Use

To use the enhanced Socket.IO implementation:

1. Update the app.ts file to use the enhanced Socket.IO setup:

```typescript
import { setupEnhancedSocket } from './socket/enhancedIndex';

// Initialize Socket.IO with CORS and connection options
const io = new Server(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGIN,
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  },
  connectionStateRecovery: {
    maxDisconnectionDuration: 2 * 60 * 1000, 
    skipMiddlewares: true,
  },
  pingTimeout: 60000, 
  pingInterval: 25000, 
});

// Setup socket handlers
setupSocket(io);
```

2. Use the client wrapper in your frontend code:

```typescript
import { SocketClient, SocketEvent } from '@sparkstrand/socket-client';

const socket = new SocketClient({
  url: 'https://api.example.com',
  token: 'jwt-token'
});

socket.on(SocketEvent.CONNECT, () => {
  console.log('Connected to server');
});

socket.on(SocketEvent.NEW_MESSAGE, (data) => {
  console.log('New message:', data);
});
```

## Event Reference

### Connection Events

- `connect`: Emitted when the socket connects to the server
- `disconnect`: Emitted when the socket disconnects from the server
- `connect_error`: Emitted when a connection error occurs
- `reconnect`: Emitted when the socket reconnects to the server
- `reconnect_attempt`: Emitted when the socket attempts to reconnect
- `reconnect_error`: Emitted when a reconnection error occurs
- `reconnect_failed`: Emitted when all reconnection attempts fail

### Authentication Events

- `authenticated`: Emitted when the socket is authenticated
- `auth_error`: Emitted when an authentication error occurs

### Room Events

- `joinRoom`: Emitted to join a room
- `leaveRoom`: Emitted to leave a room
- `roomJoined`: Emitted when a room is joined
- `roomLeft`: Emitted when a room is left
- `createRoom`: Emitted to create a room
- `roomCreated`: Emitted when a room is created
- `userJoined`: Emitted when a user joins a room
- `userLeft`: Emitted when a user leaves a room

### Message Events

- `sendMessage`: Emitted to send a message
- `newMessage`: Emitted when a new message is received
- `markMessageRead`: Emitted to mark a message as read
- `messageRead`: Emitted when a message is marked as read

### Typing Events

- `typing`: Emitted when a user starts typing
- `stopTyping`: Emitted when a user stops typing
- `userTyping`: Emitted when a user is typing
- `userStoppedTyping`: Emitted when a user stops typing

### User Status Events

- `setUserStatus`: Emitted to set user status
- `userStatusChanged`: Emitted when a user's status changes

### Error Events

- `error`: Emitted when an error occurs


## Installation

```bash
yarn add @sparkstrand/chat-api-client
```



# Not Related just used for development inspiration
**This API client contains an SDK for server side use only and a seperate one for client-side operations.**

## Usage

Import Chat Client:

```typescript
import { Client } from '@sparkstrand/chat-api-client';
```

Create a client using access tokens:

```typescript
const client = new Client({ tokenAuth: { token: 'my_token' } });
```

## Request Options

This client library also supports passing in [`request` options](https://github.com/axios/axios#request-config):

```typescript
const client = new Client({ tokenAuth: { token: 'my_token' } });
client.useRequestOpts({
    baseURL: 'http://local.test-server.com',
});
```

Note that certain request options (such as `json`, and certain `headers` names cannot be overriden).

### Setting the API version

We version our API. You can specify which version of the API to use when performing API requests using request options:

```typescript
const client = new Client({ tokenAuth: { token: 'my_token' } });
client.useRequestOpts({
    headers: {
        'API-Version': 1.0,
    },
});
```

### Setting the API base url

If you would like to use a specific instance (e.g the european instance and not be redirected through a US instance) you can set the `baseUrl` as follows:

```typescript
const client = new Client({ tokenAuth: { token: 'my_token' } });
client.useRequestOpts({
    baseURL: 'https://api.eu.voltage.io',
});
```

## Examples

### Admins

#### [Retrieve admin]()

```typescript
const admin = await client.admins.find({ id: '123' });
```

#### [Set Admin away]()

```typescript
await client.admins.away({
    adminId: '123',
    enableAwayMode: true,
    enableReassignMode: false,
});
```

#### [List all activity logs]()

```typescript
await client.admins.listAllActivityLogs({
    before: new Date('Fri, 17 Dec 2021 18:02:18 GMT');,
    after: new Date('Fri, 17 Dec 2021 18:02:18 GMT');,
});
```

#### [List all admins]()

```typescript
const admins = await client.admins.list();
```

### Articles

#### [Create an article]()

```typescript
const article = await client.articles.create({
    title: 'Thanks for everything',
    description: 'English description',
    body: '<p>This is the body in html</p>',
    authorId: 1,
    state: 'published',
    parentId: 1,
    parentType: 'collection',
    translatedContent: {
        fr: {
            title: 'Allez les verts',
            description: 'French description',
            body: '<p>French body in html</p>',
            author_id: 1,
            state: 'published',
        },
    },
});
```

#### [Retrieve an article]()

```typescript
const response = await client.articles.find({ id: '123' });
```

#### [Update an article]()

```typescript
const article = await client.articles.update({
    id: '123',
    title: 'Thanks for everything',
    description: 'English description',
    body: '<p>This is the body in html</p>',
    authorId: 1,
    state: 'published',
    parentId: 1,
    parentType: 'collection',
    translatedContent: {
        fr: {
            title: 'Allez les verts',
            description: 'French description',
            body: '<p>French body in html</p>',
            author_id: 1,
            state: 'published',
        },
    },
});
```

#### [Delete an article]()

```typescript
await client.articles.delete({ id: '123' });
```

#### [List all articles]()

```typescript
const response = await client.articles.list({
    page: 3,
    perPage: 12,
});
```

### Companies

#### [Create a company]()

```typescript
const company = await client.companies.create({
    createdAt: dateToUnixTimestamp(new Date()),
    companyId: '46029',
    name: 'BestCompanyInc.',
    monthlySpend: 9001,
    plan: '1. Get pizzaid',
    size: 62049,
    website: 'http://the-best.one',
    industry: 'The Best One',
    customAttributes: {},
});
```

#### [Update a company]()

```typescript
const company = await client.companies.update({
    createdAt: dateToUnixTimestamp(new Date()),
    companyId: '46029',
    name: 'BestCompanyInc.',
    monthlySpend: 9001,
    plan: '1. Get pizzaid',
    size: 62049,
    website: 'http://the-best.one',
    industry: 'The Best One',
    customAttributes: {},
});
```

#### [Retrieve a company]()

##### By id

```typescript
const company = await client.companies.find({
    companyId: 123,
});
```

##### By name

```typescript
const company = await client.companies.find({
    name: 'bruh moment inc.',
});
```

#### [Delete a company]()

```typescript
const company = await client.companies.delete({
    id: 62049,
});
```

#### [List all companies]()

##### With pagination

```typescript
const companies = await client.companies.list({
    page: 1,
    perPage: 35,
    order: Order.DESC,
});
```

##### With TagId and SegmentId

```typescript
const companies = await client.companies.list({
    tagId: '1234',
    segmentId: '4567',
});
```

#### [Scroll over companies]()

##### Using infinite scroll

```typescript
const companies = await client.companies.scroll.each({});
```

##### Using manual scroll

```typescript
const companies = await client.companies.scroll.next({
    scrollParam: '123_soleil',
});
```

#### [Attach a contact]()

```typescript
const response = await client.companies.attachContact({
    contactId: '123',
    companyId: '234',
});
```

#### [Detach a contact]()

```typescript
const response = await client.companies.detachContact({
    contactId: '123',
    companyId: '234',
});
```

#### [List attached contacts]()

```typescript
const response = await client.companies.listAttachedContacts({
    companyId: '123',
    page: 1,
    perPage: 15,
});
```

#### [List attached segments]()

```typescript
const response = await client.companies.listAttachedSegments({
    companyId: '123',
});
```

### Contacts

#### [Create Contact]()

##### With User Role

```typescript
const user = await client.contacts.createUser({
    externalId: '536e564f316c83104c000020',
    phone: '+48370044567',
    name: 'Niko Bellic',
    avatar: 'https://nico-from-gta-iv.com/lets_go_bowling.jpg',
    signedUpAt: 1638203719,
    lastSeenAt: 1638203720,
    ownerId: '536e564f316c83104c000021',
    isUnsubscribedFromEmails: true,
});
```

##### With Lead Role

```typescript
const lead = await client.contacts.createLead({
    phone: '+48370044567',
    name: 'Roman Bellic',
    avatar: 'https://nico-from-gta-iv.com/lets_go_bowling_yey.jpg',
    signedUpAt: 1638203719,
    lastSeenAt: 1638203720,
    ownerId: '536e564f316c83104c000021',
    isUnsubscribedFromEmails: true,
});
```

#### [Retrieve a Contact]()

```typescript
const response = await client.contacts.find({ id: '123' });
```

#### [Update a Contact]()

```typescript
const response = await client.contacts.update({
    id: '123',
    role: Role.USER,
    name: 'Roman The Bowling Fan',
    customAttributes: {
        callBrother: "Hey Niko, it's me – Roman. Let's go bowling!",
    },
});
```

#### [Delete a Contact]()

```typescript
const response = await client.contacts.delete({ id: '123' });
```

#### [Archive a Contact]()

```typescript
const response = await client.contacts.archive({ id: '123' });
```

#### [Unarchive a Contact]()

```typescript
const response = await client.contacts.unarchive({ id: '123' });
```

#### [Merge two Contacts]()

```typescript
const response = await client.contacts.mergeLeadInUser({
    leadId: '123',
    userId: '234',
});
```

#### [Search for contacts]()

```typescript
const response = await client.contacts.search({
    data: {
        query: {
            operator: Operators.AND,
            value: [
                {
                    operator: Operators.AND,
                    value: [
                        {
                            field: 'updated_at',
                            operator: Operators.GREATER_THAN,
                            value: 1560436650,
                        },
                        {
                            field: 'conversation_rating.rating',
                            operator: Operators.EQUALS,
                            value: 1,
                        },
                    ],
                },
                {
                    operator: Operators.OR,
                    value: [
                        {
                            field: 'updated_at',
                            operator: Operators.GREATER_THAN,
                            value: 1560436650,
                        },
                        {
                            field: 'conversation_rating.rating',
                            operator: Operators.EQUALS,
                            value: 2,
                        },
                    ],
                },
            ],
        },
        pagination: {
            per_page: 5,
            starting_after:
                'WzE2MzU4NjA2NDgwMDAsIjYxODJiNjJlNDM4YjdhM2EwMWE4YWYxNSIsMl0=',
        },
        sort: { field: 'name', order: SearchContactOrderBy.ASC },
    },
});
```

#### [List all Contacts]()

##### With cursor

```typescript
const response = await client.contacts.list({
    perPage: 5,
    startingAfter:
        'WzE2MzU3NzU4NjkwMDAsIjYxODJiNjJhMDMwZTk4OTBkZWU4NGM5YiIsMl0=',
});
```

##### Without a cursor

```typescript
const response = await client.contacts.list();
```

#### [List attached companies]()

```typescript
const response = await client.contacts.listAttachedCompanies({
    id: '123',
    perPage: 5,
    page: 1,
});
```

#### [List attached tags]()

```typescript
const response = await client.contacts.listAttachedTags({ id: '123' });
```

#### [List attached segments]()

```typescript
const response = await client.contacts.listAttachedSegments({ id: '123' });
```

#### [List attached email subscriptions]()

```typescript
const response = await client.contacts.listAttachedEmailSubscriptions({
    id: '123',
});
```

### Conversations

#### [Create a conversation]()

```typescript
const response = await client.conversations.create({
    userId: '123',
    body: 'Hello darkness my old friend',
});
```

#### [Retrieve a conversation]()

##### Formatted text

```typescript
const response = await client.conversations.find({
    id: '123',
});
```

##### As plain text

```typescript
const response = await client.conversations.find({
    id: '123',
    inPlainText: true,
});
```

#### [Update a conversation]()

```typescript
const response = await client.conversations.update({
    id,
    markRead: true,
    customAttributes: {
        anything: 'you want',
    },
});
```

#### [Reply to a conversation]()

##### By id

###### As user

```typescript
const response = await client.conversations.replyByIdAsUser({
    id: '098',
    body: 'blablbalba',
    customerId: '123',
    attachmentUrls: '345',
});
```

###### As admin

```typescript
const response = await client.conversations.replyByIdAsAdmin({
    id: '098',
    adminId: '458',
    messageType: ReplyToConversationMessageType.NOTE,
    body: '<b>Bee C</b>',
    attachmentUrls: ['https://site.org/bebra.jpg'],
});
```

##### By last conversation

###### As user

```typescript
const response = await client.conversations.replyByLastAsUser({
    body: 'blablbalba',
    customerId: '123',
    attachmentUrls: '345',
});
```

###### As admin

```typescript
const response = await client.conversations.replyByLastAsAdmin({
    adminId: '458',
    messageType: ReplyToConversationMessageType.NOTE,
    body: '<b>Bee C</b>',
    attachmentUrls: ['https://site.org/bebra.jpg'],
});
```

#### [Assign a conversation]()

##### As team without assignment rules

```typescript
const response = await client.conversations.assign({
    id: '123',
    type: AssignToConversationUserType.TEAM,
    adminId: '456',
    assigneeId: '789',
    body: '<b>blablbalba</b>',
});
```

##### As team with assignment rules

```typescript
const response = await client.conversations.assign({
    id: '123',
    withRunningAssignmentRules: true,
});
```

#### [Snooze a conversation]()

```typescript
const response = await client.conversations.snooze({
    id: '123',
    adminId: '234',
    snoozedUntil: '1501512795',
});
```

#### [Close a conversation]()

```typescript
const response = await client.conversations.close({
    id: '123',
    adminId: '456',
    body: "That's it...",
});
```

#### [Open a conversation]()

```typescript
const response = await client.conversations.open({
    id: '123',
    adminId: '234',
});
```

#### [Attach a contact to group conversation]()

##### As admin, using customerId

```typescript
const response = await client.conversations.attachContactAsAdmin({
    id: '123',
    adminId: '234',
    customer: {
        customerId: '456',
    },
});
```

##### As contact, using customerId

```typescript
const response = await client.conversations.attachContactAsAdmin({
    id: '123',
    userId: '234',
    customer: {
        customerId: '456',
    },
});
```

#### [Delete a contact from group conversation as admin]()

```typescript
const response = await client.conversations.detachContactAsAdmin({
    conversationId: '123',
    contactId: '456',
    adminId: '789',
});
```

#### [Search for conversations]()

```typescript
const response = await client.conversations.search({
    data: {
        query: {
            operator: Operators.AND,
            value: [
                {
                    operator: Operators.AND,
                    value: [
                        {
                            field: 'updated_at',
                            operator: Operators.GREATER_THAN,
                            value: 1560436650,
                        },
                        {
                            field: 'conversation_rating.rating',
                            operator: Operators.EQUALS,
                            value: 1,
                        },
                    ],
                },
                {
                    operator: Operators.OR,
                    value: [
                        {
                            field: 'updated_at',
                            operator: Operators.GREATER_THAN,
                            value: 1560436650,
                        },
                        {
                            field: 'conversation_rating.rating',
                            operator: Operators.EQUALS,
                            value: 2,
                        },
                    ],
                },
            ],
        },
        pagination: {
            per_page: 5,
            starting_after:
                'WzE2MzU4NjA2NDgwMDAsIjYxODJiNjJlNDM4YjdhM2EwMWE4YWYxNSIsMl0=',
        },
        sort: {
            field: 'name',
            order: SearchConversationOrderBy.DESC,
        },
    },
});
```

#### [List all conversations]()

```typescript
const response = await client.conversations.list({
    startingAfter: 'WzE2NzA0MjI1MjkwMDAsMjQzMTY3NzA2ODcsMl0=',
    perPage: 10,
});
```

#### [Redact a conversation]()

```typescript
const response = await client.conversations.redactConversationPart({
    type: RedactConversationPartType.CONVERSATION_PART,
    conversationId: '123',
    conversationPartId: '456',
});
```

### Counts

#### [App Total Count]()

```typescript
const response = await client.counts.forApp();
```

#### [Conversation Count Model]()

```typescript
const response = await client.counts.countConversation();
```

#### [Admin Conversation Count Model]()

```typescript
const response = await client.counts.countAdminConversation();
```

#### [User Segment/Tag Count Model]()

##### Count User Segment

```typescript
const response = await client.counts.countUserSegment();
```

##### Count User Tag

```typescript
const response = await client.counts.countUserTag();
```

#### [Company User/Segment/Tag Count Model]()

##### Count Company Segment

```typescript
const response = await client.counts.countCompanySegment();
const response = await client.counts.countCompanyTag();
const response = await client.counts.countCompanyUser();
```

##### Count Company Tag

```typescript
const response = await client.counts.countCompanyTag();
```

##### Count Company User

```typescript
const response = await client.counts.countCompanyUser();
```

### Data Attributes

#### [Create Data Attribute]()

```typescript
const response = await client.dataAttributes.create({
    name: 'list_cda',
    model: ModelType.CONTACT,
    dataType: DataType.STRING,
    description: 'You are either alive or dead',
    options: [{ value: 'alive' }, { value: 'dead' }],
});
```

#### [Update Data Attribute]()

```typescript
const response = await client.dataAttributes.update({
    id: '123',
    description: 'You are either alive or dead',
    options: [{ value: 'alive' }, { value: 'dead' }],
    archived: true,
});
```

#### [List all Data Attributes]()

```typescript
const response = await client.dataAttributes.list({
    model: ModelType.CONTACT,
    includeArchived: true,
});
```

### Data Exports

#### [Create a export job]()

```typescript
const response = await client.dataExport.create({
    createdAtAfter: 1527811200,
    createdAtBefore: 1530316800,
});
```

#### [Retrieve a job status]()

```typescript
const response = await client.dataExport.find({id: export.id})
```

#### [Cancel a job]()

```typescript
const response = await client.dataExport.cancel({id: export.id})
```

### Events

#### [Submit a data event]()

```typescript
const response = await client.events.create({
    eventName: 'placed-order',
    createdAt: 1389913941,
    userId: 'f4ca124298',
    metadata: {
        order_date: 1392036272,
        stripe_invoice: 'inv_3434343434',
        order_number: {
            value: '3434-3434',
            url: 'https://example.org/orders/3434-3434',
        },
        price: {
            currency: 'usd',
            amount: 2999,
        },
    },
});
```

#### [List all data events]()

```typescript
const response = await client.events.listBy({
    userId: '1234',
    perPage: 2,
    summary: true,
    email: '<EMAIL>',
});
```

### Help Center - Collections

#### [Create a collection]()

```typescript
const collection = await client.helpCenter.collections.create({
    name: 'Thanks for everything',
    description: 'English description',
    translatedContent: {
        fr: {
            name: 'Allez les verts',
            description: 'French description',
        },
    },
});
```

#### [Retrieve a collection]()

```typescript
const response = await client.helpCenter.collections.find({ id: '123' });
```

#### [Update a collection]()

```typescript
const article = await client.helpCenter.collections.update({
    id: '123',
    name: 'Thanks for everything',
    description: 'English description',
    translatedContent: {
        fr: {
            name: 'Allez les verts',
            description: 'French description',
        },
    },
});
```

#### [Delete a collection]()

```typescript
await client.helpCenter.collections.delete({
    id: '123',
});
```

#### [List all collections]()

```typescript
const response = client.helpCenter.collections.list({
    page: 3,
    perPage: 12,
});
```

## Help Center - Sections

#### [Create a section]()

```typescript
const collection = await client.helpCenter.sections.create({
    name: 'Thanks for everything',
    parentId: '1234',
    translatedContent: {
        fr: {
            name: 'Allez les verts',
            description: 'French description',
        },
    },
});
```

#### [Retrieve a section]()

```typescript
const response = await client.helpCenter.sections.find({ id: '123' });
```

#### [Update a section]()

```typescript
const article = await client.helpCenter.sections.update({
    id: '123',
    name: 'Thanks for everything',
    parentId: '456',
    translatedContent: {
        fr: {
            name: 'Allez les verts',
            description: 'French description',
        },
    },
});
```

#### [Delete a section]()

```typescript
await client.helpCenter.sections.delete({
    id: '123',
});
```

#### [List all sections]()

```typescript
const response = client.helpCenter.sections.list({
    page: 3,
    perPage: 12,
});
```

### Messages

#### [Create a message]()

```typescript
const response = await client.messages.create({
    messageType: 'email',
    subject: 'This is our demand now',
    body: 'Destroy ponies',
    template: 'plain',
    from: {
        type: 'admin',
        id: '394051',
    },
    to: {
        type: 'user',
        id: '536e564f316c83104c000020',
    },
});
```

#### [Create conversation without contact reply]()

```typescript
const response = await client.messages.create({
    messageType: 'inapp',
    body: 'Look at me, I am a conversation now',
    from: {
        type: 'admin',
        id: '394051',
    },
    to: {
        type: 'user',
        id: '536e564f316c83104c000020',
    },
    createConversationWithoutContactReply: true,
});
```

### Notes

#### [Create a note]()

```typescript
const response = await client.notes.create({
    adminId: '12345',
    body: 'Shiny',
    contactId: '5678',
});
```

#### [Retrieve a note]()

```typescript
const response = await client.notes.find({ id: '123' });
```

#### [List all notes]()

```typescript
const response = await client.notes.list({
    contactId: '123',
    page: 2,
    perPage: 3,
});
```

### Segments

#### [Retrieve a segment]()

```typescript
const response = await client.segments.find({
    id: '123',
    includeCount: true,
});
```

#### [List all segments]()

```typescript
const response = await client.segments.list({
    includeCount: true,
});
```

### Subscriptions

#### [List all subscription types]()

```typescript
const response = await client.subscriptions.listTypes();
```

### PhoneCallRedirects

#### [Create a phone call redirect]()

```typescript
const response = await client.phoneCallRedirect.create({
    phone: '+353871234567',
});
```

### Tags

#### [Create or update a tag]()

##### Create

```typescript
const response = await client.tags.create({ name: 'haven' });
```

##### Update

```typescript
const response = await client.tags.update({ id: '123', name: 'haven' });
```

#### [Delete a tag]()

```typescript
const response = await client.tags.delete({ id: 'baz' });
```

#### [Attach a contact]()

```typescript
const response = await client.tags.tagContact({
    contactId: '123',
    tagId: '234',
});
```

#### [Attach a conversation]()

```typescript
const response = await client.tags.tagConversation({
    conversationId: '123',
    tagId: '456',
    adminId: '789',
});
```

#### [Tag companies]()

```typescript
const response = await client.tags.tagCompanies({
    tagName: 'gutenTag',
    companiesIds: ['123', '234', '456'],
});
```

#### [Untag companies]()

```typescript
const response = await client.tags.untagCompanies({
    tagName: 'gutenTag',
    companiesIds: ['123', '234', '456'],
});
```

#### [Untag conversation]()

```typescript
const response = await client.tags.untagConversation({
    conversationId: '123',
    tagId: '345',
    adminId: '678',
});
```

#### [Untag contact]()

```typescript
const response = await client.tags.untagContact({
    contactId: '123',
    tagId: '345',
});
```

#### [List all tags]()

```typescript
const response = await client.tags.list();
```

### Teams

#### [Retrieve a team]()

```typescript
const response = await client.teams.find({
    id: '123',
});
```

#### [List all teams]()

```typescript
const response = await client.teams.list();
```

### Visitors

#### [Retrieve a Visitor]()

```typescript
const response = await client.visitors.find({ id: '123' });
```

OR

```typescript
const response = await client.visitors.find({ userId: '123' });
```

#### [Update a Visitor]()

```typescript
const response = await client.visitors.update({
    userId: '123',
    name: 'anonymous bruh',
    customAttributes: {
        paid_subscriber: true,
    },
});
```

#### [Delete a Visitor]()

```typescript
const response = await client.visitors.delete({
    id,
});
```

#### [Convert a Visitor]()

```typescript
const response = await client.visitors.mergeToContact({
    visitor: {
        id: '123',
    },
    user: {
        userId: '123',
    },
    type: Role.USER,
});
```

### Identity verification

`@sparkstrand/chat-api-client` provides a helper for using [identity verification](https://docs.chat.io/enable-identity-verification...):

```node
import { IdentityVerification } from '@sparkstrand/chat-api-client';

IdentityVerification.userHash({
    secretKey: 's3cre7',
    identifier: '<EMAIL>',
});
```

## License

Apache-2.0

## Testing

```bash
yarn test
```

## Running the code locally

Compile using babel:

```bash
yarn prepublish
```

## Pull Requests

-   **Add tests!** Your patch won't be accepted if it doesn't have tests.

-   **Document any change in behaviour**. Make sure the README and any other
    relevant documentation are kept up-to-date.

-   **Create topic branches**. Don't ask us to pull from your master branch.

-   **One pull request per feature**. If you want to do more than one thing, send
    multiple pull requests.

-   **Send coherent history**. Make sure each individual commit in your pull
    request is meaningful. If you had to make multiple intermediate commits while
    developing, please squash them before sending them to us.
