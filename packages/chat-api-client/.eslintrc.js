
module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  // Environment settings
  env: {
    browser: true, // For frontend client and hooks
    node: true, // For backend client
    es2020: true, // Support modern JavaScript features
    jest: true, // For Jest tests
  },
  // Files to lint
  settings: {
    // React settings - Auto-detect React version
    react: {
      version: 'detect', 
    },
  },
  // Custom rules
  rules: {
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'no-console': 'off'
  },
  // Override rules for specific files
  overrides: [
    {
      // Test files
      files: ['tests/**/*.ts'], 
      // Allow 'any' in tests for mocks
      rules: {
        '@typescript-eslint/no-explicit-any': 'off', 
      },
    },
    { 
      // Declaration files
      files: ['lib/types/*.d.ts'], 

      // Allow unused vars in .d.ts
      rules: {
        '@typescript-eslint/no-unused-vars': 'off', 
      },
    },
  ],
  // Ignore compiled output and node_modules
  ignorePatterns: ['dist/', 'node_modules/', 'coverage/'],
};
