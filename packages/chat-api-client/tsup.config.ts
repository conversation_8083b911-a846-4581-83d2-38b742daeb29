import { defineConfig } from 'tsup';

export default defineConfig({
  entry: {
    'backend/index': 'lib/backend/index.ts',
    'frontend/index': 'lib/frontend/index.ts',
    'hooks/index': 'lib/hooks/index.ts',
    'context/index': 'lib/context/index.tsx',
    'types/index': 'lib/types/index.ts'
  },
  outDir: 'dist',
  format: ['cjs', 'esm'],
  dts: true,
  clean: true,
  splitting: false,
  sourcemap: true,
  minify: false,
  external: ['react', 'socket.io-client', 'eventemitter3', 'axios']
});
