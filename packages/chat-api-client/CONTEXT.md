# ChatContext Documentation

## Table of Contents
- [Overview](#overview)
- [Purpose](#purpose)
- [Installation](#installation)
- [Context Structure](#context-structure)
- [Setup](#setup)
- [Hooks](#hooks)
  - [useChat](#1-usechat)
  - [useChatMessage](#2-usechatmessage)
  - [useChatRoom](#3-usechatroom)
  - [useUserOnline](#4-useruseronline)
  - [useUserTyping](#5-useusertyping)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)
- [Socket Events](#socket-events)
- [Types](#types)
- [Limitations](#limitations)
- [Example Full Application](#example-full-application)

## Overview
The ChatContext is a React Context API implementation designed to manage real-time chat functionality using the ChatFrontendClient SDK. It provides a centralized interface for handling WebSocket connections, user authentication, messaging, room management, user status, and typing indicators in a chat application. The context is structured with a parent ChatContext and child contexts for modularity, accessible via custom hooks.

## Purpose
The ChatContext enables developers to:

- Establish and manage WebSocket connections to the Sparkstrand chat server.
- Authenticate users and manage sessions.
- Send and receive messages in real-time.
- Manage chat rooms (join, switch, leave, create, etc.).
- Track online users and their statuses.
- Display typing indicators.
- Ensure type-safe interactions with the chat server via TypeScript interfaces.

## Installation
Install the required dependency:
```bash
npm install @sparkstrand/chat-api-client
```

Configure environment variables for the server URL and API key:

**For Next.js**
```
NEXT_PUBLIC_CHAT_SERVER_URL=http://sparkstrand-chat-server-url
NEXT_PUBLIC_CHAT_API_KEY=your-api-key
NEXT_CHAT_API_SECRET=your-api-secret
```

**For Create React App**
```
REACT_APP_CHAT_SERVER_URL=http://sparkstrand-chat-server-url
REACT_APP_CHAT_API_KEY=your-api-key
REACT_APP_CHAT_API_SECRET=your-api-secret
```

**For Vite**
```
VITE_CHAT_SERVER_URL=http://sparkstrand-chat-server-url
VITE_CHAT_API_KEY=your-api-key
VITE_CHAT_API_SECRET=your-api-secret
```

## Context Structure
The ChatContext consists of one parent context and four child contexts, each with a dedicated hook:

### Parent Context
- **ChatContext** (via `useChat`): Manages the ChatFrontendClient instance, connection state, authentication, and errors.

### Child Contexts
- **MessageContext** (via `useChatMessage`): Handles message sending, editing, deletion, and reading.
- **RoomContext** (via `useChatRoom`): Manages room actions and data.
- **UserStatusContext** (via `useUserOnline`): Tracks user online status.
- **TypingContext** (via `useUserTyping`): Manages typing indicators.

## Setup
Wrap your application or component tree with ChatProvider to provide context:

```tsx
// app/layout.tsx
import { ChatProvider } from '@sparkstrand/chat-api-client/lib/context';
import { SocketClientOptions } from '@sparkstrand/chat-api-client/lib/types';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const chatOptions: SocketClientOptions = {
    url: process.env.NEXT_PUBLIC_CHAT_SERVER_URL!,
    apiKey: process.env.NEXT_PUBLIC_CHAT_API_KEY!,
    id: `guest-${Math.random().toString(36).substr(2, 9)}`,
    autoConnect: false,
    debug: process.env.NODE_ENV !== 'production',
    reconnection: true,
    reconnectionAttempts: 10,
    reconnectionDelay: 2000,
    reconnectionDelayMax: 30000,
    timeout: 30000,
  };

  return (
    <html lang="en">
      <body>
        <ChatProvider options={chatOptions}>
          {children}
        </ChatProvider>
      </body>
    </html>
  );
}
```

### SocketClientOptions:

- **url**: Chat server URL (required).
- **apiKey**: API key for authentication (required).
- **id**: Unique guest ID (optional, can be platform or server-assigned).
- **autoConnect**: Connect automatically (default: false).
- **debug**: Enable debug logging (default: false).
- **reconnection**: Enable reconnection (default: true).
- **reconnectionAttempts**: Max reconnection attempts (default: 10).
- **reconnectionDelay**: Initial reconnection delay (default: 2000 ms).
- **reconnectionDelayMax**: Max reconnection delay (default: 30000 ms).
- **timeout**: Connection timeout (default: 30000 ms).

## Hooks

### 1. useChat

**Purpose**: Manages the chat client, connection state, and authentication.

**Interface**:
```typescript
interface ChatContextType {
  client: ChatFrontendClient | null;
  isConnected: boolean;
  login: (id: string) => Promise<void>;
  disconnect: () => void;
  reconnect: () => Promise<void>;
  error: Error | null;
  userId: string | null;
}
```

**Properties and Methods**:

- **client**: ChatFrontendClient instance or null.
- **isConnected**: Indicates socket connection status.
- **login(id: string)**: Authenticates and connects the socket.
- **disconnect()**: Disconnects the socket and clears typing timeouts.
- **reconnect()**: Reconnects using the existing session.
- **error**: Connection or authentication error, or null.
- **userId**: Current user's ID, or null.

**Example**:
```tsx
'use client';

import { useEffect } from 'react';
import { useChat } from '@sparkstrand/chat-api-client/lib/context';

export default function ChatStatus() {
  const { isConnected, login, userId, error } = useChat();

  useEffect(() => {
    if (userId && !isConnected) {
      login(userId).catch((err) => console.error('Login failed:', err));
    }
  }, [userId, login, isConnected]);

  return (
    <div>
      <p>Status: {isConnected ? 'Connected' : 'Disconnected'}</p>
      {error && <p className="text-red-500">Error: {error.message}</p>}
      <button
        onClick={() => login(userId || `guest-${Math.random().toString(36).substr(2, 9)}`)}
        disabled={isConnected}
        className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
      >
        Login
      </button>
    </div>
  );
}
```

**Notes**:

- Check isConnected before socket operations.
- Handle error for user feedback.
- Call login on mount or when userId changes.

### 2. useChatMessage

**Purpose**: Manages message operations.

**Interface**:
```typescript
interface MessageContextType {
  sendMessage: (message: ISendMessage) => void;
  markMessageRead: (messageId: string) => void;
  editMessage: (message: IEditMessage) => void;
  deleteMessage: (message: IDeleteMessage) => void;
  messages: IMessage[];
}
```

**ISendMessage**:
```typescript
interface ISendMessage {
  text?: string;
  isPinned?: boolean;
  senderId?: string;
  isAnswered?: boolean;
  isEncrypted?: boolean;
  parentId?: string;
  to: string;
  files?: File[];
}
```

**Example**:
```tsx
'use client';

import { useState } from 'react';
import { useChat, useChatMessage } from '@sparkstrand/chat-api-client/lib/context';

export default function ChatBox({ roomId }: { roomId: string }) {
  const { userId } = useChat();
  const { messages, sendMessage } = useChatMessage();
  const [input, setInput] = useState('');

  const handleSend = () => {
    if (input.trim() && userId) {
      sendMessage({
        to: roomId,
        text: input,
        senderId: userId,
      });
      setInput('');
    }
  };

  return (
    <div className="flex flex-col h-96">
      <div className="flex-1 overflow-y-auto p-4">
        {messages
          .filter((msg) => msg.to === roomId)
          .map((msg) => (
            <div
              key={msg.id}
              className={`mb-2 ${msg.senderId === userId ? 'text-right' : 'text-left'}`}
            >
              <span
                className={`inline-block p-2 rounded ${msg.senderId === userId ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
              >
                {msg.text}
              </span>
            </div>
          ))}
      </div>
      <div className="p-4 border-t">
        <input
          type="text"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSend()}
          placeholder="Type a message..."
          className="w-full p-2 border rounded"
        />
      </div>
    </div>
  );
}
```

**Notes**:

- Filter messages by to (room ID) for room-specific display.
- Ensure userId is set before sending messages.
- Messages update automatically via SocketEvent.NEW_MESSAGE.

### 3. useChatRoom

**Purpose**: Manages room operations and data.

**Interface**:
```typescript
interface RoomContextType {
  joinRoom: (roomId: string) => void;
  switchRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  createRoom: (data: ICreateRoom) => void;
  emitGetListOfGuestRooms: () => void;
  rooms: IRoom[];
  setRooms: React.Dispatch<React.SetStateAction<IRoom[]>>;
  currentRoomData: IRoom | null;
  currentRoomMedia: IRoomMedia[];
  currentRoomMessages: IMessage[];
  getRoomDataById: (roomId: string) => void;
  getRoomMedia: (roomId: string) => void;
  getRoomMessages: (roomId: string, limit: number, cursor?: string) => void;
}
```

**ICreateRoom**:
```typescript
interface ICreateRoom {
  name: string;
  description?: string;
  avatar?: { filename: string; fileUrl: string };
  memberIds?: string[];
  applicationId?: string;
  type?: RoomType;
  setting?: Record<string, any>;
  creatorId?: string;
  metaData?: Record<string, any>;
  expiresAt?: Date;
}
```

**Example**:
```tsx
'use client';

import { useChat, useChatRoom } from '@sparkstrand/chat-api-client/lib/context';
import { RoomType } from '@sparkstrand/chat-api-client/lib/types';

export default function RoomSelector() {
  const { userId } = useChat();
  const { rooms, createRoom, switchRoom } = useChatRoom();

  const handleCreateRoom = () => {
    if (userId) {
      createRoom({
        name: `Room ${rooms.length + 1}`,
        type: RoomType.GROUP,
        memberIds: [], // Replace with valid server-assigned guest IDs
      });
    }
  };

  return (
    <div className="p-4">
      <button
        onClick={handleCreateRoom}
        className="mb-4 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Create Room
      </button>
      <ul>
        {rooms.map((room) => (
          <li
            key={room.id}
            onClick={() => switchRoom(room.id)}
            className="p-2 cursor-pointer hover:bg-gray-100 rounded"
          >
            {room.name}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

**Notes**:

- switchRoom triggers getRoomDataById and getRoomMedia to populate currentRoomData and currentRoomMessages.
- createRoom requires valid memberIds (guest IDs from the server).
- Use currentRoomMessages for room-specific messages to avoid filtering.

### 4. useUserOnline

**Purpose**: Tracks online users and statuses.

**Interface**:
```typescript
interface UserStatusContextType {
  setUserStatus: (status: UserStatus) => void;
  onlineUsers: string[];
  userStatuses: Record<string, UserStatus>;
}
```

**UserStatus**:
```typescript
enum UserStatus {
  ONLINE = 'online',
  AWAY = 'away',
  OFFLINE = 'offline',
}
```

**Example**:
```tsx
'use client';

import { useUserOnline } from '@sparkstrand/chat-api-client/lib/context';
import { UserStatus } from '@sparkstrand/chat-api-client/lib/types';

export default function OnlineUsers() {
  const { onlineUsers, userStatuses, setUserStatus } = useUserOnline();

  return (
    <div className="p-4">
      <h3 className="font-semibold mb-2">Online Users</h3>
      <select
        onChange={(e) => setUserStatus(e.target.value as UserStatus)}
        className="mb-4 p-2 border rounded"
      >
        <option value={UserStatus.ONLINE}>Online</option>
        <option value={UserStatus.AWAY}>Away</option>
        <option value={UserStatus.OFFLINE}>Offline</option>
      </select>
      <ul>
        {onlineUsers.map((userId) => (
          <li key={userId} className="p-1">
            User {userId} - {userStatuses[userId] || 'Online'}
          </li>
        ))}
      </ul>
    </div>
  );
}
```

**Notes**:

- onlineUsers updates via SocketEvent.USER_ONLINE and SocketEvent.USER_LEFT.
- setUserStatus broadcasts status changes.
- userStatuses maps user IDs to their status.

### 5. useUserTyping

**Purpose**: Manages typing indicators.

**Interface**:
```typescript
interface TypingContextType {
  sendTypingIndicator: (roomId: string) => void;
  sendStopTypingIndicator: (roomId: string) => void;
  typingUsers: Record<string, string[]>;
}
```

**Example**:
```tsx
'use client';

import { useState } from 'react';
import { useChat, useChatMessage, useUserTyping } from '@sparkstrand/chat-api-client/lib/context';

export default function ChatInput({ roomId }: { roomId: string }) {
  const { userId } = useChat();
  const { sendMessage } = useChatMessage();
  const { sendTypingIndicator, sendStopTypingIndicator, typingUsers } = useUserTyping();
  const [input, setInput] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
    if (e.target.value && userId) {
      sendTypingIndicator(roomId);
    } else {
      sendStopTypingIndicator(roomId);
    }
  };

  const handleSend = () => {
    if (input.trim() && userId) {
      sendMessage({
        to: roomId,
        text: input,
        senderId: userId,
      });
      setInput('');
      sendStopTypingIndicator(roomId);
    }
  };

  return (
    <div className="p-4">
      <div className="mb-2 text-sm text-gray-500">
        {typingUsers[roomId]?.length > 0 &&
          `${typingUsers[roomId].join(', ')} ${typingUsers[roomId].length > 1 ? 'are' : 'is'} typing...`}
      </div>
      <div className="flex gap-2">
        <input
          type="text"
          value={input}
          onChange={handleInputChange}
          onKeyPress={(e) => e.key === 'Enter' && handleSend()}
          placeholder="Type a message..."
          className="flex-1 p-2 border rounded"
        />
        <button
          onClick={handleSend}
          className="px-4 py-2 bg-blue-500 text-white rounded"
        >
          Send
        </button>
      </div>
    </div>
  );
}
```

**Notes**:

- sendTypingIndicator triggers when typing starts.
- sendStopTypingIndicator triggers after 3 seconds or on input clear/send.
- typingUsers maps room IDs to typing user IDs.

## Error Handling

Each hook provides error information:

- **useChat.error**: Captures connection, authentication, and socket errors.

**Example**:
```tsx
import { useChat } from '@sparkstrand/chat-api-client/lib/context';

export default function ErrorDisplay() {
  const { error } = useChat();

  if (!error) return null;

  return (
    <div className="p-4 bg-red-100 text-red-700 rounded">
      Error: {error.message}
    </div>
  );
}
```

## Best Practices

### Initialization

- Place ChatProvider at the highest required level (e.g., layout.tsx).
- Configure SocketClientOptions with valid url and apiKey.

### Authentication

- Call useChat().login on mount or userId change.
- Handle login errors with retries or user feedback.

### State Management

- Use useChat().isConnected for conditional rendering.
- Prefer useChatRoom().currentRoomMessages over filtering useChatMessage().messages.
- Cap message arrays to prevent memory issues:
```typescript
const MAX_MESSAGES = 1000;
setMessages((prev) => [...prev, message].slice(-MAX_MESSAGES));
```

### Performance

- Use useMemo in components to optimize context access.
- Debounce sendTypingIndicator to reduce socket emissions:
```typescript
import { debounce } from 'lodash';
const debouncedTyping = debounce(() => sendTypingIndicator(roomId), 500);
```

### Cleanup

- ChatProvider disconnects the socket and removes event listeners on unmount:
```typescript
useEffect(() => {
  const chatClient = new ChatFrontendClient(options);
  setClient(chatClient);
  // ... event listeners
  return () => {
    chatClient.removeAllListeners();
    chatClient.disconnect();
    setClient(null);
  };
}, [options]);
```

- Clear local state (e.g., input fields) on room switch.

## Socket Events

The ChatFrontendClient emits events defined in SocketEvent:

- **Connection**: CONNECT, DISCONNECT, CONNECT_ERROR, RECONNECT, etc.
- **Authentication**: AUTHENTICATED, AUTH_ERROR.
- **Rooms**: ROOM_JOINED, ROOM_LEFT, ROOM_SWITCHED, ROOM_CREATED, etc.
- **Messages**: NEW_MESSAGE, MESSAGE_EDITED, MESSAGE_DELETED, MESSAGE_READ.
- **Users**: USER_ONLINE, USER_LEFT, USER_STATUS_CHANGED.
- **Typing**: USER_TYPING, USER_STOPPED_TYPING.

See types for full details.

## Types

Key interfaces and enums:

- **SocketEvent**: Defines socket event types (e.g., CONNECT, NEW_MESSAGE).
- **RoomType**: DM, GROUP, SELF, ANONYMOUS.
- **UserStatus**: ONLINE, AWAY, OFFLINE.
- **IMessage**: Message structure with id, text, to, sender, etc.
- **ISendMessage**: Payload for sending messages.
- **IRoom**: Room details with id, name, type, messages, etc.
- **ICreateRoom**: Payload for creating rooms.
- **IRoomMedia**: Media files associated with rooms.

For complete type definitions, refer to the SDK's type exports.

## Limitations

- **Server Dependency**: Requires a running Sparkstrand chat server. Verify with url/api/v1/health.
- **Scalability**: Optimize for high message volumes with message caps and debouncing.
- **Message Duplication**: messages and currentRoomMessages require synchronization.

## Example Full Application

A complete chat interface using all hooks:

```tsx
// components/ChatApp.tsx
'use client';

import { useEffect, useState } from 'react';
import {
  useChat,
  useChatMessage,
  useChatRoom,
  useUserOnline,
  useUserTyping,
} from '@sparkstrand/chat-api-client/lib/context';
import { RoomType } from '@sparkstrand/chat-api-client/lib/types';

export default function ChatApp() {
  const { isConnected, login, userId, error } = useChat();
  const { messages, sendMessage } = useChatMessage();
  const { joinRoom, rooms, createRoom, switchRoom, currentRoomMessages } = useChatRoom();
  const { onlineUsers } = useUserOnline();
  const { typingUsers, sendTypingIndicator, sendStopTypingIndicator } = useUserTyping();
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');

  useEffect(() => {
    if (userId && !isConnected) {
      login(userId).catch((err) => console.error('Login failed:', err));
    }
    if (isConnected && !selectedRoom && rooms.length > 0) {
      joinRoom(rooms[0].id);
      setSelectedRoom(rooms[0].id);
    }
  }, [userId, isConnected, login, joinRoom, rooms, selectedRoom]);

  const handleSendMessage = () => {
    if (messageInput.trim() && selectedRoom && userId) {
      sendMessage({
        to: selectedRoom,
        text: messageInput,
        senderId: userId,
      });
      setMessageInput('');
      sendStopTypingIndicator(selectedRoom);
    }
  };

  const handleCreateRoom = () => {
    if (userId) {
      createRoom({
        name: `Room ${rooms.length + 1}`,
        type: RoomType.GROUP,
        memberIds: [], // Replace with valid guest IDs
      });
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-1/4 bg-white border-r p-4">
        <h2 className="text-lg font-semibold mb-4">Rooms</h2>
        <button
          onClick={handleCreateRoom}
          className="w-full bg-blue-500 text-white py-2 rounded mb-4"
        >
          Create Room
        </button>
        <ul>
          {rooms.map((room) => (
            <li
              key={room.id}
              onClick={() => {
                setSelectedRoom(room.id);
                switchRoom(room.id);
              }}
              className={`p-2 cursor-pointer rounded ${
                selectedRoom === room.id ? 'bg-blue-100' : 'hover:bg-gray-100'
              }`}
            >
              {room.name}
            </li>
          ))}
        </ul>
        <h3 className="text-lg font-semibold mt-4">Online Users</h3>
        <ul>
          {onlineUsers.map((id) => (
            <li key={id} className="p-1">
              User {id}
            </li>
          ))}
        </ul>
      </div>

      {/* Chat Area */}
      <div className="w-3/4 flex flex-col">
        <div className="bg-white p-4 border-b">
          <h2 className="text-lg font-semibold">
            {selectedRoom ? rooms.find((r) => r.id === selectedRoom)?.name : 'Select a room'}
          </h2>
          <p className="text-sm text-gray-500">
            {isConnected ? 'Connected' : 'Disconnected'}
            {error && ` | Error: ${error.message}`}
          </p>
        </div>
        <div className="flex-1 p-4 overflow-y-auto">
          {currentRoomMessages.map((msg) => (
            <div
              key={msg.id}
              className={`mb-4 flex ${
                msg.sender.id === userId ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`max-w-xs p-3 rounded-lg ${
                  msg.sender.id === userId ? 'bg-blue-500 text-white' : 'bg-white border'
                }`}
              >
                <p>{msg.text}</p>
                <p className="text-xs mt-1 opacity-70">
                  {new Date(msg.createdAt).toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))}
          {selectedRoom && typingUsers[selectedRoom]?.length > 0 && (
            <div className="text-sm text-gray-500 italic">
              {typingUsers[selectedRoom].join(', ')} {typingUsers[selectedRoom].length > 1 ? 'are' : 'is'} typing...
            </div>
          )}
        </div>
        <div className="bg-white p-4 border-t">
          <div className="flex gap-2">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => {
                setMessageInput(e.target.value);
                if (e.target.value && selectedRoom) {
                  sendTypingIndicator(selectedRoom);
                } else if (selectedRoom) {
                  sendStopTypingIndicator(selectedRoom);
                }
              }}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type a message..."
              className="flex-1 p-2 border rounded"
              disabled={!selectedRoom}
            />
            <button
              onClick={handleSendMessage}
              className="px-4 py-2 bg-blue-500 text-white rounded disabled:bg-gray-300"
              disabled={!messageInput.trim() || !selectedRoom}
            >
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
```

For issues, contact Bello or Bolu on Slack.
