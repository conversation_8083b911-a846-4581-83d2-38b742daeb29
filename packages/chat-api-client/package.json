{"name": "@sparkstrand/chat-api-client", "version": "0.0.7", "description": "Node and browser bindings to the Sparkstrand Chat API", "homepage": "https://github.com/sparkstrand/chat-application", "bugs": "https://github.com/sparkstrand/chat-application/issues", "author": {"name": "Spark Strand", "email": "<EMAIL>"}, "scripts": {"clean": "rm -rf dist", "lint": "eslint .", "lint:fix": "eslint . --fix", "build": "tsup", "test": "jest --maxWorkers=50%", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration --runInBand", "coverage": "jest --coverage", "prepublishOnly": "yarn clean && yarn lint && yarn build"}, "files": ["dist", "lib"], "main": "dist/backend/index.js", "module": "dist/backend/index.mjs", "types": "dist/backend/index.d.ts", "exports": {".": {"types": "./dist/backend/index.d.ts", "require": "./dist/backend/index.js", "import": "./dist/backend/index.mjs"}, "./frontend": {"types": "./dist/frontend/index.d.ts", "require": "./dist/frontend/index.js", "import": "./dist/frontend/index.mjs"}, "./hooks": {"types": "./dist/hooks/index.d.ts", "require": "./dist/hooks/index.js", "import": "./dist/hooks/index.mjs"}, "./context": {"types": "./dist/context/index.d.ts", "require": "./dist/context/index.js", "import": "./dist/context/index.mjs"}, "./types": {"types": "./dist/types/index.d.ts", "require": "./dist/types/index.js", "import": "./dist/types/index.mjs"}}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/sparkstrand/chat-application.git", "directory": "packages/chat-api-client"}, "dependencies": {"axios": "^1.6.0", "eventemitter3": "^5.0.1", "socket.io-client": "^4.7.5"}, "peerDependencies": {"react": "^18.2.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/react": "^18.2.20", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "eslint": "^8.44.0", "jest": "^29.7.0", "nock": "^13.3.1", "prettier": "^2.8.8", "ts-jest": "^29.1.2", "ts-node": "^10.9.1", "tsup": "^8.0.2", "typescript": "^5.5.4"}, "engines": {"node": ">=14.0.0 <=22.x"}, "license": "Apache-2.0", "packageManager": "yarn@4.5.3"}