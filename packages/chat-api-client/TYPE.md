# Chat-APi-Client Types Documentation

## Overview
This document describes the shared TypeScript types used by two wrapper clients for a real-time chat application. These types ensure type safety across the ChatFrontendClient and related React hooks (e.g., useSparkStrandChat, ChatContext). They define enums, interfaces, and data structures for socket events, rooms, messages, users, configuration options, and response formats.

## Purpose
The shared types provide:

- Type Safety: Consistent data structures for client-server communication.
- Interoperability: Shared definitions for wrapper clients and React integrations.
- Clarity: Well-documented interfaces for messages, rooms, users, and events.
- Extensibility: Flexible structures for handling various chat features (e.g., file attachments, message status).

## Types
### SocketEvent Enum
Purpose: Defines the event types emitted or listened to by the socket client for real-time communication.
Definition: 

```typescript
export enum SocketEvent {
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  CONNECT_ERROR = 'connect_error',
  RECONNECT = 'reconnect',
  RECONNECT_ATTEMPT = 'reconnect_attempt',
  RECONNECT_ERROR = 'reconnect_error',
  RECONNECT_FAILED = 'reconnect_failed',
  AUTHENTICATED = 'authenticated',
  AUTH_ERROR = 'auth_error',
  JOIN_ROOM = 'joinRoom',
  LEAVE_ROOM = 'leaveRoom',
  SWITCH_ROOM = 'switchRoom',
  ROOM_JOINED = 'roomJoined',
  ROOM_LEFT = 'roomLeft',
  ROOM_SWITCHED = 'roomSwitched',
  CREATE_ROOM = 'createRoom',
  ROOM_CREATED = 'roomCreated',
  LIST_OF_GUEST_ROOMS = 'listOfGuestRooms',
  USER_ONLINE = 'userOnline',
  SEND_MESSAGE = 'sendMessage',
  NEW_MESSAGE = 'newMessage',
  MESSAGE_READ = 'messageRead',
  MARK_MESSAGE_READ = 'markMessageRead',
  USER_JOINED = 'userJoined',
  USER_LEFT = 'userLeft',
  USER_STATUS_CHANGED = 'userStatusChanged',
  SET_USER_STATUS = 'setUserStatus',
  ERROR = 'error',
  TYPING = 'typing',
  STOP_TYPING = 'stopTyping',
  USER_TYPING = 'userTyping',
  USER_STOPPED_TYPING = 'userStoppedTyping',
}
```

### Usage:

- Client Actions: Events like JOIN_ROOM, SEND_MESSAGE, TYPING are emitted by the client to the server.
- Server Notifications: Events like NEW_MESSAGE, ROOM_JOINED, USER_TYPING are emitted by the server to clients.
- Connection Management: Events like CONNECT, DISCONNECT, RECONNECT handle socket lifecycle.

### Example:

```typescript
socket.on(SocketEvent.NEW_MESSAGE, (message: IMessage) => {
  console.log('New message:', message.text);
});
socket.emit(SocketEvent.JOIN_ROOM, 'room1');
```

### RoomType Enum
Purpose: Defines the types of chat rooms supported by the application.

Definition:
```typescript
export enum RoomType {
  DM = 'dm',
  GROUP = 'group',
  SELF = 'self',
  ANONYMOUS = 'anonymous',
}
```

Values:

- DM: Direct message room for one-on-one conversations.
- GROUP: Group chat room for multiple users.
- SELF: Room for a user's private notes or self-messages.
- ANONYMOUS: Room for anonymous users without persistent identities.

Usage: Specifies the room type when creating or joining rooms.

Example:
```typescript
socket.createRoom({
  name: 'Team Chat',
  type: RoomType.GROUP,
  membersId: ['user1', 'user2'],
});
```

### UserStatus Enum
Purpose: Defines the possible online statuses for users.
Definition:
```typescript
export enum UserStatus {
  ONLINE = 'online',
  AWAY = 'away',
  OFFLINE = 'offline',
}
```

Values:

- ONLINE: User is active and connected.
- AWAY: User is connected but inactive.
- OFFLINE: User is disconnected.

Usage: Tracks user presence and updates via SET_USER_STATUS and USER_STATUS_CHANGED events.
Example:
```typescript
socket.setUserStatus(UserStatus.AWAY);
```

### MessageStatus Enum
Purpose: Defines the delivery status of a message.
Definition:
```typescript
export enum MessageStatus {
  SENT = 'Sent',
  DELIVERED = 'Delivered',
  READ = 'Read',
}
```

Values:

- SENT: Message has been sent by the client.
- DELIVERED: Message has been received by the server or recipient.
- READ: Message has been viewed by the recipient.

Usage: Tracks message lifecycle for delivery confirmation.
Example:
```typescript
const message: IMessage = {
  id: 'msg1',
  text: 'Hello',
  status: MessageStatus.SENT,
};
```

### MessageSenderType Enum
Purpose: Defines the types of message senders in the chat system.
Definition:
```typescript
export enum MessageSenderType {
  ADMIN = 'admin',
  CHAT_AGENT = 'chatAgent',
  AI_AGENT = 'aiAgent',
  GUEST = 'guest',
}
```
Values:

- ADMIN: System administrator or moderator.
- CHAT_AGENT: Human customer support agent.
- AI_AGENT: Automated bot or AI responder.
- GUEST: Temporary or unauthenticated user.

Usage: Identifies the sender type in messages.
Example:
```typescript
const sender: MessageSender = {
  id: 'user1',
  type: MessageSenderType.GUEST,
};
```

### MessageSender Interface
Purpose: Represents the sender of a message.
Definition:
```typescript
export interface MessageSender {
  id: string;
  type: MessageSenderType | string;
}
```

Properties:

- id: Unique identifier for the sender.
- type: Sender type (from MessageSenderType or custom string).

Usage: Used in IMessage to specify the sender.
Example:
```typescript
const message: IMessage = {
  from: { id: 'guest1', type: MessageSenderType.GUEST },
  text: 'Hi there',
  to: 'room1',
};
```

### IMessage Interface
Purpose: Defines the structure of a chat message, based on the Prisma schema and authenticated event response.
Definition:
```typescript
export interface IMessage {
  id?: string;
  text?: string;
  from?: MessageSender;
  to?: string;
  roomId?: string;
  files?: Array<{
    filename: string;
    fileUrl: string;
  }>;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  read?: boolean;
  isPinned?: boolean;
  isAnswered?: boolean;
  isEncrypted?: boolean;
  status?: MessageStatus;
  parentId?: string | null;
  edited?: boolean;
  analyticId?: string;
  tempId?: string;
}
```

Properties:

- id: Unique message ID (optional, generated by server if not provided).
- text: Message content (optional for file-only messages).
- from: Sender information (MessageSender).
- to: Recipient ID for direct messages (optional).
- roomId: ID of the room the message belongs to (optional).
- files: Array of file attachments with filename and fileUrl (optional).
- createdAt: Timestamp when the message was created (optional).
- updatedAt: Timestamp when the message was last updated (optional).
- read: Whether the message has been read (optional).
- isPinned: Whether the message is pinned in the room (optional).
- isAnswered: Whether the message is a response to another (optional).
- isEncrypted: Whether the message is encrypted (optional).
- status: Message delivery status (MessageStatus, optional).
- parentId: ID of the parent message for replies (optional).
- edited: Whether the message has been edited (optional).
- analyticId: ID for analytics tracking (optional).
- tempId: Temporary client-side ID for pending messages (optional).

Usage: Represents messages sent or received via SEND_MESSAGE and NEW_MESSAGE events.
Example:
```typescript
const message: IMessage = {
  id: 'msg1',
  text: 'Hello, world!',
  from: { id: 'guest1', type: MessageSenderType.GUEST },
  roomId: 'room1',
  createdAt: new Date().toISOString(),
  status: MessageStatus.SENT,
  tempId: 'temp1',
};
socket.emit(SocketEvent.SEND_MESSAGE, message);
```
### IRoom Interface
Purpose: Defines the structure of a chat room, based on the Prisma schema and authenticated event response.
Definition:
```typescript
export interface IRoom {
  id: string;
  name: string;
  description?: string | null;
  avatar?: {
    filename: string;
    fileUrl: string;
  } | null;
  archived?: boolean;
  type: RoomType;
  membersCount?: number;
  onlineMembersCount?: number;
  createdAt?: string | Date;
  updatedAt?: string | Date;
  setting?: Record<string, any> | null;
  bannedGuestIds?: string[];
  expiresAt?: string | Date | null;
  messages?: IMessage[];
  applicationId?: string;
  userIds?: string[];
  guestIds?: string[];
  tagIds?: string[];
  anonymousIds?: string[];
}
```

Properties:

- id: Unique room ID.
- name: Room name.
- description: Optional room description.
- avatar: Optional room avatar with filename and fileUrl.
- archived: Whether the room is archived (optional).
- type: Room type (RoomType).
- membersCount: Total number of members (optional).
- onlineMembersCount: Number of online members (optional).
- createdAt: Timestamp when the room was created (optional).
- updatedAt: Timestamp when the room was last updated (optional).
- setting: Optional room settings as a key-value object.
- bannedGuestIds: Array of banned guest IDs (optional).
- expiresAt: Expiration timestamp for temporary rooms (optional).
- messages: Array of messages in the room (optional).
- applicationId: ID of the associated application (optional).
- userIds: Array of registered user IDs (optional).
- guestIds: Array of guest user IDs (optional).
- tagIds: Array of tag IDs for categorization (optional).
- anonymousIds: Array of anonymous user IDs (optional).

Usage: Represents rooms in LIST_OF_GUEST_ROOMS, ROOM_CREATED, and AuthenticatedData.
Example:
```typescript
const room: IRoom = {
  id: 'room1',
  name: 'General Chat',
  type: RoomType.GROUP,
  membersCount: 10,
  guestIds: ['guest1', 'guest2'],
};
```

### IUser Interface
Purpose: Defines the structure of a user in the chat system.
Definition:
```typescript
export interface IUser {
  id: string;
  type: 'user' | 'agent' | 'guest' | 'anonymous';
  username?: string;
  status?: UserStatus;
  currentRoomId?: string | null;
}
```

Properties:

- id: Unique user ID.
- type: User type (user, agent, guest, anonymous).
- username: Optional display name.
- status: Optional user status (UserStatus).
- currentRoomId: ID of the currently active room (optional).

Usage: Represents users in AuthenticatedData and user-related events (USER_ONLINE, USER_STATUS_CHANGED).
Example:
```typescript
const user: IUser = {
  id: 'guest1',
  type: 'guest',
  username: 'GuestUser',
  status: UserStatus.ONLINE,
  currentRoomId: 'room1',
};
```

### SocketClientOptions Interface
Purpose: Defines the configuration options for initializing the ChatFrontendClient.
Definition:
```typescript
export interface SocketClientOptions {
  url: string;
  id: string;
  path?: string;
  namespace?: string;
  apiKey?: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  reconnectionDelayMax?: number;
  timeout?: number;
  debug?: boolean;
}
```

Properties:

- url: Chat server URL (required).
- id: Unique user ID (required).
- path: Socket.IO path (optional, e.g., / by default).
- namespace: Socket.IO namespace (optional, e.g., General by default).
- apiKey: API key for authentication (optional).
- autoConnect: Whether to connect automatically (optional, default: false).
- reconnection: Enable reconnection attempts (optional, default: true).
- reconnectionAttempts: Number of reconnection attempts (optional, default: 10).
- reconnectionDelay: Initial reconnection delay in ms (optional, default: 2000).
- reconnectionDelayMax: Maximum reconnection delay in ms (optional, default: 30000).
- timeout: Connection timeout in ms (optional, default: 30000).
- debug: Enable debug logging (optional, default: false).

Usage: Configures the ChatFrontendClient instance.
Example:
```typescript
const options: SocketClientOptions = {
  url: 'http://localhost:3000',
  id: 'guest1',
  apiKey: 'your-api-key',
  debug: true,
};
const client = new ChatFrontendClient(options);
```

### ErrorData Interface
Purpose: Defines the structure of error responses from the server or client.
Definition:
```typescript
export interface ErrorData {
  message: string;
  success: boolean;
  statusCode: number;
}
```
Properties:

- message: Error description.
- success: Always false for errors.
- statusCode: HTTP status code (e.g., 401, 503).

Usage: Used in AUTH_ERROR, ERROR, and RECONNECT_ERROR events.
Example:
```typescript
socket.on(SocketEvent.AUTH_ERROR, (error: ErrorData) => {
  console.error(`Authentication error: ${error.message} (${error.statusCode})`);
});
```

### AuthenticatedData Interface
Purpose: Defines the structure of the response for the AUTHENTICATED event, containing user and room information.
Definition:
```typescript
export interface AuthenticatedData {
  user: IUser;
  status: string;
  applicationName: string;
  guestRoomData: IRoom[];
}
```
Properties:

- user: Authenticated user information (IUser).
- status: Connection status (e.g., Connected).
- applicationName: Name of the connected application.
- guestRoomData: Array of rooms the user has access to (IRoom[]).

Usage: Received when authentication succeeds via the AUTHENTICATED event.
Example:
```typescript
socket.on(SocketEvent.AUTHENTICATED, (data: AuthenticatedData) => {
  console.log(`Authenticated as ${data.user.username} in ${data.applicationName}`);
  console.log('Accessible rooms:', data.guestRoomData);
});
```

### IResponse<T> Interface
Purpose: Defines a generic response structure for API or socket responses.
Definition:
```typescript
export interface IResponse<T> {
  data?: T;
  success: boolean;
  message: string;
  statusCode: number;
}
```

Properties:

- data: Optional response data of type T.
- success: Whether the request was successful.
- message: Descriptive message.
- statusCode: HTTP status code (e.g., 200, 400).

Usage: Used for server responses, particularly for API calls or wrapped socket events.
Example:
```typescript
const response: IResponse<IMessage> = {
  data: { id: 'msg1', text: 'Hello', roomId: 'room1' },
  success: true,
  message: 'Message sent successfully',
  statusCode: 200,
};
```
Usage Examples
Handling Messages
```typescript
socket.on(SocketEvent.NEW_MESSAGE, (response: IResponse<IMessage>) => {
  if (response.success) {
    console.log('New message:', response.data?.text);
  }
});

const message: IMessage = {
  text: 'Hello, team!',
  from: { id: 'guest1', type: MessageSenderType.GUEST },
  roomId: 'room1',
  status: MessageStatus.SENT,
  tempId: 'temp1',
};
socket.emit(SocketEvent.SEND_MESSAGE, message);

Managing Rooms
socket.on(SocketEvent.ROOM_CREATED, (response: IResponse<IRoom>) => {
  if (response.success) {
    console.log('Created room:', response.data?.name);
  }
});

socket.emit(SocketEvent.CREATE_ROOM, {
  name: 'Project Team',
  type: RoomType.GROUP,
  guestIds: ['guest1', 'guest2'],
});

User Status Updates
socket.on(SocketEvent.USER_STATUS_CHANGED, (data: { userId: string; status: UserStatus }) => {
  console.log(`User ${data.userId} is now ${data.status}`);
});

socket.emit(SocketEvent.SET_USER_STATUS, UserStatus.ONLINE);
```

Best Practices

Type Safety:

Use SocketEvent for all socket event names to avoid typos.
Validate IMessage and IRoom properties before emitting to ensure required fields are present.


Optional Fields:

Handle optional fields (e.g., IMessage.files, IRoom.avatar) with proper null checks.
Use default values or fallback logic for optional timestamps (createdAt, updatedAt).


Error Handling:

Check ErrorData in error events to display user-friendly messages.
Use IResponse<T> to handle API response success/failure.


Extensibility:

Leverage IRoom.setting for custom room configurations.
Use IMessage.analyticId for tracking or analytics integration.


Message Status:

Update IMessage.status based on MESSAGE_READ events.
Use tempId for client-side message tracking before server confirmation.



Notes

Prisma Integration: IMessage and IRoom are based on Prisma schemas, so align client-side types with server-side models.
Extensibility: The types support advanced features like file attachments, pinned messages, and encrypted messages.
Anonymous Rooms: RoomType.ANONYMOUS and anonymousIds support anonymous interactions, requiring careful handling of user privacy.

_This documentation provides a clear, actionable guide for sparkstrand developers to use the shared types in a chat application, ensuring type-safe and consistent communication between client and server._

*For any issues contact Bello or Bolu on Slack*
