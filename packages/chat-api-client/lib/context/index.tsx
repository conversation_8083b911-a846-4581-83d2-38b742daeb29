import React, { createContext, useContext, useEffect, useState, useMemo } from 'react';
import { ChatFrontendClient } from '../frontend';
import { SocketClientOptions, IMessage,
   UserStatus, SocketEvent, IRoom, ISendMessage,
   IRoomMedia, ICreateRoom, IEditMessage, IDeleteMessage
  } from '../types';

interface ChatContextType {
  client: ChatFrontendClient | null;
  isConnected: boolean;
  login: (id: string) => Promise<void>;
  disconnect: () => void;
  reconnect: () => Promise<void>;
  error: Error | null;
  userId: string | null;
}

interface MessageContextType {
  sendMessage: (message: ISendMessage) => void;
  markMessageRead: (messageId: string) => void;
  editMessage: (message: IEditMessage) => void;
  deleteMessage: (message: IDeleteMessage) => void;
  messages: IMessage[];
}

interface RoomContextType {
  joinRoom: (roomId: string) => void;
  switchRoom: (roomId: string) => void;
  leaveRoom: (roomId: string) => void;
  createRoom: (data: ICreateRoom) => void;
  emitGetListOfGuestRooms: () => void;
  rooms: IRoom[];
  setRooms: React.Dispatch<React.SetStateAction<IRoom[]>>;
  currentRoomData: IRoom | null;
  currentRoomMedia: IRoomMedia[];
  currentRoomMessages: IMessage[];
  getRoomDataById: (roomId: string) => void;
  getRoomMedia: (roomId: string) => void;
  getRoomMessages: (roomId: string, limit: number, cursor?: string) => void;
}

interface UserStatusContextType {
  setUserStatus: (status: UserStatus) => void;
  onlineUsers: string[];
  userStatuses: Record<string, UserStatus>;
}

interface TypingContextType {
  sendTypingIndicator: (roomId: string) => void;
  sendStopTypingIndicator: (roomId: string) => void;
  typingUsers: Record<string, string[]>;
}

// Parent context
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Child contexts
const MessageContext = createContext<MessageContextType | undefined>(undefined);
const RoomContext = createContext<RoomContextType | undefined>(undefined);
const UserStatusContext = createContext<UserStatusContextType | undefined>(undefined);
const TypingContext = createContext<TypingContextType | undefined>(undefined);

interface ChatProviderProps {
  options: SocketClientOptions;
  children: React.ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ options, children }) => {
  const [client, setClient] = useState<ChatFrontendClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  const [userStatuses, setUserStatuses] = useState<Record<string, UserStatus>>({});
  const [typingUsers, setTypingUsers] = useState<Record<string, string[]>>({});
  const [rooms, setRooms] = useState<IRoom[]>([]);
  const [currentRoomData, setCurrentRoomData] = useState<IRoom | null>(null);
  const [currentRoomMedia, setCurrentRoomMedia] = useState<IRoomMedia[]>([]);
  const [currentRoomMessages, setCurrentRoomMessages] = useState<IMessage[]>([]);

  useEffect(() => {
    const chatClient = new ChatFrontendClient(options);
    setClient(chatClient);

    // Setup connection event listeners
    chatClient.on(SocketEvent.CONNECT, () => {
      setIsConnected(chatClient.isConnected());
      setError(null);
    });

    chatClient.on(SocketEvent.DISCONNECT, (reason) => {
      setIsConnected(chatClient.isConnected());
      setError(new Error(`Disconnected: ${reason}`));
    });

    chatClient.on(SocketEvent.CONNECT_ERROR, (err) => {
      setError(err);
    });

    chatClient.on(SocketEvent.AUTH_ERROR, (err) => {
      setError(new Error(err.message));
    });

    chatClient.on(SocketEvent.ERROR, (err) => {
      setError(new Error(err.message));
    });

    // Room Switch events -  the user need to call switchRoom() to populate the currentRoomData and currentRoomMessages. which you then access via useChatRoom().currentRoomData and useChatRoom().currentRoomMessages
    chatClient.on(SocketEvent.ROOM_SWITCHED, (data) => {
      const roomId = typeof data === 'object' ? data.roomId : data;
      console.log('[ChatContext] ROOM_SWITCHED event received:', roomId);
      if (roomId) {
        setCurrentRoomMessages([]);
        setCurrentRoomData(null);
        setCurrentRoomMedia([]);

        console.log('[ChatContext] Fetching room data for:', roomId);
        //  This will populate the currentRoomData and currentRoomMessages.
        chatClient.getRoomDataById(roomId);

        //  This will populate the currentRoomMedia
        chatClient.getRoomMedia(roomId);

        // Set a backup timeout to fetch room data if not received
        setTimeout(() => {
          console.log('[ChatContext] Backup room data fetch for:', roomId);
          chatClient.getRoomDataById(roomId);
        }, 2000);
      }
    });

    // Message events
    chatClient.on(SocketEvent.NEW_MESSAGE, (message: IMessage) => {
      setMessages((prev) => [...prev, message]);
      setCurrentRoomMessages(prev => {
        if(message.to === currentRoomData?.id) {
          return [...prev, message];
        }
        return prev;
      });
    });

    chatClient.on(SocketEvent.MESSAGE_READ, (messageId: string) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, read: true } : msg
        )
      );

      setCurrentRoomMessages(prev => prev.map(msg => msg.id === messageId ? { ...msg, read: true } : msg));
    });

    chatClient.on(SocketEvent.MESSAGE_DELETED, (messageId: string) => {
      setMessages((prev) => prev.filter(msg => msg.id !== messageId));
      setCurrentRoomMessages(prev => prev.filter(msg => msg.id !== messageId));
    });

    chatClient.on(SocketEvent.MESSAGE_EDITED, (message: IMessage) => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === message.id ? message : msg
        )
      );
      setCurrentRoomMessages(prev => prev.map(msg => msg.id === message.id ? message : msg));
    });

    chatClient.on(SocketEvent.MESSAGE_DELETED, (message: IMessage) => {
      setMessages((prev) => prev.filter(msg => msg.id !== message.id));
      setCurrentRoomMessages(prev => prev.filter(msg => msg.id !== message.id));
    });

    // Rooms events
    chatClient.on(SocketEvent.LIST_OF_GUEST_ROOMS, (rooms: IRoom[]) => {
      setRooms(rooms);
    });

    chatClient.on(SocketEvent.ROOM_DATA, (room: IRoom) => {
      console.log('[ChatContext] ROOM_DATA event received:', room?.id, room?.name, 'Messages:', room?.messages?.length);
      setCurrentRoomData(room);
      setCurrentRoomMessages(room.messages || []);
    });

    chatClient.on(SocketEvent.ROOM_MEDIA, (media: IRoomMedia[]) => {
      setCurrentRoomMedia(media);
    });

    chatClient.on(SocketEvent.ROOM_CREATED, (room: IRoom) => {
      setRooms((prev) => [...prev, room]);
    });

    chatClient.on(SocketEvent.ROOM_MESSAGES, (messages: IMessage[]) => {
      setCurrentRoomMessages(messages);
    });

    // User status events
    chatClient.on(SocketEvent.USER_ONLINE, (userId: string) => {
      setOnlineUsers((prev) => [...new Set([...prev, userId])]);
    });

    chatClient.on(SocketEvent.USER_LEFT, (userId: string) => {
      setOnlineUsers((prev) => prev.filter((id) => id !== userId));
    });

    chatClient.on(SocketEvent.USER_STATUS_CHANGED, ({ userId, status }: { userId: string; status: UserStatus }) => {
      setUserStatuses((prev) => ({ ...prev, [userId]: status }));
    });

    // Typing events
    chatClient.on(SocketEvent.USER_TYPING, ({ roomId, userId }: { roomId: string; userId: string }) => {
      setTypingUsers((prev) => ({
        ...prev,
        [roomId]: [...(prev[roomId] || []), userId],
      }));
    });

    chatClient.on(SocketEvent.USER_STOPPED_TYPING, ({ roomId, userId }: { roomId: string; userId: string }) => {
      setTypingUsers((prev) => ({
        ...prev,
        [roomId]: (prev[roomId] || []).filter((id) => id !== userId),
      }));
    });

    return () => {
      chatClient.removeAllListeners();
      chatClient.disconnect();
      setClient(null);
    };
  }, [options]);

  const login = async (id: string) => {
    if (!client) throw new Error('Chat client not initialized');
    try {
      await client.login(id);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Login failed'));
      throw err;
    }
  };

  const disconnect = () => {
    if (client) {
      client.disconnect();
      setIsConnected(client.isConnected());
    }
  };

  const reconnect = async () => {
    if (!client) throw new Error('Chat client not initialized');
    try {
      await client.reconnect();
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Reconnection failed'));
      throw err;
    }
  };


  const chatContextValue = useMemo(
    () => ({
      client,
      isConnected,
      login,
      disconnect,
      reconnect,
      error,
      userId: client?.getUserId() || null,
    }),
    [client, isConnected, error]
  );

  const messageContextValue = useMemo(
    () => ({
      sendMessage: (message: ISendMessage) => client?.sendMessage(message),
      markMessageRead: (messageId: string) => client?.markMessageRead(messageId),
      editMessage: (message: IEditMessage) => client?.editMessage(message),
      deleteMessage: (message: IDeleteMessage) => client?.deleteMessage(message),
      messages,
    }),
    [client, messages]
  );

  const roomContextValue = useMemo(
    () => ({
      joinRoom: (roomId: string) => client?.joinRoom(roomId),
      switchRoom: (roomId: string) => client?.switchRoom(roomId),
      leaveRoom: (roomId: string) => client?.leaveRoom(roomId),
      createRoom: (data: ICreateRoom) => client?.createRoom(data),
      emitGetListOfGuestRooms: () => client?.getListOfGuestRooms(),
      setRooms,
      rooms,
      currentRoomData,
      currentRoomMedia,
      currentRoomMessages,
      getRoomDataById: (roomId: string) => client?.getRoomDataById(roomId),
      getRoomMedia: (roomId: string) => client?.getRoomMedia(roomId),
      getRoomMessages: (roomId: string, limit: number, cursor?: string) => client?.getRoomMessages(roomId, limit, cursor),
    }),
    [client, rooms, currentRoomData, currentRoomMedia, currentRoomMessages]
  );

  const userStatusContextValue = useMemo(
    () => ({
      setUserStatus: (status: UserStatus) => client?.setUserStatus(status),
      onlineUsers,
      userStatuses,
    }),
    [client, onlineUsers, userStatuses]
  );

  const typingContextValue = useMemo(
    () => ({
      sendTypingIndicator: (roomId: string) => client?.sendTypingIndicator(roomId),
      sendStopTypingIndicator: (roomId: string) => client?.sendStopTypingIndicator(roomId),
      typingUsers,
    }),
    [client, typingUsers]
  );

  return (
    <ChatContext.Provider value={chatContextValue}>
      <MessageContext.Provider value={messageContextValue}>
        <RoomContext.Provider value={roomContextValue}>
          <UserStatusContext.Provider value={userStatusContextValue}>
            <TypingContext.Provider value={typingContextValue}>
              {children}
            </TypingContext.Provider>
          </UserStatusContext.Provider>
        </RoomContext.Provider>
      </MessageContext.Provider>
    </ChatContext.Provider>
  );
};

// Hooks for accessing contexts
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (!context) throw new Error('useChat must be used within a ChatProvider');
  return context;
};

export const useChatMessage = (): MessageContextType => {
  const context = useContext(MessageContext);
  if (!context) throw new Error('useChatMessage must be used within a ChatProvider');
  return context;
};

export const useChatRoom = (): RoomContextType => {
  const context = useContext(RoomContext);
  if (!context) throw new Error('useChatRoom must be used within a ChatProvider');
  return context;
};

export const useUserOnline = (): UserStatusContextType => {
  const context = useContext(UserStatusContext);
  if (!context) throw new Error('useUserOnline must be used within a ChatProvider');
  return context;
};

export const useUserTyping = (): TypingContextType => {
  const context = useContext(TypingContext);
  if (!context) throw new Error('useUserTyping must be used within a ChatProvider');
  return context;
};