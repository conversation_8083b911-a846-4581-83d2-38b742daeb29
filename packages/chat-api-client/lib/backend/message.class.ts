import {
    IResponse,
    IMessage,
    SearchQuery,
    QueryNode,
    QueryCondition,
    Operators,
    Pagination,
    Sort,
    RestClientAuthOptions,
    ISendMessage,
    IUpdateMessage,
  } from '../types';
  import { IRestClient } from './restful.client';
  import { validateRequiredFields } from './utils/validation';
  
  /**
   * Query parser for translating SearchQuery to Prisma-compatible MongoDB queries.
   */
  class QueryParser {
    /**
     * Parse a SearchQuery into a Prisma query object.
     * @param searchQuery - The search query to parse.
     * @returns Prisma query object with where, skip, take, and orderBy clauses.
     */
    public static parse(searchQuery: SearchQuery): Record<string, any> {
      const query = this.parseQueryNode(searchQuery.data.query);
      const pagination = this.parsePagination(searchQuery.pagination);
      const sort = this.parseSort(searchQuery.sort);
  
      return {
        where: query,
        ...pagination,
        ...sort,
      };
    }
  
    /**
     * Parse a QueryNode or QueryCondition into a Prisma where clause.
     * @param node - The query node or condition to parse.
     * @returns Prisma where clause.
     */
    private static parseQueryNode(node: QueryNode | QueryCondition): Record<string, any> {
      if ('operator' in node && (node.operator === Operators.AND || node.operator === Operators.OR)) {
        const conditions = node.value.map((child) => this.parseQueryNode(child));
        return {
          [node.operator.toLowerCase()]: conditions,
        };
      }
  
      const condition = node as QueryCondition;
      const field = condition.field === 'roomId' ? 'to' : condition.field; 
      let prismaOperator: string | Record<string, any>;
  
      switch (condition.operator) {
        case Operators.EQUALS:
          prismaOperator = condition.value;
          break;
        case Operators.NOT_EQUALS:
          prismaOperator = { not: condition.value };
          break;
        case Operators.GREATER_THAN:
          prismaOperator = { gt: condition.value };
          break;
        case Operators.LESS_THAN:
          prismaOperator = { lt: condition.value };
          break;
        case Operators.GREATER_THAN_OR_EQUAL:
          prismaOperator = { gte: condition.value };
          break;
        case Operators.LESS_THAN_OR_EQUAL:
          prismaOperator = { lte: condition.value };
          break;
        case Operators.IN:
          prismaOperator = { in: condition.value };
          break;
        case Operators.CONTAINS:
          prismaOperator = { contains: condition.value, mode: 'insensitive' };
          break;
        default:
          throw new Error(`Unsupported operator: ${condition.operator}`);
      }
  
      // Handle nested fields (e.g., from.id)
      if (field.includes('.')) {
        const [parent, child] = field.split('.');
        return { [parent]: { [child]: prismaOperator } };
      }
  
      return { [field]: prismaOperator };
    }
  
    /**
     * Parse pagination parameters.
     * @param pagination - Pagination options.
     * @returns Prisma skip and take clauses.
     */
    private static parsePagination(pagination?: Pagination): Record<string, any> {
      if (!pagination) return {};
      return {
        skip: pagination.skip || (pagination.per_page && pagination.starting_after ? parseInt(pagination.starting_after) : 0),
        take: pagination.take || pagination.per_page || 10,
      };
    }
  
    /**
     * Parse sort parameters.
     * @param sort - Sort options.
     * @returns Prisma orderBy clause.
     */
    private static parseSort(sort?: Sort): Record<string, any> {
      if (!sort) return {};
      const field = sort.field === 'roomId' ? 'to' : sort.field;
      return {
        orderBy: { [field]: sort.order.toLowerCase() },
      };
    }
  }
  
  /**
   * Class for managing message-related operations.
   */
  export class Messages {
    private basePath = 'api/v1/messages';
  
    /**
     * Constructor for Messages.
     * @param restClient - The REST client for HTTP requests.
     */
    constructor(private readonly restClient: IRestClient) {}
  
    /**
     * Create a new message in a room.
     * @param data - Message creation data.
     * @param authOptions - Authentication options.
     * @returns Response containing the created message or error.
     */
    public async create(
      data: ISendMessage,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<IMessage | null>> {
      const validationError = validateRequiredFields(
        {
          to: data.to,
        },
        'create message'
      );
      if (validationError) return validationError;
  
      return await this.restClient.POST<IMessage | null>(data, `${this.basePath}`, authOptions);
    }
  
    /**
     * Update an existing message.
     * @param messageId - The ID of the message to update.
     * @param data - Message update data.
     * @param authOptions - Authentication options.
     * @returns Response containing the updated message or error.
     */
    public async update(
      messageId: string,
      data: IUpdateMessage,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<IMessage | null>> {
      const validationError = validateRequiredFields({ messageId }, 'update message');
      if (validationError) return validationError;
  
      if (!Object.keys(data).length) {
        return {
          success: false,
          message: 'No fields provided for message update',
          statusCode: 400,
          data: null,
        };
      }
  
      return await this.restClient.PUT<IMessage | null>(data, `${this.basePath}/${messageId}`, authOptions);
    }
  
    /**
     * Delete a message.
     * @param messageId - The ID of the message to delete.
     * @param authOptions - Authentication options.
     * @returns Response indicating success or error.
     */
    public async delete(
      messageId: string,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<null>> {
      const validationError = validateRequiredFields({ messageId }, 'delete message');
      if (validationError) return validationError;
  
      return await this.restClient.DELETE<null>(null, `${this.basePath}/${messageId}`, authOptions);
    }
  
    /**
     * Retrieve a message by ID.
     * @param messageId - The ID of the message.
     * @param authOptions - Authentication options.
     * @returns Response containing the message or error.
     */
    public async get(
      messageId: string,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<IMessage | null>> {
      const validationError = validateRequiredFields({ messageId }, 'get message');
      if (validationError) return validationError;
  
      return await this.restClient.GET<IMessage | null>(null, `${this.basePath}/${messageId}`, authOptions);
    }
  
    /**
     * Search messages with a query.
     * @param query - Search query for filtering messages.
     * @param authOptions - Authentication options.
     * @returns Response containing the matching messages or error.
     */
    public async search(
      query: SearchQuery,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<IMessage[]>> {
      const prismaQuery = QueryParser.parse(query);
      return await this.restClient.POST<IMessage[]>(prismaQuery, `${this.basePath}/search`, authOptions);
    }
  
    /**
     * Mark a message as read.
     * @param messageId - The ID of the message to mark as read.
     * @param authOptions - Authentication options.
     * @returns Response indicating success or error.
     */
    public async markAsRead(
      messageId: string,
      authOptions: RestClientAuthOptions = { token: true, apiKey: false, apiKeySecret: false }
    ): Promise<IResponse<null>> {
      const validationError = validateRequiredFields({ messageId }, 'mark message as read');
      if (validationError) return validationError;
  
      return await this.restClient.PUT<null>(null, `${this.basePath}/${messageId}/read`, authOptions);
    }
  }