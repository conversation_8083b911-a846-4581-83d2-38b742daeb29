import { Component } from "react";
import styled from "styled-components";
import ChatListItem from "./ChatListItem";
import SearchResult from "./SearchResult";
import { chats } from "./data/chats";
import { messages } from "./data/messages";

const StyledList = styled.ul`
  padding: 0;
  margin: 0;
`;

type Props = {
  showChatScreen: (id: number) => void;
  searchTerm: string;
};

interface Message {
  id: number;
  content: string;
  status: string;
}

class ChatList extends Component<Props> {
  filterBySearchTerm = (msg: Message) => {
    return (
      msg.content.toUpperCase().indexOf(this.props.searchTerm.toUpperCase()) >=
      0
    );
  };

  render() {
    let searchResults = messages.filter(this.filterBySearchTerm);
    if (this.props.searchTerm.length >= 1) {
      return (
        <StyledList>
          {searchResults.map(item => (
            <SearchResult
              key={item.id}
              {...item}
              searchTerm={this.props.searchTerm}
            />
          ))}
        </StyledList>
      );
    } else {
      return (
        <StyledList>
          {chats.map(item => (
            <ChatListItem
              key={item.id}
              {...item}
              name={item.title}
              onClick={() => this.props.showChatScreen(item.id)}
            />
          ))}
        </StyledList>
      );
    }
  }
}

export default ChatList;