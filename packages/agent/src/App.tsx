import React from "react";
// import { createGlobalStyle } from "styled-components";
import Header<PERSON>av from "./HeaderNav";
import HeaderTop from "./HeaderTop";
import MainCarousel from "./MainCarousel";
import ChatScreen from "./ChatScreen";
import { Transition } from "react-spring";


type State = {
  viewState: string,
  chatScreenIsVisible: boolean,
  currentChatId: number,
  searchTerm: string,
  searchInputIsvisible: boolean,
  dropdownIsVisible: boolean
};

class App extends React.Component<{}, State> {
  state = {
    viewState: "2",
    chatScreenIsVisible: false,
    currentChatId: 0,
    searchTerm: "",
    searchInputIsvisible: false,
    dropdownIsVisible: false
  };

  showSearchInput = () => {
    this.setState({ searchInputIsvisible: true, viewState: "2" });
  };

  closeSearchInput = () => {
    this.setState({ searchInputIsvisible: false, searchTerm: "" });
  };

  handleSearchtermChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    this.setState({ searchTerm: event.target.value });
  };

  changeViewState = (event: React.MouseEvent<HTMLElement>) => {
    const newState = (event.currentTarget as HTMLElement).dataset.nav;
    this.setState({ viewState: newState || "2" });
  };

  showChatScreen = (id: number) => {
    this.setState({ chatScreenIsVisible: true, currentChatId: id });
  };

  closeChatScreen = () => {
    this.setState({ chatScreenIsVisible: false, currentChatId: 0 });
  };

  toggleDropdown = () => {
    this.setState(prevState => {
      return { dropdownIsVisible: !prevState.dropdownIsVisible };
    });
  };

  render() {
    return (
      <React.Fragment>
        <HeaderTop
          searchTerm={this.state.searchTerm}
          handleSearchtermChange={this.handleSearchtermChange}
          showSearchInput={this.showSearchInput}
          closeSearchInput={this.closeSearchInput}
          searchInputIsvisible={this.state.searchInputIsvisible}
          toggleDropdown={this.toggleDropdown}
          dropdownIsVisible={this.state.dropdownIsVisible}
        />
        <HeaderNav
          viewState={this.state.viewState}
          changeViewState={this.changeViewState}
        />
        <MainCarousel
          showChatScreen={this.showChatScreen}
          viewState={this.state.viewState}
          searchTerm={this.state.searchTerm}
        />
        <Transition
          items={this.state.chatScreenIsVisible}
          from={{ opacity: 0 }}
          enter={{ opacity: 1 }}
          leave={{ opacity: 0 }}
          config={{ duration: 200 }}
        >
          {show =>
            show &&
            (props => (
              <ChatScreen
                style={props}
                currentChatId={this.state.currentChatId}
                closeChatScreen={this.closeChatScreen}
              />
            ))
          }
        </Transition>
      </React.Fragment>
    );
  }
}

export default App;
