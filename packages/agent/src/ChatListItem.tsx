import React from 'react';
import styled from 'styled-components';

export interface ChatListItemProps {
  avatar?: string;
  name: string;
  message?: string;
  time?: string;
  unread?: boolean;
  isActive?: boolean;
  onClick?: () => void;
}

const ChatListItem: React.FC<ChatListItemProps> = ({
  avatar,
  name,
  message,
  time,
  unread = false,
  isActive = false,
  onClick
}) => {
  return (
    <Container isActive={isActive} onClick={onClick}>
      <AvatarContainer>
        {avatar ? (
          <Avatar src={avatar} alt={name} />
        ) : (
          <DefaultAvatar>{name.charAt(0)}</DefaultAvatar>
        )}
      </AvatarContainer>
      <Content>
        <TopRow>
          <Name>{name}</Name>
          {time && <Time>{time}</Time>}
        </TopRow>
        {message && (
          <MessageRow>
            <Message>{message}</Message>
            {unread && <UnreadIndicator />}
          </MessageRow>
        )}
      </Content>
    </Container>
  );
};

const Container = styled.div<{ isActive?: boolean }>`
  display: flex;
  padding: 12px 16px;
  align-items: center;
  cursor: pointer;
  background-color: ${props => props.isActive ? '#f0f0f0' : 'transparent'};
  
  &:hover {
    background-color: #f5f5f5;
  }
`;

const AvatarContainer = styled.div`
  margin-right: 12px;
`;

const Avatar = styled.img`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
`;

const DefaultAvatar = styled.div`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
`;

const Content = styled.div`
  flex: 1;
  min-width: 0;
`;

const TopRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
`;

const Name = styled.div`
  font-weight: 600;
  font-size: 16px;
`;

const Time = styled.div`
  font-size: 12px;
  color: #666;
`;

const MessageRow = styled.div`
  display: flex;
  align-items: center;
`;

const Message = styled.div`
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
`;

const UnreadIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #007bff;
  margin-left: 8px;
`;

export default ChatListItem;
