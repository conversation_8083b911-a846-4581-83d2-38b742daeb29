import { Component } from "react";
import styled from "styled-components";
import { messages } from "./data/messages";
import { chats } from "./data/chats";
import ChatScreenFooter from "./ChatScreenFooter";
import ChatMessage from "./ChatMessage";
import ChatScreenHeader from "./ChatScreenHeader";

const StyledChatScreen = styled.div`
  background-color: #ece5dd;
  position: absolute;
  top: 0;
  z-index: 1;
`;

const ChatContent = styled.div`
  padding-top: 60px;
  padding-bottom: 50px;
  padding-right: 5px;
  padding-left: 5px;

  overflow: hidden;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: flex-end;
`;

interface Message {
  id: number;
  content: string;
  status: string;
}

type State = {
  messages: Message[];
};

type Props = {
  closeChatScreen: () => void;
  currentChatId: number;
  style: {
    opacity: number;
  };
};

class ChatScreen extends Component<Props, State> {
  state = {
    messages: []
  };

  componentDidMount() {
    this.setState({ messages });
  }

  componentDidUpdate() {
    window.scrollTo(0, document.body.scrollHeight);
  }

  addmessage = (formvalue: string) => {
    const newMessages = [...this.state.messages];
    const message = {
      id: Date.now(),
      content: formvalue,
      status: "outgoing"
    };
    window.scrollTo(0, document.body.scrollHeight);
    this.setState({ messages: [...newMessages, message] });
  };

  render() {
    let msg;
    if (this.props.currentChatId) {
      msg = chats.find(item => item.id === this.props.currentChatId);
    } else {
      msg = chats.find(item => item.id === 64138);
    }
    return (
      <StyledChatScreen style={this.props.style}>
        <ChatScreenHeader
          msg={msg}
          closeChatScreen={this.props.closeChatScreen}
        />
        <ChatContent>
          {this.state.messages.map((item: Message) => (
            <ChatMessage
              key={item.id}
              content={item.content}
              status={item.status}
            />
          ))}
        </ChatContent>
        <ChatScreenFooter addMessage={this.addmessage} />
      </StyledChatScreen>
    );
  }
}

export default ChatScreen;
