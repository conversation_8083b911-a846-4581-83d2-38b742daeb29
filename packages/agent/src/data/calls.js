const calls = [
  {
    id: 10235,
    title: "<PERSON>",
    preview: "Nemo recusandae molestias qui recusandae enim in natus.",
    avatar:
      "https://s3.amazonaws.com/uifaces/faces/twitter/brunodesign1206/128.jpg",
    phrase1:
      "Use the haptic PNG transmitter, then you can override the wireless application!",
    phrase3: "compress granular optimize convergence"
  },
  {
    id: 64987,
    title: "<PERSON><PERSON><PERSON>",
    preview: "Quia fugiat sint inventore.",
    avatar: "https://s3.amazonaws.com/uifaces/faces/twitter/romanbulah/128.jpg",
    phrase1:
      "Use the neural HDD pixel, then you can generate the solid state circuit!",
    phrase3: "program real-time matrix functionalities"
  },
  {
    id: 43915,
    title: "<PERSON>",
    preview: "Sapiente et sit est modi ut debitis.",
    avatar: "https://s3.amazonaws.com/uifaces/faces/twitter/jm_denis/128.jpg",
    phrase1:
      "Try to copy the SMTP array, maybe it will back up the bluetooth alarm!",
    phrase3: "calculate open-source grow experiences"
  }
];

export { calls };
