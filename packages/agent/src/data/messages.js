const messages = [
  {
    id: 23739,
    content:
      "navigating the transmitter won't do anything, we need to input the neural SSL monitor!",
    status: "outgoing"
  },
  {
    id: 60930,
    content:
      "Try to back up the IB circuit, maybe it will copy the bluetooth port!",
    status: "incoming"
  },
  {
    id: 14630,
    content: "We need to index the cross-platform TCP bandwidth!",
    status: "outgoing"
  },
  {
    id: 30474,
    content:
      "back uptransmitting the capacitor won't do anything, we need to transmit the back-end SQL protocol!",
    status: "incoming"
  },
  {
    id: 2673,
    content:
      "I'll input the bluetooth CSS monitor, that should firewall the SMS microchip!",
    status: "outgoing"
  },
  {
    id: 4534,
    content:
      "generating the sensor won't do anything, we need to program the neural USB transmitter!",
    status: "incoming"
  },
  {
    id: 9920,
    content:
      "You can't reboot the application without parsing the haptic SQL sensor!",
    status: "outgoing"
  },
  {
    id: 3521,
    content:
      "overrideI'll input the 1080p SMS feed, that should bandwidth the TCP protocol!",
    status: "incoming"
  },
  {
    id: 87660,
    content:
      "Use the bluetooth TCP application, then you can navigate the digital sensor!",
    status: "outgoing"
  },
  {
    id: 20319,
    content:
      "I'll compress the digital RAM card, that should bus the SSL program!",
    status: "incoming"
  },
  {
    id: 167,
    content:
      "I'll transmit the haptic ADP transmitter🤖 💩, that should firewall the USB hard drive!",
    status: "outgoing"
  },
  {
    id: 25454,
    content: "We need to input the open-source HTTP driver!",
    status: "incoming"
  },
  {
    id: 350,
    content: "Use the neural🔥, SMS pixel, then you can copy the haptic feed!",
    status: "outgoing"
  },
  {
    id: 2250,
    content:
      "Try to calculate the FTP driver😉, maybe it will synthesize the open-source capacitor!",
    status: "incoming"
  },
  {
    id: 53854,
    content: "try programming cutting-edge brand relationship👌 ",
    status: "outgoing"
  },
  {
    id: 61809,
    content:
      "Use the 1080p SSL panel, then you can generate the optical alarm!",
    status: "incoming"
  },
  {
    id: 66826,
    content:
      "generating the matrix won't do anything, we need to back up the optical SQL array!",
    status: "outgoing"
  },
  {
    id: 51630,
    content:
      "I'll connect the 1080p AI capacitor, that should microchip the RAM panel!🔥🔥",
    status: "incoming"
  },
  {
    id: 92113,
    content: "programming dot-com implement applications😎",
    status: "outgoing"
  },

  {
    id: 63181,
    content:
      "If we connect the program, we can get to the THX sensor through the bluetooth COM bus!",
    status: "incoming"
  }
];

export { messages };

// 😀 😁 😂 🤣 😃 😄 😅 😆 😉 😊 😋 😎 😍 😘
