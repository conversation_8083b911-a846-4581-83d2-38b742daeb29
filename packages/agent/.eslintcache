[{"/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/index.js": "1", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/App.js": "2", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/HeaderNav.js": "3", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/HeaderTop.js": "4", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreen.js": "5", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/MainCarousel.js": "6", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreenHeader.js": "7", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/messages.js": "8", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/chats.js": "9", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/Dropdown.js": "10", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreenFooter.js": "11", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/CallsList.js": "12", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatMessage.js": "13", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/StatusList.js": "14", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatList.js": "15", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/CallsListItem.js": "16", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatListItem.js": "17", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/calls.js": "18", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/SearchResult.js": "19", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/StatusListItem.js": "20", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/statusItems.js": "21", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/helpers.js": "22", "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/Highlight.js": "23"}, {"size": 146, "mtime": 1612292147908, "results": "24", "hashOfConfig": "25"}, {"size": 3076, "mtime": 1612360529704, "results": "26", "hashOfConfig": "25"}, {"size": 2431, "mtime": 1612292147905, "results": "27", "hashOfConfig": "25"}, {"size": 2758, "mtime": 1612292147905, "results": "28", "hashOfConfig": "25"}, {"size": 2097, "mtime": 1612292147905, "results": "29", "hashOfConfig": "25"}, {"size": 1720, "mtime": 1612292147905, "results": "30", "hashOfConfig": "25"}, {"size": 2179, "mtime": 1612292147905, "results": "31", "hashOfConfig": "25"}, {"size": 2941, "mtime": 1612292147906, "results": "32", "hashOfConfig": "25"}, {"size": 1969, "mtime": 1612292147906, "results": "33", "hashOfConfig": "25"}, {"size": 1256, "mtime": 1612292147905, "results": "34", "hashOfConfig": "25"}, {"size": 3234, "mtime": 1612292147905, "results": "35", "hashOfConfig": "25"}, {"size": 416, "mtime": 1612292147904, "results": "36", "hashOfConfig": "25"}, {"size": 1573, "mtime": 1612292147904, "results": "37", "hashOfConfig": "25"}, {"size": 311, "mtime": 1612292147906, "results": "38", "hashOfConfig": "25"}, {"size": 1292, "mtime": 1612292147904, "results": "39", "hashOfConfig": "25"}, {"size": 1637, "mtime": 1612292147904, "results": "40", "hashOfConfig": "25"}, {"size": 4052, "mtime": 1612292147904, "results": "41", "hashOfConfig": "25"}, {"size": 1066, "mtime": 1612292147906, "results": "42", "hashOfConfig": "25"}, {"size": 320, "mtime": 1612292147906, "results": "43", "hashOfConfig": "25"}, {"size": 1901, "mtime": 1612292147906, "results": "44", "hashOfConfig": "25"}, {"size": 376, "mtime": 1612292147906, "results": "45", "hashOfConfig": "25"}, {"size": 130, "mtime": 1612292147906, "results": "46", "hashOfConfig": "25"}, {"size": 771, "mtime": 1612292147905, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1regbau", {"filePath": "50", "messages": "51", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "errorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "64"}, {"filePath": "65", "messages": "66", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "64"}, {"filePath": "85", "messages": "86", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "64"}, {"filePath": "91", "messages": "92", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/index.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/App.js", ["95"], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/HeaderNav.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/HeaderTop.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreen.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/MainCarousel.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreenHeader.js", ["96", "97", "98", "99"], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/messages.js", [], ["100", "101"], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/chats.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/Dropdown.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatScreenFooter.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/CallsList.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatMessage.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/StatusList.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatList.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/CallsListItem.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/ChatListItem.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/calls.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/SearchResult.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/StatusListItem.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/data/statusItems.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/helpers.js", [], "/Applications/MAMP/htdocs/sparkstrand-voltage/agent/src/Highlight.js", [], {"ruleId": "102", "severity": 1, "message": "103", "line": 4, "column": 10, "nodeType": "104", "messageId": "105", "endLine": 4, "endColumn": 27}, {"ruleId": "106", "severity": 1, "message": "107", "line": 83, "column": 9, "nodeType": "108", "endLine": 83, "endColumn": 38}, {"ruleId": "106", "severity": 1, "message": "107", "line": 86, "column": 9, "nodeType": "108", "endLine": 86, "endColumn": 12}, {"ruleId": "106", "severity": 1, "message": "107", "line": 92, "column": 9, "nodeType": "108", "endLine": 92, "endColumn": 12}, {"ruleId": "106", "severity": 1, "message": "107", "line": 95, "column": 9, "nodeType": "108", "endLine": 95, "endColumn": 12}, {"ruleId": "109", "replacedBy": "110"}, {"ruleId": "111", "replacedBy": "112"}, "no-unused-vars", "'createGlobalStyle' is defined but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/evcohen/eslint-plugin-jsx-a11y/blob/master/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-native-reassign", ["113"], "no-negated-in-lhs", ["114"], "no-global-assign", "no-unsafe-negation"]