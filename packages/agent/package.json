{"name": "@sparkstrand/agent", "version": "0.0.1", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "scripts": {"dev": "vite", "build": "tsup", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "repository": {"type": "git", "url": "git+https://github.com/sparkstrand/chat-application.git", "directory": "packages/agent"}, "dependencies": {"react-spring": "^7.2.9", "styled-components": "^5.2.1"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/styled-components": "^5.1.26", "@vitejs/plugin-react": "^4.3.4", "serve": "^14.2.4", "tsup": "^7.2.0", "typescript": "^5.3.3", "vite": "^4.3.0", "vite-plugin-svgr": "^4.3.0"}}