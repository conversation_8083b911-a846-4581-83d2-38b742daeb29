{"include": ["src", "src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "compilerOptions": {"module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "target": "es2017", "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "jsx": "react-jsx", "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "typeRoots": ["./node_modules/@types", "./src/types"]}}