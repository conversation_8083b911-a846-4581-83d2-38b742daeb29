{"include": ["src", "types"], "compilerOptions": {"module": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "target": "es2017", "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"*": ["src/*", "node_modules/*"]}, "jsx": "react-jsx", "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}}