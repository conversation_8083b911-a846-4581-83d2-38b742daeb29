{"name": "@sparkstrand/chat", "version": "0.0.9", "author": "Spark Strand", "module": "dist/index.mjs", "description": "React Chat components, driven by hooks.", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/sparkstrand/chat-application.git", "directory": "packages/chat"}, "bugs": {"url": "https://github.com/sparkstrand/chat-application/issues"}, "files": ["dist"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs", "browser": "./dist/index.mjs"}, "./theme.css": "./theme.css", "./styles.css": "./dist/styles.css"}, "scripts": {"dev": "concurrently \"yarn buildTailwind --watch\"  \"tsup src/index.tsx --format esm,cjs --sourcemap --watch --dts --external react --external next --external next/Image\"", "build": "tsup", "test": "jest test", "typecheck": "tsc --noEmit", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint 'src/**/*.{ts,tsx}'", "lint:fix": "eslint --fix", "bundlesize": "pnpm build && size-limit", "start": "react-scripts start", "deploy": "npm run build && firebase deploy", "buildTailwind": "tailwindcss -i ./styles.css -o ./dist/styles.css"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "size-limit": [{"path": "./dist/index.js", "limit": "3 kB"}, {"path": "./dist/index.mjs", "limit": "3 kB"}], "publishConfig": {"registry": "https://npm.pkg.github.com"}, "devDependencies": {"@size-limit/preset-small-lib": "^7.0.3", "@testing-library/react": "^14.0.0", "@types/bcryptjs": "^2.4.2", "@types/body-parser": "^1.19.0", "@types/cookie-parser": "^1.4.2", "@types/express": "^4.17.9", "@types/hapi__joi": "^17.1.6", "@types/jest": "^27.5.2", "@types/jsonwebtoken": "^8.5.0", "@types/mongoose": "^5.10.3", "@types/multer": "^1.4.5", "@types/node": "^18.11.19", "@types/passport": "^1.0.4", "@types/passport-jwt": "^3.0.3", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-mic": "^12", "@types/socket.io": "^2.1.12", "concurrently": "^9.1.2", "cpy": "^11.1.0", "esbuild": "^0.25.2", "esbuild-css-modules-plugin": "^3.1.4", "eslint": "^6.2.2", "eslint-config-airbnb": "^18.0.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.14.3", "eslint-plugin-react-hooks": "^1.7.0", "eslint-plugin-simple-import-sort": "^10.0.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-test-renderer": "^18.2.0", "size-limit": "^7.0.3", "source-map": "0.6.1", "ts-jest": "^29.0.5", "tslib": "^2.0.3", "tsup": "^6.6.3", "typescript": "^4.9.4"}, "dependencies": {"@babel/core": "7.12.3", "@chakra-ui/icons": "^1.0.2", "@chakra-ui/react": "1.1.2", "@emotion/babel-plugin": "^11.1.2", "@emotion/core": "^11.0.0", "@emotion/react": "^11.1.4", "@emotion/styled": "^11.0.0", "@hapi/joi": "^17.1.1", "@material-ui/core": "^4.11.2", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.57", "@pmmmwh/react-refresh-webpack-plugin": "0.4.2", "@svgr/webpack": "5.4.0", "@tailwindcss/cli": "^4.0.17", "@tailwindcss/postcss": "^4.0.17", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/compression": "^1.7.0", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^16.9.53", "@types/react-dom": "^16.9.8", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "autoprefixer": "^9", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.0", "babel-loader": "8.1.0", "babel-plugin-named-asset-import": "^0.3.7", "babel-preset-react-app": "^10.0.0", "bcryptjs": "^2.4.3", "bfj": "^7.0.2", "camelcase": "^6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "classnames": "^2.2.6", "cloudinary": "^1.23.0", "compression": "^1.7.4", "connected-react-router": "^6.8.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "css-loader": "4.3.0", "datauri": "^3.0.0", "date-fns": "^2.16.1", "dateformat": "^4.4.1", "dotenv": "^8.2.0", "dotenv-expand": "5.1.0", "emoji-mart": "^3.0.0", "emoji-picker-react": "^4.12.2", "eslint": "^7.11.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "eslint-webpack-plugin": "^2.1.0", "file-loader": "6.1.1", "firebase": "^7.18.0", "framer-motion": "^3.1.1", "fs-extra": "^9.0.1", "html-webpack-plugin": "4.5.0", "identity-obj-proxy": "3.0.0", "jest": "26.6.0", "jest-circus": "26.6.0", "jest-resolve": "26.6.0", "jest-watch-typeahead": "0.6.1", "joi-image-extension": "^1.0.0", "jsonwebtoken": "^8.5.1", "lightningcss": "^1.29.3", "lucide": "^0.485.0", "lucide-react": "^0.485.0", "mini-css-extract-plugin": "0.11.3", "mongoose": "^5.10.18", "multer": "^1.4.2", "optimize-css-assets-webpack-plugin": "5.0.4", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "pnp-webpack-plugin": "1.6.4", "postcss": "^8.5.3", "postcss-flexbugs-fixes": "4.2.1", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "5.0.2", "prompts": "2.4.0", "query-string": "^6.8.2", "react": "^19.0.1", "react-app-polyfill": "^2.0.0", "react-bootstrap": "^1.3.0", "react-dev-utils": "^11.0.1", "react-dom": "^19.0.1", "react-easy-crop": "^3.3.1", "react-emoji": "^0.5.0", "react-flip-move": "^3.0.4", "react-github-corner": "^2.5.0", "react-icons": "^5.5.0", "react-mic": "^12.4.6", "react-redux": "^7.2.2", "react-refresh": "^0.8.3", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-scroll-to-bottom": "^4.1.0", "react-switch": "^5.0.1", "redux": "^4.0.5", "redux-saga": "^1.1.3", "reselect": "^4.0.0", "resolve": "1.18.1", "resolve-url-loader": "^3.1.2", "sass-loader": "8.0.2", "semver": "7.3.2", "serve": "^11.3.2", "socket.io": "^3.0.4", "socket.io-client": "^3.0.4", "socketio-jwt": "^4.6.2", "style-loader": "1.3.0", "tailwindcss": "^4.1.1", "terser-webpack-plugin": "4.2.3", "ts-node": "^9.1.1", "ts-pnp": "1.2.0", "typescript": "^4.0.3", "url-loader": "4.1.1", "web-vitals": "^0.2.4", "webpack": "4.44.2", "webpack-dev-server": "3.11.0", "webpack-manifest-plugin": "2.2.0", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3", "workbox-webpack-plugin": "5.1.4"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=14.15.1 <=22.x"}, "proxy": "http://localhost:5000/"}