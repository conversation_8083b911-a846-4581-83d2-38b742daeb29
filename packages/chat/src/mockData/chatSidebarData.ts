export interface DocumentType {
  id: string;
  url: string;
  name: string;
  fileType: "pdf" | "doc" | "docx" | "xls" | "xlsx" | "ppt" | "pptx";
}

export interface ImageType {
  src: string;
  alt?: string;
}

export interface MemberType {
  id: string;
  name: string;
  status: "online" | "offline";
  role?: "moderator" | "member";
  avatar?: string;
}

type RoomInfo = {
  title: string;
  description: string;
  attendeesCount: number;
  roomImage: string;
};

export interface ChatSidebarData {
  roomInfo: RoomInfo;
  notificationsEnabled: boolean;
  media: {
    images: ImageType[];
    documents: DocumentType[];
  };
  members: MemberType[];
}

export const chatSidebarData: ChatSidebarData = {
  roomInfo: {
    title: "VolleyBall Friends",
    description:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem asperiores sequi hic dignissimos rerum fugiat mollitia eum ea tempora beatae.",
    attendeesCount: 26,
    roomImage:
      "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
  },
  notificationsEnabled: false,
  media: {
    images: [
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.sportyexpats.fr/_next/image?url=%2Fimages%2Fabout.png&w=1920&q=75",
      },
    ],
    documents: [
      
      {
        id: "3",
        url: "https://propaddy2.s3.eu-north-1.amazonaws.com/uploads/BulkPropertySheet.xlsx",
        name: "Financial Statements.xlsx",
        fileType: "xlsx",
      },
    ],
  },
  members: [
    {
      id: "1",
      name: "Levi Rcgh",
      status: "online",
      role: "moderator",
    },
    ...Array(6).fill({
      id: "2",
      name: "Member Name",
      status: "online",
    }),
  ],
};
