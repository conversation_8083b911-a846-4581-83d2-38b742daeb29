import { ImageLink, AttendeesLink } from "./ImageLInk";

export type EventDataType = {
  id: number;
  date: string;
  title: string;
  image: string;
  attendees: string[];
  attendeeCount: number;
  attendeesText:string;
  viewEventText:string;
};

export const EventData: EventDataType[] = [
  {
    id: 1,
    date: "MON, SEP 9, 2024, 9:00 AM CEST",
    title: "FOOTBALL FRIENDLY",
    image: ImageL<PERSON>,
    attendees: [ImageL<PERSON>, AttendeesLink, ImageLink, AttendeesLink],
    attendeeCount: 5,
    attendeesText: "Attendees",
    viewEventText:"View Event Room"
  },
  {
    id: 2,
    date: "FRI, OCT 12, 2024, 6:00 PM CEST",
    title: "BASKETBALL MATCH",
    image: AttendeesLink,
    attendees: [Attend<PERSON><PERSON><PERSON>, ImageLink, AttendeesLink, ImageLink],
    attendeeCount: 8,
    attendeesText: "Attendees",
    viewEventText:"View Event Room"
  },
  {
    id: 3,
    date: "WED, NOV 20, 2024, 3:00 PM CEST",
    title: "TENNIS CHAMPIONSHIP",
    image: ImageLink,
    attendees: [ImageL<PERSON>, AttendeesLink, AttendeesLink],
    attendeeCount: 3,
    attendeesText: "Attendees",
    viewEventText:"View Event Room"
  },
];
