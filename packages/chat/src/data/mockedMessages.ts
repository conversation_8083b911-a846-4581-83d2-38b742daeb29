    
type Message = {
    id: number;
        sender: string;
        message: string;
        timestamp: string;
        avatar: string;
      };
      
    

export const messages: Message[] = [
    {
      id: 1,
      sender: "<PERSON><PERSON><PERSON>",
      message: "Hey everyone! I am excited about getting up for the day and heading to the beach for a friendly match of Volleyball! It will be a lot of fun.",
      timestamp: "12:24pm",
      avatar: "https://picsum.photos/id/237/200/300",
    },
    {
      id: 2,
      sender: "James Belt",
      message: "Hey everyone! I am excited about getting up for the day and heading to the beach for a friendly match of Volleyball! It will be a lot of fun.",
      timestamp: "12:28pm",
      avatar: "https://picsum.photos/id/1/200/300",
    },
    {
      id: 3,
      sender: "<PERSON>",
      message: "Hey everyone! I am excited about getting up for the day and heading to the beach for a friendly match of Volleyball! It will be a lot of fun.",
      timestamp: "12:28pm",
      avatar: "https://picsum.photos/id/10/200/300",
    },
    {
      id: 4,
      sender: "Fontaine Wat",
      message: "Hey everyone! I am excited about getting up for the day and heading to the beach for a friendly match of Volleyball! It will be a lot of fun.",
      timestamp: "12:38pm",
      avatar: "https://picsum.photos/id/12/200/300",
    },
    {
      id: 5,
      sender: "You",
      message: "Hey everyone! I am excited about getting up for the day and heading to the beach for a friendly match of Volleyball! It will be a lot of fun.",
      timestamp: "12:54pm",
      avatar: "https://picsum.photos/id/31/200/300",
    },
  ];