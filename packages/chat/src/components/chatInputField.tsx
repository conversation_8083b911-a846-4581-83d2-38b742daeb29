"use client";

import { FC, useState, useRef, ChangeEvent } from "react";
import {
  FaMicrophone,
  FaPaperclip,
  FaRegSmile,
  FaPaperPlane,
} from "react-icons/fa";
import EmojiPicker from "emoji-picker-react";
import { ReactMic } from "react-mic";
import { EmojiClickData } from "emoji-picker-react";

interface ChatInputFieldProps {
  sendButtonText?: string;
  placeholderText?: string;
  emojiButtonText?: string;
  pauseButtonText?: string;
  resumeButtonText?: string;
}

const ChatInputField: FC<ChatInputFieldProps> = ({
  sendButtonText = "Send",
  placeholderText = "Type Message...",
  pauseButtonText = "Pause",
  resumeButtonText = "Resume",
}) => {
  const [message, setMessage] = useState("");
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [recording, setRecording] = useState(false);
  const [audioURL, setAudioURL] = useState("");
  const micRef = useRef(null);
  // Using underscore prefix to indicate intentionally unused variable
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_isPaused, setIsPaused] = useState(false);

  const handleEmojiClick = (emojiObject: EmojiClickData): void => {
    setMessage((prev) => prev + emojiObject.emoji);
    setShowEmojiPicker(false);
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>): void => {
    const file = event.target.files?.[0];
    if (file) {
      console.log("File selected:", file);
    }
  };

  const handleSendMessage = (): void => {
    if (message.trim()) {
      console.log("Message sent:", message);
      setMessage("");
    }
    if (audioURL) {
      console.log("Audio message sent:", audioURL);
      setAudioURL("");
    }
  };

  const startRecording = (): void => {
    setRecording(true);
    setIsPaused(false);
  };

  const stopRecording = (): void => {
    setRecording(false);
    setIsPaused(false);
  };

  const pauseRecording = (): void => {
    setRecording(false);
    setIsPaused(true);
  };

  const resumeRecording = (): void => {
    setRecording(true);
    setIsPaused(false);
  };

  const onStop = (recordedData: {
    blob: Blob;
    startTime: number;
    stopTime: number;
    blobURL: string;
  }): void => {
    setAudioURL(URL.createObjectURL(recordedData.blob));
    console.log("Recorded Data: ", recordedData);
  };

  return (
    <div className="flex items-center bg-gray-800 rounded-lg p-2">
      <div className="relative">
        <FaMicrophone
          className={`text-white mr-2 cursor-pointer ${recording ? "text-red-500" : ""}`}
          onMouseDown={startRecording}
          onMouseUp={stopRecording}
        />
        <ReactMic
          ref={micRef}
          record={recording}
          className="absolute"
          onStop={(blobObject) => onStop({...blobObject, blobURL: URL.createObjectURL(blobObject.blob)})}
          strokeColor="#fff"
          backgroundColor="transparent"
        />
      </div>
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder={placeholderText}
        className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none"
        onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
      />
      <div className="flex items-center space-x-2 ml-2">
        <FaRegSmile
          className="text-white cursor-pointer"
          onClick={() => setShowEmojiPicker((prev) => !prev)}
        />
        <input
          type="file"
          className="hidden"
          id="file-input"
          onChange={handleFileChange}
        />
        <label htmlFor="file-input">
          <FaPaperclip className="text-white cursor-pointer" />
        </label>
        <button
          className="flex items-center justify-center bg-gray-700 text-white rounded px-3 py-1"
          onClick={handleSendMessage}
        >
          {sendButtonText}
          <FaPaperPlane className="ml-1" />
        </button>
        {recording && (
          <div className="flex space-x-2">
            <button className="text-white" onClick={pauseRecording}>
              {pauseButtonText}
            </button>
            <button className="text-white" onClick={resumeRecording}>
              {resumeButtonText}
            </button>
          </div>
        )}
      </div>
      {showEmojiPicker && (
        <div className="absolute z-10">
          <EmojiPicker
            onEmojiClick={(emojiData) => handleEmojiClick(emojiData)}
          />
        </div>
      )}
    </div>
  );
};

export default ChatInputField;
