import { FC } from "react";

const colorClasses = ["text-red-500", "text-purple-500", "text-green-500", "text-blue-500", "text-yellow-500", "text-pink-500"];

type Message = {
  id: number;
  sender: string;
  message: string;
  timestamp: string;
  avatar: string;
};

type ChatDisplayProps = {
  messages: Message[];
  loggedInUser?: string;
};

const ChatDisplay: FC<ChatDisplayProps> = ({ messages, loggedInUser = "You" }) => {
  return (
    <div className="flex flex-col space-y-4 p-4 ">
      {messages.map(({ id, sender, message, timestamp, avatar }, index) => {
        const isUser = sender === loggedInUser;
        const senderColor = colorClasses[index % colorClasses.length];
        return (
          <div key={id} className={`flex flex-col ${isUser ? "items-end" : "items-start"} space-y-1`}>            
            <div className="flex items-end space-x-2">
              {!isUser && (
                <img src={avatar} alt={sender} className="w-10 h-10 rounded-full self-end" />
              )}
              <div className={`relative p-4 rounded-2xl shadow-md max-w-lg ${isUser ? "bg-green-500 text-white" : "bg-gray-100 text-black"}`}>
                <p className={`font-bold text-sm mb-1 ${isUser ? "text-white" : senderColor}`}>{isUser ? "You" : sender}</p>
                <p className="leading-tight">{message}</p>
              </div>
              {isUser && (
                <img src={avatar} alt="You" className="w-10 h-10 rounded-full self-end" />
              )}
            </div>
            <p className={`text-xs text-gray-500 ${isUser ? "pr-12" : "pl-12"}`}>{timestamp}</p>
          </div>
        );
      })}
    </div>
  );
};

export default ChatDisplay;