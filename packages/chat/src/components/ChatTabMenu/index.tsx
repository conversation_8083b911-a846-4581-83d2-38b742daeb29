import { FC, useState } from "react";

interface ChatTabMenuProps {
  pastLabel?: string;
  upcomingLabel?: string;
  joinedLabel?: string;
}

const ChatTabMenu: FC<ChatTabMenuProps> = ({
  pastLabel = "Past Events",
  upcomingLabel = "Upcoming Events",
  joinedLabel = "Joined Events",
}) => {
  const [activeTab, setActiveTab] = useState("past");

  const handleTabClick = (tab: string): void => {
    setActiveTab(tab);
  };

  return (
    <div className="flex justify-center space-x-4 p-4 bg-gray-100 rounded-lg">
      <button
        className={`px-16 py-3 rounded-lg transition-colors duration-300 font-medium ${
          activeTab === "joined" ? "bg-cyan-200" : "bg-transparent"
        }`}
        onClick={() => handleTabClick("past")}
      >
        {pastLabel}
      </button>
      <button
        className={`px-16 py-3 rounded-lg transition-colors duration-300 font-medium ${
          activeTab === "upcoming" ? "bg-cyan-200" : "bg-transparent"
        }`}
        onClick={() => handleTabClick("upcoming")}
      >
        {upcomingLabel}
      </button>
      <button
        className={`px-16 py-3 rounded-lg transition-colors duration-300 font-medium ${
          activeTab === "joined" ? "bg-cyan-200" : "bg-transparent"
        }`}
        onClick={() => handleTabClick("joined")}
      >
        {joinedLabel}
      </button>
    </div>
  );
};

export default ChatTabMenu;
