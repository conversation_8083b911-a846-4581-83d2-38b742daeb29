import { Dispatch, SetStateAction } from "react";

export interface ImageType {
  src: string;
  alt?: string;
}

export interface ImageSliderModalProps {
  images: ImageType[];
  isOpen: boolean;
  onClose: () => void;
  selectedIndex: number;
  setSelectedIndex: Dispatch<SetStateAction<number>>;
}

export interface DocumentType {
  id: string;
  url: string;
  name: string;
  fileType: "pdf" | "doc" | "docx" | "xls" | "xlsx" | "ppt" | "pptx";
}

export interface DocumentSliderModalProps {
  documents: DocumentType[];
  isOpen: boolean;
  onClose: () => void;
  selectedIndex: number;
  setSelectedIndex: Dispatch<SetStateAction<number>>;
}