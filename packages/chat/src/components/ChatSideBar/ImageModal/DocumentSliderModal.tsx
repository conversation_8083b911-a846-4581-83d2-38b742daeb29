import { ReactElement, useEffect } from "react";
import { DocumentSliderModalProps } from "./types";
import { getFileIcon } from "../../../utils/getFilIcon";

const DocumentSliderModal = ({
  documents,
  isOpen,
  onClose,
  selectedIndex,
  setSelectedIndex,
}: DocumentSliderModalProps): ReactElement | null => {
  const goToPrevious = (): void => {
    setSelectedIndex((prev) => (prev === 0 ? documents.length - 1 : prev - 1));
  };

  const goToNext = (): void => {
    setSelectedIndex((prev) => (prev === documents.length - 1 ? 0 : prev + 1));
  };

  const goToDocument = (index: number): void => {
    setSelectedIndex(index);
  };

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent): void => {
      switch (e.key) {
        case "Escape":
          onClose();
          break;
        case "ArrowLeft":
          goToPrevious();
          break;
        case "ArrowRight":
          goToNext();
          break;
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "auto";
    };
  }, [isOpen, selectedIndex, onClose]);

  if (!isOpen) return null;

  const currentDocument = documents[selectedIndex];

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-90">
      <button
        onClick={onClose}
        className="absolute top-5 right-5 text-white text-3xl px-3 py-1 hover:opacity-70"
        aria-label="Close modal"
      >
        &times;
      </button>

      <div className="relative w-[90%] max-w-[900px] h-[85vh] flex flex-col">
        <button
          onClick={goToPrevious}
          className="absolute top-1/2 -translate-y-1/2 left-5 w-12 h-12 rounded-full bg-black bg-opacity-50 text-white text-lg flex items-center justify-center z-10 hover:bg-opacity-80 disabled:opacity-30"
          aria-label="Previous document"
          disabled={documents.length <= 1}
        >
          &larr;
        </button>

        <div className="flex-1 flex flex-col bg-white rounded-lg overflow-hidden">
          <div className="flex-1 flex flex-col items-center justify-center p-5 bg-gray-100">
            <div className="text-[60px] mb-5 text-center">
              {getFileIcon(currentDocument.fileType)}
              <span className="block text-base font-bold text-gray-800 mt-2">
                {currentDocument.fileType.toUpperCase()}
              </span>
            </div>
            <iframe
              src={`https://docs.google.com/viewer?url=${encodeURIComponent(
                currentDocument.url
              )}&embedded=true`}
              title={currentDocument.name}
              className="w-full h-[70%] border-none bg-white shadow-md"
            />
          </div>

          <div className="flex justify-between items-center px-5 py-4 bg-[#4e5152] text-white">
            <a
              href={currentDocument.url}
              download
              className="px-4 py-2 bg-white text-[#4e5152] font-bold rounded hover:bg-gray-100"
            >
              Download
            </a>
            <span className="text-right text-sm truncate max-w-[60%]">
              {currentDocument.name}
            </span>
          </div>
        </div>

        <button
          onClick={goToNext}
          className="absolute top-1/2 -translate-y-1/2 right-5 w-12 h-12 rounded-full bg-black bg-opacity-50 text-white text-lg flex items-center justify-center z-10 hover:bg-opacity-80 disabled:opacity-30"
          aria-label="Next document"
          disabled={documents.length <= 1}
        >
          &rarr;
        </button>

        {documents.length > 1 && (
          <div className="flex gap-2 py-4 overflow-x-auto justify-center scrollbar-thin">
            {documents.map((doc, index) => (
              <button
                key={doc.id}
                onClick={() => goToDocument(index)}
                className={`w-14 h-14 flex flex-col items-center justify-center rounded border-2 ${
                  selectedIndex === index
                    ? "border-blue-500 bg-blue-500"
                    : "border-transparent bg-[#333]"
                } text-white flex-shrink-0`}
                aria-label={`View ${doc.name}`}
              >
                <span className="text-xl">{getFileIcon(doc.fileType)}</span>
                <span className="text-[10px] mt-1 uppercase">
                  {doc.fileType}
                </span>
              </button>
            ))}
          </div>
        )}

        <div className="text-white text-center text-sm opacity-80 mt-2">
          {selectedIndex + 1} / {documents.length}
        </div>
      </div>
    </div>
  );
};

export default DocumentSliderModal;
