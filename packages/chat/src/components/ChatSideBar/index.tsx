"use client";

import { ReactElement, useState } from "react";


import { MdOutlineCancel } from "react-icons/md";
import { Fa<PERSON>ileAlt, FaRegBell, FaUsers } from "react-icons/fa";
import ImageSliderModal from "./ImageModal/ImageSliderModal";
import DocumentSliderModal from "./ImageModal/DocumentSliderModal";

import {
  MemberType,
  ImageType,
  DocumentType,
  ChatSidebarData,
} from "../../mockData/chatSidebarData";
import { getFileIcon } from "../../utils/getFilIcon";

interface ChatSideBarProps {
  data: ChatSidebarData;
  content: {
    eventInfo: string;
    room: string;
    attendies: string;
    description: string;
    notifications: string;
    medias: string;
    files: string;
    member: string;
    userType: string;
  };
}

const ChatSideBar = ({ data, content }: ChatSideBarProps): ReactElement => {
  const [switchOn, setSwitchOn] = useState(data?.notificationsEnabled);
  const [mediaChoice, setMediaChoice] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDocModalOpen, setIsDocModalOpen] = useState<boolean>(false);
  const [selectedIndex, setSelectedIndex] = useState<number>(0);

  const { roomInfo, media, members } = data;
  const { images, documents } = media;
  const {
    attendies,
    description,
    eventInfo,
    files,
    medias,
    member,
    notifications,
    room,
    userType,
  } = content;

  const toggleSwitch = () => {
    setSwitchOn(!switchOn);
  };

  return (
    <div className="w-full max-w-[380px] h-screen overflow-y-auto bg-white shadow-lg">
      <ImageSliderModal
        images={images}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
      />
      <DocumentSliderModal
        documents={documents}
        isOpen={isDocModalOpen}
        onClose={() => setIsDocModalOpen(false)}
        selectedIndex={selectedIndex}
        setSelectedIndex={setSelectedIndex}
      />

      {/* Header */}
      <div className="w-full p-5 flex justify-between items-center border-b border-gray-500">
        <h2 className="text-xl font-bold text-black">{eventInfo}</h2>
        <MdOutlineCancel size={24} className="cursor-pointer text-black" />
      </div>

      {/* Info */}
      <div className="w-full p-5 flex flex-col items-center gap-12">
        {/* Room Info */}
        <div className="flex flex-col gap-3 items-center w-full">
          <div className="w-[100px] h-[100px]">
            <img
              src={roomInfo.roomImage}
              alt="Room"
              className="w-full h-full object-cover rounded-md"
            />
          </div>
          <h4 className="text-base font-bold text-black">{roomInfo.title}</h4>
          <p className="text-xs text-gray-400">
            {room} • {roomInfo.attendeesCount} {attendies}
          </p>
        </div>

        {/* Description */}
        <div className="w-full flex flex-col gap-2 items-start py-3">
          <div className="flex gap-2 items-center">
            <FaFileAlt size={16} className="text-gray-400" />
            <h4 className="text-sm font-semibold text-gray-400">
              {description}
            </h4>
          </div>
          <p className="text-xs text-black">{roomInfo.description}</p>
        </div>

        {/* Notifications */}
        <div className="w-full flex justify-between items-center">
          <div className="flex gap-2 items-center">
            <FaRegBell size={16} className="text-gray-400" />
            <h4 className="text-sm font-semibold text-gray-400">
              {notifications}
            </h4>
          </div>
          <div
            onClick={toggleSwitch}
            className={`w-10 h-5 rounded-full px-1 cursor-pointer flex items-center ${
              switchOn
                ? "bg-green-500 justify-end"
                : "bg-gray-400 justify-start"
            }`}
          >
            <div className="w-[14px] h-[14px] bg-gray-100 rounded-full" />
          </div>
        </div>

        {/* Media */}
        <div className="w-full">
          <div className="w-full h-12 rounded-md bg-gray-300 flex mb-5">
            <div
              onClick={() => setMediaChoice(1)}
              className={`w-1/2 h-full cursor-pointer flex gap-5 px-3 items-center rounded-md ${
                mediaChoice === 1 ? "bg-gray-500" : ""
              }`}
            >
              <h4 className="text-sm font-semibold text-black m-0">{medias}</h4>
              <div className="w-5 h-5 bg-white rounded-full text-xs flex items-center justify-center text-black">
                {images.length}
              </div>
            </div>
            <div
              onClick={() => setMediaChoice(2)}
              className={`w-1/2 h-full cursor-pointer flex gap-5 px-3 items-center rounded-md ${
                mediaChoice === 2 ? "bg-gray-500" : ""
              }`}
            >
              <h4 className="text-sm font-semibold text-black m-0">{files}</h4>
              <div className="w-5 h-5 bg-white rounded-full text-xs flex items-center justify-center text-black">
                {documents.length}
              </div>
            </div>
          </div>

          <div className="w-full flex gap-2 overflow-x-auto pb-2 whitespace-nowrap scrollbar-hide">
            {mediaChoice === 1
              ? images.map((image: ImageType, index: number) => (
                  <div
                    key={index}
                    onClick={() => {
                      setIsModalOpen(true);
                      setSelectedIndex(index);
                    }}
                    className="w-[100px] h-[100px] flex-shrink-0 cursor-pointer"
                  >
                    <img
                      src={image.src}
                      alt={image.alt || `Image ${index + 1}`}
                      className="w-full h-full object-cover rounded-md"
                    />
                  </div>
                ))
              : documents.map((doc: DocumentType, index: number) => (
                  <div
                    key={doc.id}
                    onClick={() => {
                      setSelectedIndex(index);
                      setIsDocModalOpen(true);
                    }}
                    className="min-w-[150px] p-4 bg-gray-100 rounded-md cursor-pointer flex flex-col items-center transition-transform text-center"
                  >
                    <span className="text-4xl">
                      {getFileIcon(doc.fileType)}
                    </span>
                    <span className="mt-2 font-bold text-xs break-words">
                      {doc.name}
                    </span>
                    <span className="mt-1 text-xs text-gray-600">
                      {doc.fileType.toUpperCase()}
                    </span>
                  </div>
                ))}
          </div>
        </div>

        {/* Members */}
        <div className="w-full flex flex-col gap-3">
          <div className="flex gap-2 items-center">
            <FaUsers size={16} className="text-gray-400" />
            <h4 className="text-sm font-semibold text-gray-400">
              {member} ({members.length})
            </h4>
          </div>
          <div className="flex flex-col gap-4">
            {members.map((member: MemberType, index: number) => (
              <div
                key={`${member.id}-${index}`}
                className="w-full flex justify-between"
              >
                <div className="flex gap-2 items-center">
                  <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-bold">
                    {member.name.charAt(0)}
                  </div>
                  <div className="flex flex-col gap-0.5">
                    <h3 className="text-sm font-medium m-0">{member.name}</h3>
                    <p className="text-xs text-gray-400 m-0">{member.status}</p>
                  </div>
                </div>
                {member.role === "moderator" && (
                  <div className="h-10 px-3 bg-blue-100 text-blue-500 flex items-center justify-center rounded-md">
                    {userType}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatSideBar;
