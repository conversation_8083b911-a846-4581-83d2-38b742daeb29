import { FC } from 'react'


const Message: FC<{
  isYou: boolean;
  pic: string;
  message: string;
  time: string;
  user: string;
  msgColorScheme: string;
}> = ({ isYou, message, pic, time, user, msgColorScheme }) => {
  const formatTime = (isoString: string): string => {
    const date = new Date(isoString);
    return date.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
      timeZone: "UTC",
    });
  };

  const stringToColor = (str: string): string => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }
    const color = `hsl(${hash % 360}, 70%, 50%)`;
    return color;
  };

  return (
    <div className="flex flex-col gap-3 mb-5 font-sans">
      <div className={`flex gap-5 items-end ${isYou ? "justify-end" : ""}`}>
        <div
          className={`${isYou ? "order-2" : "order-1"} w-[50px] h-[50px] rounded-full`}
        >
          <img
            width={50}
            height={50}
            src={pic}
            alt="profile pic"
            className="w-full h-full rounded-full object-cover"
          />
        </div>
        <div
          className={`${isYou ? "order-1" : "order-2"} ${
            isYou
              ? "rounded-tr-none bg-[var(--msgColorScheme)] text-white"
              : "rounded-tl-none"
          } w-fit min-w-[150px] p-1 px-3 shadow-[0_2px_4px_rgba(0,0,0,0.3)] rounded-lg`}
          style={{ background: isYou ? msgColorScheme : "" }}
        >
          <h4
            className="text-sm m-0"
            style={{ color: isYou ? "#fff" : stringToColor(user) }}
          >
            {user}
          </h4>
          <p>{message}</p>
        </div>
      </div>
      <div className={`flex ${isYou ? "justify-end" : ""} px-[70px]`}>
        <p className="text-black font-normal text-xs">{formatTime(time)}</p>
      </div>
    </div>
  );
};

export default Message