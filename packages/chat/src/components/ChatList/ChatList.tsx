import { ReactElement } from "react";
import { EventData, EventDataType } from "../../mockData/Data";
import EventCards from "./EventCards";

type ChatListProps = {
  title: string;
  length: number;
  eventText: string;
};

const ChatList = ({ title, length, eventText }: ChatListProps): ReactElement => {
  return (
    <div className="chat-list">
      <h3
        style={{ fontSize: "14px" }}
        className="text-[#2F2F2F] flex font-medium border-b pb-2 border-[#D9D9D9]"
      >
        <span className="pr-2">{title}</span>(<span className="">{length}</span>
        )
      </h3>

      <div className="py-3">
        {EventData.map((event: EventDataType) => (
          <EventCards
            attendeeCount={event.attendeeCount}
            attendeeImages={event.attendees}
            date={event.date}
            eventThumbnail={event.image}
            eventText={eventText}
            title={event.title}
            attendeesText={event.attendeesText}
            viewEventText={event.viewEventText}
            key={event.id}
          />
        ))}
      </div>
    </div>
  );
};

export default ChatList;
