import { ReactElement } from 'react';
import { MapPin } from "lucide-react";

type EventTextProps = {
  eventText: string;
  date:string,
  title:string,
  attendeeCount:number,
  attendeeImages:string[],
  eventThumbnail:string,
  attendeesText:string,
  viewEventText:string
};

const EventCards = ({ eventText, date, title, attendeeCount, attendeeImages, eventThumbnail, attendeesText, viewEventText}: EventTextProps): ReactElement => {
  return (
    <div className="flex items-center justify-between gap-4 my-2">
      <img src={eventThumbnail} alt="" className="w-[160px] h-[118px] rounded-2xl" />
      <div className="border shadow flex-1 border-[#F0F0F0] rounded-2xl px-4 py-3">
        <h4 className="text-[#767676] font-semibold text-sm">
          {date}
        </h4>
        <h2 style={{ fontSize: "18px" }} className="text-[#2F2F2F] font-bold ">
          {title}
        </h2>
        <div className="text-[#767676] flex items-center">
          <MapPin className="h-4" />
          <p style={{ fontSize: "14px" }} className="">
            {eventText}
          </p>
        </div>
        <div className="flex items-center justify-between">
          <p
            style={{ fontSize: "14px" }}
            className="text-[#2F2F2F] underline font-normal"

          >
           {viewEventText}
          </p>
          <div className="flex items-center justify-between gap-2">
            <StackedImages
              images={attendeeImages}
            />
            <h3 style={{ fontSize: "14px" }} className="font-normal flex items-center ml-5 text-[#767676]">
              <span className="">{attendeeCount}</span>
              <span className="">{attendeesText}</span>
            </h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventCards;

const StackedImages = ({ images }: { images: string[] }): ReactElement => {
  return (
    <div className="flex relative items-center">
      {images.map((image, index) => (
        <div
          key={index}
          className={`relative  transition-transform duration-300 ${
            index === 0 ? "z-10" : `-ml-12 z-${10 - index}`
          }`}
        >
          <img
            src={image}
            alt={` ${index + 1}`}
            className=" object-cover shadow-lg rounded-full h-[28px] w-[28px]"
          />
        </div>
      ))}
    </div>
  );
};
