"use client";

import { FC, useState } from "react";
import { FaPlus, FaRegBell } from "react-icons/fa";

interface TopBarProps {
  username?: string;
  buttonText?: string;
  hasNotification?: boolean;
}

const TopBar: FC<TopBarProps> = ({
  username = "",
  buttonText = "",
  hasNotification = true,
}) => {
  const [showNotification, setShowNotification] = useState(hasNotification);

  // Define event handlers within the component
  const handleCreateClick = (): void => {
    console.log("Create button clicked");
  };

  const handleNotificationClick = (): void => {
    console.log("Notification clicked");
    setShowNotification(false); // Clear notification when clicked
  };

  return (
    <div className="flex justify-between items-center w-full px-5 py-3 border-b border-[#D9D9D9]">
      {/* Welcome message */}
      <div className="text-[#2F2F2F] text-lg font-medium">
        Welcome {username}{" "}
        <span role="img" aria-label="waving hand">
          👋
        </span>
      </div>

      {/* Right side actions */}
      <div className="flex items-center space-x-4">
        {/* Create button */}
        <button
          onClick={handleCreateClick}
          className="flex items-center space-x-2 border border-[#D9D9D9] rounded px-4 py-2 text-gray-300 hover:bg-gray-800 transition-colors"
        >
          <FaPlus size={16} color="#2F2F2F" />
          <span className="text-[#2F2F2F]">{buttonText}</span>
        </button>

        {/* Notification icon */}
        <div
          className="relative cursor-pointer"
          onClick={handleNotificationClick}
        >
          <FaRegBell size={20} color="#2F2F2F" />
          {showNotification && (
            <span className="absolute -top-1 -right-1 bg-red-500 rounded-full w-2 h-2"></span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopBar;
