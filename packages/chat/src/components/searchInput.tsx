"use client";
import { FC, FormEvent, useState } from "react";
import { FaSearch } from "react-icons/fa";

interface SearchInputProps {
  placeholder?: string;
  onSearch: (query: string) => void;
}

const SearchInput: FC<SearchInputProps> = ({
  placeholder = "Search for events, chats....",
  onSearch,
}) => {
  const [query, setQuery] = useState("");

  const handleSearch = (event: FormEvent): void => {
    event.preventDefault();
    onSearch(query);
  };

  return (
    <form onSubmit={handleSearch} className="relative w-full max-w-md">
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        placeholder={placeholder}
        className="block w-full rounded-full border border-gray-300 shadow-md pl-4 pr-10 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        aria-label="Search"
      />
      <button
        type="submit"
        className="absolute inset-y-0 right-0 flex items-center pr-3"
      >
        <FaSearch className="h-5 w-5 text-gray-500" aria-hidden="true" />
      </button>
    </form>
  );
};

export default SearchInput;
