import { FC, ReactNode } from "react";
import { FaCog, FaSignOutAlt, FaCalendarAlt, FaHeadset } from "react-icons/fa";

interface NavButtonProps {
  label: string;
  icon: ReactNode;
  onClick?: () => void;
}

interface SideBarProps {
  profileImageSrc?: string;
  userName?: string;
  userId?: string;
  navButtons?: NavButtonProps[];
  footerButtons?: NavButtonProps[];
}

const SideBar: FC<SideBarProps> = ({
  profileImageSrc = "",
  userName = "",
  userId = "",
}) => {
  return (
    <div className="flex flex-col h-screen w-64 bg-gray-200 shadow-lg p-4">
      {/* User Profile Section */}
      <div className="flex flex-col items-center mb-8">
        <div className="w-12 h-12 mb-2">
          <img
            src={profileImageSrc || "https://placehold.co/48x48"}
            alt={`${userName}'s Profile`}
            className="w-full h-full rounded-full  object-cover"
          />
        </div>
        <h2 className="text-lg font-semibold">{userName}</h2>
        <p className="text-sm text-gray-500">{userId}</p>
      </div>

      {/* Navigation Buttons */}
      <div className="flex flex-col space-y-2 mb-auto">
        <button className="flex justify-center items-center p-2 text-gray-700 hover:bg-gray-200 rounded-lg">
          <FaCalendarAlt className="mr-2" />
          Events
        </button>
        <button className="flex justify-center items-center p-2 text-gray-700 hover:bg-gray-200 rounded-lg">
          <FaHeadset className="mr-2" />
          Support
        </button>
      </div>

      {/* Footer Section */}
      <div className="flex flex-col space-y-2">
        <button className="flex justify-center items-center p-2 text-gray-700 hover:bg-gray-200 rounded-lg">
          <FaCog className="mr-2" />
          Settings
        </button>
        <button className="flex justify-center items-center p-2 text-gray-700 hover:bg-gray-200 rounded-lg">
          <FaSignOutAlt className="mr-2" />
          Logout
        </button>
      </div>
    </div>
  );
};

export default SideBar;
