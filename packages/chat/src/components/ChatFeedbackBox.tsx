import { FC, useState } from "react";

interface ChatFeedbackBoxProps {
  title?: string;
  feedbackOptions?: { label: string; icon: string; value: string }[];
  selectedText?: string;
}

const ChatFeedbackBox: FC<ChatFeedbackBoxProps> = ({
  title = "How would you rate this event?",
  feedbackOptions = [
    { label: "Good", icon: "👍", value: "good" },
    { label: "Neutral", icon: "✋", value: "neutral" },
    { label: "Bad", icon: "👎", value: "bad" },
  ],
  selectedText = "You selected:",
}) => {
  const [selectedFeedback, setSelectedFeedback] = useState<string | null>(null);

  const handleFeedbackClick = (feedback: string): void => {
    setSelectedFeedback(feedback);
  };

  return (
    <div className="bg-gray-100 rounded-lg shadow-md p-6 text-center">
      <h2 className="text-lg font-semibold text-gray-700 mb-4">{title}</h2>
      <div className="flex justify-center space-x-7">
        {feedbackOptions.map((feedback) => (
          <button
            key={feedback.value}
            className={`flex flex-col items-center cursor-pointer focus:outline-none transition-transform transform ${
              selectedFeedback === feedback.value
                ? "scale-110 text-blue-500"
                : "hover:scale-105 hover:text-gray-800"
            }`}
            onClick={() => handleFeedbackClick(feedback.value)}
            aria-label={feedback.label}
          >
            <span className="text-3xl">{feedback.icon}</span>
            <span
              className={`text-gray-600 ${
                selectedFeedback === feedback.value ? "font-semibold" : ""
              }`}
            >
              {feedback.label}
            </span>
          </button>
        ))}
      </div>
      {selectedFeedback && (
        <p className="mt-4 text-sm text-gray-500">
          {selectedText}{" "}
          <span className="font-semibold">{selectedFeedback}</span>
        </p>
      )}
    </div>
  );
};

export default ChatFeedbackBox;
