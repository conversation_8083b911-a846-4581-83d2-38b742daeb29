"use client";
import { FC, JSX } from "react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  previousText?: string;
  nextText?: string;
}

const Pagination: FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  previousText = "Previous",
  nextText = "Next",
}) => {
  const handlePrevious = (): void => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = (): void => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const renderPageNumbers = (): JSX.Element[] => {
    const pages = [];
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1, 2, 3);
      if (currentPage > 4) {
        pages.push("...");
      }
      for (
        let i = Math.max(4, currentPage - 1);
        i <= Math.min(totalPages - 3, currentPage + 1);
        i++
      ) {
        pages.push(i);
      }
      if (currentPage < totalPages - 3) {
        pages.push("...");
      }
      pages.push(totalPages - 2, totalPages - 1, totalPages);
    }
    return pages.map((page, index) => (
      <button
        key={index}
        onClick={() => typeof page === "number" && onPageChange(page)}
        className={`w-10 h-10 flex items-center justify-center rounded-lg font-bold ${currentPage === page ? "bg-blue-300 text-black" : "text-gray-700"}`}
        disabled={typeof page !== "number"}
      >
        {page}
      </button>
    ));
  };

  return (
    <div className="w-full flex items-center justify-between px-6">
      <button
        onClick={handlePrevious}
        className="px-4 py-2 border border-gray-400 rounded-lg disabled:opacity-50"
        aria-label="Previous Page"
        disabled={currentPage === 1}
      >
        {previousText}
      </button>
      <div className="flex space-x-2">{renderPageNumbers()}</div>
      <button
        onClick={handleNext}
        className="px-4 py-2 border border-gray-400 rounded-lg disabled:opacity-50"
        aria-label="Next Page"
        disabled={currentPage === totalPages}
      >
        {nextText}
      </button>
    </div>
  );
};

export default Pagination;
