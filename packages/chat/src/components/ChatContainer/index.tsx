import { FC} from 'react'
import Message from '../Message';

type Message = {
  profile: string;
  message: string;
  time: string; // ISO 8601 format date string
  user: string;
  msgColorScheme: string;
};

type MessagesByDay = {
  day: string;
  messages: Message[];
}[];




const ChatContainer: FC<{ messages: MessagesByDay }> = ({
  messages = [],
}) => {
  return (
    <div className="w-full h-screen bg-white p-8 flex flex-col-reverse overflow-y-auto font-sans [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
      {messages.map((chatDay, index) => (
        <div key={index} className="mb-4">
          <div className="w-fit px-3 py-1 shadow-[0_1px_2px_rgba(0,0,0,0.3)] font-semibold text-base text-black rounded-lg mx-auto my-5">
            {chatDay.day}
          </div>
          <div className="space-y-5">
            {chatDay.messages.map((msg, msgIndex) => (
              <Message
                key={msgIndex}
                msgColorScheme={msg.msgColorScheme}
                isYou={msg.user == "You"}
                message={msg.message}
                pic={msg.profile}
                user={msg.user}
                time={msg.time}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};


export default ChatContainer