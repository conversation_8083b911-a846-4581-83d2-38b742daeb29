import { FC } from "react";
import { FaArrowLeft } from "react-icons/fa";

interface ChatHeaderProps {
  title: string;
  username: string;
  userImage?: string;
  typingUsers?: string[];
  typingText?: string;
}

const ChatHeader: FC<ChatHeaderProps> = ({
  title,
  username,
  userImage,
  typingUsers = [],
  typingText = "is typing...",
}) => {
  const defaultImage = "https://placehold.co/50x50";

  const typingMessage =
    typingUsers.length === 1
      ? `${typingUsers[0]} ${typingText}`
      : typingUsers.length > 1
        ? `${typingUsers.join(", ")} are typing...`
        : null;

  return (
    <div className="flex items-center p-4 bg-white shadow-md">
      <button className="mr-4">
        <FaArrowLeft className="w-6 h-6" aria-label="Back" />
      </button>
      <div className="flex items-center flex-grow">
        <img
          src={userImage || defaultImage}
          alt={username}
          className="w-10 h-10 rounded-full mr-2"
        />
        <div className="flex flex-col">
          <h1 className="text-lg font-semibold">{title}</h1>
          {typingMessage && (
            <div className="flex items-center">
              <span className="text-sm text-gray-500">{typingMessage}</span>
              <span className="ml-1 w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatHeader;
