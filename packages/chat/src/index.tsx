import Header from "./components/Header";
import { ReactElement } from 'react';

const Chat = (): ReactElement => {
  return (
    <div>
      <Header />
    </div>
  );
};


export { default as ChatList } from "./components/ChatList/ChatList";
export { default as ChatContainer } from "./components/ChatContainer";
export { default as Message } from "./components/Message";
export { default as ChatSideBar } from "./components/ChatSideBar";
export { default as EventCards } from "./components/ChatList/EventCards";
export { default as ChatTabMenu } from "./components/ChatTabMenu";
export { default as ChatInputField } from "./components/chatInputField";
export { default as ChatHeader } from "./components/ChatHeader";
export { default as SearchInput } from "./components/searchInput";
export { default as SideBar } from "./components/SideBar";
export { default as TopBar } from "./components/TopBar";

export default Chat;
