declare module 'next/Image' {
  import { DetailedHTMLProps, ImgHTMLAttributes } from 'react';
  
  type ImageProps = {
    src: string;
    alt: string;
    width?: number;
    height?: number;
    layout?: 'fixed' | 'intrinsic' | 'responsive' | 'fill';
    priority?: boolean;
    loading?: 'lazy' | 'eager';
    objectFit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';
    objectPosition?: string;
    quality?: number;
    placeholder?: 'blur' | 'empty';
    blurDataURL?: string;
  } & DetailedHTMLProps<ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>;
  
  const Image: React.FC<ImageProps>;
  export default Image;
}