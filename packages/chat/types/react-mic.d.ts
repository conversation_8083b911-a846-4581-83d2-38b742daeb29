declare module 'react-mic' {
  import { Component } from 'react';

  export interface ReactMicProps {
    record?: boolean;
    className?: string;
    onStop?: (blobObject: { blob: Blob, startTime: number, stopTime: number, options: any }) => void;
    onData?: (recordedData: any) => void;
    onBlock?: () => void;
    strokeColor?: string;
    backgroundColor?: string;
    mimeType?: string;
    visualSetting?: string;
    audioBitsPerSecond?: number;
    width?: string | number;
    height?: string | number;
  }

  export class ReactMic extends Component<ReactMicProps> {}
}