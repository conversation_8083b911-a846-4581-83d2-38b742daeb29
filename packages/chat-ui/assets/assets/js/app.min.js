$(document).ready((function(){function checkSize(){$(window).width()<=1200?$(".appbar").addClass("appbar-hidden"):$(".appbar").removeClass("appbar-hidden")}$("#mainNavTab a").on("click",(function(e){e.preventDefault(),$(this).tab("show")})),$("#chats-tab").on("click",(function(e){e.preventDefault(),$("body").removeClass("calls-tab-open friends-tab-open profile-tab-open"),$("body").addClass("chats-tab-open")})),$("#calls-tab").on("click",(function(e){e.preventDefault(),$("body").removeClass("chats-tab-open friends-tab-open profile-tab-open"),$("body").addClass("calls-tab-open")})),$("#friends-tab").on("click",(function(e){e.preventDefault(),$("body").removeClass("calls-tab-open chats-tab-open profile-tab-open"),$("body").addClass("friends-tab-open")})),$("#profile-tab").on("click",(function(e){e.preventDefault(),$("body").removeClass("calls-tab-open friends-tab-open chats-tab-open"),$("body").addClass("profile-tab-open")})),$("[data-chat-info-toggle]").on("click",(function(e){e.preventDefault(),$(".chat-info").addClass("chat-info-visible")})),$("[data-chat-info-close]").on("click",(function(e){e.preventDefault(),$(".chat-info").removeClass("chat-info-visible")})),$(".contacts-list .contacts-link").on("click",(function(){$(".main").addClass("main-visible")})),$(".contacts-list .media-link").on("click",(function(){$(".main").addClass("main-visible")})),$("[data-profile-edit]").on("click",(function(){$(".main").addClass("main-visible")})),$("[data-close]").on("click",(function(e){e.preventDefault(),$(".main").removeClass("main-visible")})),$(".chat-content").magnificPopup({delegate:"a.popup-media",type:"image",gallery:{enabled:!0,navigateByImgClick:!0,preload:[0,1]}}),$("[data-chat-filter]").on("click",(function(){let selectedOption=$(this).data("select");$("[data-chat-filter-list]").text($(this).text()),"all-chats"===selectedOption?$("[data-chat-list]").find("li").each((function(){$(this).show()})):($("[data-chat-list]").find("li").each((function(){$(this).hide()})),$("[data-chat-list] li."+selectedOption).show())})),$("[data-call-filter]").on("click",(function(){let selectedOption=$(this).data("select");$("[data-call-filter-list]").text($(this).text()),"all-calls"===selectedOption?$("[data-call-list]").find("li").each((function(){$(this).show()})):($("[data-call-list]").find("li").each((function(){$(this).hide()})),$("[data-call-list] li."+selectedOption).show())})),$("#createGroup").modalSteps({btnNextHtml:"Next",btnLastStepHtml:"Finish",disableNextButton:!1,completeCallback:function(){},callbacks:{},getTitleAndStep:function(){}}),$(document).on("change",".custom-file-input",(function(event){$(this).next(".custom-file-label").html(event.target.files[0].name)})),SVGInject(document.getElementsByClassName("injectable")),$("#appNavTab .nav-link").on("click",(function(){$(".backdrop").addClass("backdrop-visible"),$(".appnavbar-content").addClass("appnavbar-content-visible"),$("#appNavTab .nav-link").removeClass("active"),$(".chat-info").removeClass("chat-info-visible")})),$(".backdrop").on("click",(function(){$(".backdrop").removeClass("backdrop-visible"),$(".appnavbar-content").removeClass("appnavbar-content-visible"),$("#appNavTab .nav-link").removeClass("active")})),$("[data-apps-close]").on("click",(function(e){e.preventDefault(),$("body").removeClass("apps-visible"),$(".appbar").toggleClass("appbar-hidden"),$(".backdrop").removeClass("backdrop-visible")})),$("[data-toggle-appbar]").on("click",(function(e){e.preventDefault(),$(".appbar").removeClass("appbar-hidden"),$(".backdrop").addClass("backdrop-visible")})),$("[data-appcontent-close]").on("click",(function(e){e.preventDefault(),$(".backdrop").removeClass("backdrop-visible"),$(".appnavbar-content").removeClass("appnavbar-content-visible"),$("#appNavTab .nav-link").removeClass("active")})),$('.todo-item input[type="checkbox"]').click((function(){$(this).is(":checked")?$(this).parents(".todo-item").addClass("todo-task-done"):$(this).is(":not(:checked)")&&$(this).parents(".todo-item").removeClass("todo-task-done")})),checkSize(),$(window).resize(checkSize)}));