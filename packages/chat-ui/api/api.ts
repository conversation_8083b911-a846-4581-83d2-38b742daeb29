import { ILoginPasswordAvatar } from './../types/types'
import axios from 'axios'
import * as logger from '../logger';
import { IntercomMethod } from '../types';
import { isSSR } from '../utils';

export const instance = axios.create({
  withCredentials: true,
  baseURL: 'api/',
})

export const signUpRequest = ([
  login,
  password,
  image,
]: Array<ILoginPasswordAvatar>) => {
  const data = new FormData()
  //@ts-ignore
  data.append('login', login as string)
  //@ts-ignore
  data.append('password', password as string)
  //@ts-ignore

  data.append('image', image as Blob, 'Avatar')

  return instance.post('auth/register', data, {
    headers: {
      //@ts-ignore
      'Content-Type': `multipart/form-data; boundary=${data._boundary}`,
    },
  })
}

export const signInRequest = ([login, password]: Array<string>) => {
  return instance.post('auth/login', {
    login,
    password,
  })
}


/**
 * Safely exposes `window.Intercom` and passes the arguments to the instance
 *
 * @param method method passed to the `window.Intercom` instance
 * @param args arguments passed to the `window.Intercom` instance
 *
 * @see {@link https://developers.intercom.com/installing-intercom/docs/intercom-javascript}
 */
const IntercomAPI = (method: IntercomMethod, ...args: Array<any>) => {
  if (!isSSR && window.Intercom) {
    return window.Intercom.apply(null, [method, ...args]);
  } else {
    logger.log('error', `${method} Intercom instance is not initalized yet`);
  }
};

export default IntercomAPI;
