import React, { Component } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";

import Home from "../components/Home/Home";
import GuestChat from "../components/GuestChat";
import AgentChat from "../components/AgentChat";
import SignIn from "../components/SignIn/SignIn";
import SignUp from "../components/SignUp/SignUp";
import ResetPassword from "../components/ResetPassword/ResetPassword";
import Chats from "../components/Chats/Chats";
import Calls from "../components/Calls/Calls";
import Friends from "../components/Friends/Friends";
import Account from "../components/Account/Account";
import Documentation from "../components/Documentation/Documentation";


// Route component to manage app routes
class Routing extends Component {
  render() {
    return (
      <BrowserRouter>
        <Route path="/" component={Home} exact />
        <Route path="/SignIn" component={SignIn} exact />
        <Route path="/SignUp" component={SignUp} exact />
        <Route path="/reset-password" component={ResetPassword} exact />
        <Route path="/Documentation" component={Documentation} />
        <Route path="/quick-chat" component={GuestChat} exact />
        <Route path="/agent" component={AgentChat} exact />
        <Route path="/sign-in" component={SignIn} exact />
        <Route path="/sign-up" component={SignUp} exact />
        <Route path="/reset-password" component={ResetPassword} exact />
        <Route path="/account" component={Account} />
        <Route path="/chats" component={Chats} />
        <Route path="/calls" component={Calls} />
        <Route path="/friends" component={Friends} />
      </BrowserRouter>
    );
  }
}

export default Routing;
