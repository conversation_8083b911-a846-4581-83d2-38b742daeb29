#!/bin/bash

set -euo pipefail

# Show usage instructions
usage() {
  cat <<EOF
🧹 delete-release.sh

Deletes package versions from GitHub NPM packages. Supports two modes:
  - Snapshot mode: Deletes versions like <version>-<branch-name>-<timestamp> (e.g., 0.0.0-fix-branch-1234567890)
  - Stable mode: Deletes stable versions like x.x.x (e.g., 0.0.0, 0.0.1, 1.2.3)

📦 Usage:
  # Snapshot mode (delete snapshots for a branch):
  ./delete-release.sh snapshot <package-name> <branch-name>

  # Stable mode (delete one or more stable versions):
  ./delete-release.sh stable <package-name> <version1> [<version2> ...]

🔐 Requirements:
  - NPM_TOKEN must be set in the environment.
  - You must have permission to delete versions from the GitHub package registry.

📘 Examples:
  # Delete snapshots for a branch
  export NPM_TOKEN=ghp_xxx
  ./delete-release.sh snapshot login fix-button-hover-bug
  # Deletes versions like 0.2.0-fix-button-hover-bug-20250324150557

  # Delete specific stable versions
  export NPM_TOKEN=ghp_xxx
  ./delete-release.sh stable login 0.0.0 0.0.1 1.2.3
  # Deletes versions 0.0.0, 0.0.1, and 1.2.3
EOF
}

# Check for --help or -h
if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
  usage
  exit 0
fi

# Validate args
if [[ $# -lt 3 ]]; then
  echo "❌ Invalid usage."
  usage
  exit 1
fi

MODE="$1"
PACKAGE_NAME="$2"

if [[ -z "${NPM_TOKEN:-}" ]]; then
  echo "❌ NPM_TOKEN is not set"
  exit 1
fi

# Fetch package versions
echo "🔍 Fetching package versions for $PACKAGE_NAME..."
VERSIONS_JSON=$(curl -s -H "Authorization: Bearer ${NPM_TOKEN}" \
  -H "Accept: application/vnd.github+json" \
  "https://api.github.com/orgs/sparkstrand/packages/npm/$PACKAGE_NAME/versions")

if [[ -z "$VERSIONS_JSON" || "$VERSIONS_JSON" == "[]" ]]; then
  echo "✅ No versions found for $PACKAGE_NAME"
  exit 0
fi

# Function to delete a version by ID
delete_version() {
  local VERSION_ID="$1"
  if [[ -n "$VERSION_ID" ]]; then
    echo "🚨 Deleting version ID: $VERSION_ID"
    HTTP_STATUS=$(curl -X DELETE -s -o /dev/null -w "%{http_code}" \
      -H "Authorization: Bearer ${NPM_TOKEN}" \
      -H "Accept: application/vnd.github+json" \
      "https://api.github.com/orgs/sparkstrand/packages/npm/$PACKAGE_NAME/versions/$VERSION_ID")
    if [[ "$HTTP_STATUS" -eq 204 ]]; then
      echo " ✅ Deleted $VERSION_ID"
    else
      echo " ❌ Failed to delete $VERSION_ID (HTTP $HTTP_STATUS)"
    fi
  fi
}

# Handle modes
case "$MODE" in
  "snapshot")
    BRANCH_NAME="$3"
    echo "🔍 Checking for snapshot versions matching branch: $BRANCH_NAME..."
    FEATURE_VERSION_IDS=$(echo "$VERSIONS_JSON" | jq -r --arg BRANCH_NAME "$BRANCH_NAME" '
      .[] | select(.name | test("^[0-9]+\\.[0-9]+\\.[0-9]+-" + $BRANCH_NAME + "-[0-9]{10,14}$")) | .id
    ')

    if [[ -n "$FEATURE_VERSION_IDS" ]]; then
      echo "🗑️ Found snapshot version IDs for $PACKAGE_NAME from branch $BRANCH_NAME:"
      echo "$FEATURE_VERSION_IDS"
      echo "$FEATURE_VERSION_IDS" | while read -r VERSION_ID; do
        delete_version "$VERSION_ID"
      done
    else
      echo "✅ No snapshot versions found for $PACKAGE_NAME from branch $BRANCH_NAME"
    fi
    ;;

  "stable")
    shift 2  
    STABLE_VERSIONS=("$@")  
    if [[ ${#STABLE_VERSIONS[@]} -eq 0 ]]; then
      echo "❌ No stable versions specified."
      usage
      exit 1
    fi

    echo "🔍 Checking for stable versions: ${STABLE_VERSIONS[*]}..."
    for VERSION in "${STABLE_VERSIONS[@]}"; do
      STABLE_VERSION_ID=$(echo "$VERSIONS_JSON" | jq -r --arg VERSION "$VERSION" '
        .[] | select(.name == $VERSION) | .id
      ')
      if [[ -n "$STABLE_VERSION_ID" ]]; then
        echo "🗑️ Found stable version ID for $PACKAGE_NAME version $VERSION: $STABLE_VERSION_ID"
        delete_version "$STABLE_VERSION_ID"
      else
        echo "✅ No stable version $VERSION found for $PACKAGE_NAME"
      fi
    done
    ;;

  *)
    echo "❌ Invalid mode: $MODE. Use 'snapshot' or 'stable'."
    usage
    exit 1
    ;;
esac

echo "✅ Cleanup complete for $PACKAGE_NAME."
