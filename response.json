{"url": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43", "id": 2478614926, "node_id": "PR_kwDOMEBuU86TvKmO", "html_url": "https://github.com/sparkstrand/chat-application/pull/43", "diff_url": "https://github.com/sparkstrand/chat-application/pull/43.diff", "patch_url": "https://github.com/sparkstrand/chat-application/pull/43.patch", "issue_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/43", "number": 43, "state": "closed", "locked": false, "title": "Quick-Trigger-main-release ", "user": {"login": "Abdulgithub0", "id": 117740814, "node_id": "U_kgDOBwSVDg", "avatar_url": "https://avatars.githubusercontent.com/u/117740814?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Abdulgithub0", "html_url": "https://github.com/Abdulgithub0", "followers_url": "https://api.github.com/users/Abdulgithub0/followers", "following_url": "https://api.github.com/users/Abdulgithub0/following{/other_user}", "gists_url": "https://api.github.com/users/Abdulgithub0/gists{/gist_id}", "starred_url": "https://api.github.com/users/Abdulgithub0/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Abdulgithub0/subscriptions", "organizations_url": "https://api.github.com/users/Abdulgithub0/orgs", "repos_url": "https://api.github.com/users/Abdulgithub0/repos", "events_url": "https://api.github.com/users/Abdulgithub0/events{/privacy}", "received_events_url": "https://api.github.com/users/Abdulgithub0/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "body": null, "created_at": "2025-04-24T09:57:50Z", "updated_at": "2025-04-24T10:05:27Z", "closed_at": "2025-04-24T10:05:26Z", "merged_at": "2025-04-24T10:05:26Z", "merge_commit_sha": "5845d487532e52ee386ab6f0245e47ed50608a71", "assignee": null, "assignees": [], "requested_reviewers": [], "requested_teams": [], "labels": [], "milestone": null, "draft": false, "commits_url": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43/commits", "review_comments_url": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43/comments", "review_comment_url": "https://api.github.com/repos/sparkstrand/chat-application/pulls/comments{/number}", "comments_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/43/comments", "statuses_url": "https://api.github.com/repos/sparkstrand/chat-application/statuses/6e98e18387a60cdacff9de11bd7cca072f74c145", "head": {"label": "sparkstrand:Quick-Trigger-main-release", "ref": "Quick-Trigger-main-release", "sha": "6e98e18387a60cdacff9de11bd7cca072f74c145", "user": {"login": "sparkstrand", "id": 87420918, "node_id": "MDEyOk9yZ2FuaXphdGlvbjg3NDIwOTE4", "avatar_url": "https://avatars.githubusercontent.com/u/87420918?v=4", "gravatar_id": "", "url": "https://api.github.com/users/sparkstrand", "html_url": "https://github.com/sparkstrand", "followers_url": "https://api.github.com/users/sparkstrand/followers", "following_url": "https://api.github.com/users/sparkstrand/following{/other_user}", "gists_url": "https://api.github.com/users/sparkstrand/gists{/gist_id}", "starred_url": "https://api.github.com/users/sparkstrand/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/sparkstrand/subscriptions", "organizations_url": "https://api.github.com/users/sparkstrand/orgs", "repos_url": "https://api.github.com/users/sparkstrand/repos", "events_url": "https://api.github.com/users/sparkstrand/events{/privacy}", "received_events_url": "https://api.github.com/users/sparkstrand/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 809528915, "node_id": "R_kgDOMEBuUw", "name": "chat-application", "full_name": "sparkstrand/chat-application", "private": true, "owner": {"login": "sparkstrand", "id": 87420918, "node_id": "MDEyOk9yZ2FuaXphdGlvbjg3NDIwOTE4", "avatar_url": "https://avatars.githubusercontent.com/u/87420918?v=4", "gravatar_id": "", "url": "https://api.github.com/users/sparkstrand", "html_url": "https://github.com/sparkstrand", "followers_url": "https://api.github.com/users/sparkstrand/followers", "following_url": "https://api.github.com/users/sparkstrand/following{/other_user}", "gists_url": "https://api.github.com/users/sparkstrand/gists{/gist_id}", "starred_url": "https://api.github.com/users/sparkstrand/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/sparkstrand/subscriptions", "organizations_url": "https://api.github.com/users/sparkstrand/orgs", "repos_url": "https://api.github.com/users/sparkstrand/repos", "events_url": "https://api.github.com/users/sparkstrand/events{/privacy}", "received_events_url": "https://api.github.com/users/sparkstrand/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/sparkstrand/chat-application", "description": "Chat application to be used in all Spark Strand developed applications", "fork": false, "url": "https://api.github.com/repos/sparkstrand/chat-application", "forks_url": "https://api.github.com/repos/sparkstrand/chat-application/forks", "keys_url": "https://api.github.com/repos/sparkstrand/chat-application/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/sparkstrand/chat-application/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/sparkstrand/chat-application/teams", "hooks_url": "https://api.github.com/repos/sparkstrand/chat-application/hooks", "issue_events_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/events{/number}", "events_url": "https://api.github.com/repos/sparkstrand/chat-application/events", "assignees_url": "https://api.github.com/repos/sparkstrand/chat-application/assignees{/user}", "branches_url": "https://api.github.com/repos/sparkstrand/chat-application/branches{/branch}", "tags_url": "https://api.github.com/repos/sparkstrand/chat-application/tags", "blobs_url": "https://api.github.com/repos/sparkstrand/chat-application/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/sparkstrand/chat-application/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/sparkstrand/chat-application/git/refs{/sha}", "trees_url": "https://api.github.com/repos/sparkstrand/chat-application/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/sparkstrand/chat-application/statuses/{sha}", "languages_url": "https://api.github.com/repos/sparkstrand/chat-application/languages", "stargazers_url": "https://api.github.com/repos/sparkstrand/chat-application/stargazers", "contributors_url": "https://api.github.com/repos/sparkstrand/chat-application/contributors", "subscribers_url": "https://api.github.com/repos/sparkstrand/chat-application/subscribers", "subscription_url": "https://api.github.com/repos/sparkstrand/chat-application/subscription", "commits_url": "https://api.github.com/repos/sparkstrand/chat-application/commits{/sha}", "git_commits_url": "https://api.github.com/repos/sparkstrand/chat-application/git/commits{/sha}", "comments_url": "https://api.github.com/repos/sparkstrand/chat-application/comments{/number}", "issue_comment_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/comments{/number}", "contents_url": "https://api.github.com/repos/sparkstrand/chat-application/contents/{+path}", "compare_url": "https://api.github.com/repos/sparkstrand/chat-application/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/sparkstrand/chat-application/merges", "archive_url": "https://api.github.com/repos/sparkstrand/chat-application/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/sparkstrand/chat-application/downloads", "issues_url": "https://api.github.com/repos/sparkstrand/chat-application/issues{/number}", "pulls_url": "https://api.github.com/repos/sparkstrand/chat-application/pulls{/number}", "milestones_url": "https://api.github.com/repos/sparkstrand/chat-application/milestones{/number}", "notifications_url": "https://api.github.com/repos/sparkstrand/chat-application/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/sparkstrand/chat-application/labels{/name}", "releases_url": "https://api.github.com/repos/sparkstrand/chat-application/releases{/id}", "deployments_url": "https://api.github.com/repos/sparkstrand/chat-application/deployments", "created_at": "2024-06-02T23:52:51Z", "updated_at": "2025-04-24T10:05:29Z", "pushed_at": "2025-04-24T10:05:27Z", "git_url": "git://github.com/sparkstrand/chat-application.git", "ssh_url": "**************:sparkstrand/chat-application.git", "clone_url": "https://github.com/sparkstrand/chat-application.git", "svn_url": "https://github.com/sparkstrand/chat-application", "homepage": null, "size": 44003, "stargazers_count": 0, "watchers_count": 0, "language": "JavaScript", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 0, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 0, "license": null, "allow_forking": false, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 0, "open_issues": 0, "watchers": 0, "default_branch": "main"}}, "base": {"label": "sparkstrand:main", "ref": "main", "sha": "f09522b3a8f9099869db230e1148a277f77e9c9a", "user": {"login": "sparkstrand", "id": 87420918, "node_id": "MDEyOk9yZ2FuaXphdGlvbjg3NDIwOTE4", "avatar_url": "https://avatars.githubusercontent.com/u/87420918?v=4", "gravatar_id": "", "url": "https://api.github.com/users/sparkstrand", "html_url": "https://github.com/sparkstrand", "followers_url": "https://api.github.com/users/sparkstrand/followers", "following_url": "https://api.github.com/users/sparkstrand/following{/other_user}", "gists_url": "https://api.github.com/users/sparkstrand/gists{/gist_id}", "starred_url": "https://api.github.com/users/sparkstrand/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/sparkstrand/subscriptions", "organizations_url": "https://api.github.com/users/sparkstrand/orgs", "repos_url": "https://api.github.com/users/sparkstrand/repos", "events_url": "https://api.github.com/users/sparkstrand/events{/privacy}", "received_events_url": "https://api.github.com/users/sparkstrand/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "repo": {"id": 809528915, "node_id": "R_kgDOMEBuUw", "name": "chat-application", "full_name": "sparkstrand/chat-application", "private": true, "owner": {"login": "sparkstrand", "id": 87420918, "node_id": "MDEyOk9yZ2FuaXphdGlvbjg3NDIwOTE4", "avatar_url": "https://avatars.githubusercontent.com/u/87420918?v=4", "gravatar_id": "", "url": "https://api.github.com/users/sparkstrand", "html_url": "https://github.com/sparkstrand", "followers_url": "https://api.github.com/users/sparkstrand/followers", "following_url": "https://api.github.com/users/sparkstrand/following{/other_user}", "gists_url": "https://api.github.com/users/sparkstrand/gists{/gist_id}", "starred_url": "https://api.github.com/users/sparkstrand/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/sparkstrand/subscriptions", "organizations_url": "https://api.github.com/users/sparkstrand/orgs", "repos_url": "https://api.github.com/users/sparkstrand/repos", "events_url": "https://api.github.com/users/sparkstrand/events{/privacy}", "received_events_url": "https://api.github.com/users/sparkstrand/received_events", "type": "Organization", "user_view_type": "public", "site_admin": false}, "html_url": "https://github.com/sparkstrand/chat-application", "description": "Chat application to be used in all Spark Strand developed applications", "fork": false, "url": "https://api.github.com/repos/sparkstrand/chat-application", "forks_url": "https://api.github.com/repos/sparkstrand/chat-application/forks", "keys_url": "https://api.github.com/repos/sparkstrand/chat-application/keys{/key_id}", "collaborators_url": "https://api.github.com/repos/sparkstrand/chat-application/collaborators{/collaborator}", "teams_url": "https://api.github.com/repos/sparkstrand/chat-application/teams", "hooks_url": "https://api.github.com/repos/sparkstrand/chat-application/hooks", "issue_events_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/events{/number}", "events_url": "https://api.github.com/repos/sparkstrand/chat-application/events", "assignees_url": "https://api.github.com/repos/sparkstrand/chat-application/assignees{/user}", "branches_url": "https://api.github.com/repos/sparkstrand/chat-application/branches{/branch}", "tags_url": "https://api.github.com/repos/sparkstrand/chat-application/tags", "blobs_url": "https://api.github.com/repos/sparkstrand/chat-application/git/blobs{/sha}", "git_tags_url": "https://api.github.com/repos/sparkstrand/chat-application/git/tags{/sha}", "git_refs_url": "https://api.github.com/repos/sparkstrand/chat-application/git/refs{/sha}", "trees_url": "https://api.github.com/repos/sparkstrand/chat-application/git/trees{/sha}", "statuses_url": "https://api.github.com/repos/sparkstrand/chat-application/statuses/{sha}", "languages_url": "https://api.github.com/repos/sparkstrand/chat-application/languages", "stargazers_url": "https://api.github.com/repos/sparkstrand/chat-application/stargazers", "contributors_url": "https://api.github.com/repos/sparkstrand/chat-application/contributors", "subscribers_url": "https://api.github.com/repos/sparkstrand/chat-application/subscribers", "subscription_url": "https://api.github.com/repos/sparkstrand/chat-application/subscription", "commits_url": "https://api.github.com/repos/sparkstrand/chat-application/commits{/sha}", "git_commits_url": "https://api.github.com/repos/sparkstrand/chat-application/git/commits{/sha}", "comments_url": "https://api.github.com/repos/sparkstrand/chat-application/comments{/number}", "issue_comment_url": "https://api.github.com/repos/sparkstrand/chat-application/issues/comments{/number}", "contents_url": "https://api.github.com/repos/sparkstrand/chat-application/contents/{+path}", "compare_url": "https://api.github.com/repos/sparkstrand/chat-application/compare/{base}...{head}", "merges_url": "https://api.github.com/repos/sparkstrand/chat-application/merges", "archive_url": "https://api.github.com/repos/sparkstrand/chat-application/{archive_format}{/ref}", "downloads_url": "https://api.github.com/repos/sparkstrand/chat-application/downloads", "issues_url": "https://api.github.com/repos/sparkstrand/chat-application/issues{/number}", "pulls_url": "https://api.github.com/repos/sparkstrand/chat-application/pulls{/number}", "milestones_url": "https://api.github.com/repos/sparkstrand/chat-application/milestones{/number}", "notifications_url": "https://api.github.com/repos/sparkstrand/chat-application/notifications{?since,all,participating}", "labels_url": "https://api.github.com/repos/sparkstrand/chat-application/labels{/name}", "releases_url": "https://api.github.com/repos/sparkstrand/chat-application/releases{/id}", "deployments_url": "https://api.github.com/repos/sparkstrand/chat-application/deployments", "created_at": "2024-06-02T23:52:51Z", "updated_at": "2025-04-24T10:05:29Z", "pushed_at": "2025-04-24T10:05:27Z", "git_url": "git://github.com/sparkstrand/chat-application.git", "ssh_url": "**************:sparkstrand/chat-application.git", "clone_url": "https://github.com/sparkstrand/chat-application.git", "svn_url": "https://github.com/sparkstrand/chat-application", "homepage": null, "size": 44003, "stargazers_count": 0, "watchers_count": 0, "language": "JavaScript", "has_issues": true, "has_projects": true, "has_downloads": true, "has_wiki": false, "has_pages": false, "has_discussions": false, "forks_count": 0, "mirror_url": null, "archived": false, "disabled": false, "open_issues_count": 0, "license": null, "allow_forking": false, "is_template": false, "web_commit_signoff_required": false, "topics": [], "visibility": "private", "forks": 0, "open_issues": 0, "watchers": 0, "default_branch": "main"}}, "_links": {"self": {"href": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43"}, "html": {"href": "https://github.com/sparkstrand/chat-application/pull/43"}, "issue": {"href": "https://api.github.com/repos/sparkstrand/chat-application/issues/43"}, "comments": {"href": "https://api.github.com/repos/sparkstrand/chat-application/issues/43/comments"}, "review_comments": {"href": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43/comments"}, "review_comment": {"href": "https://api.github.com/repos/sparkstrand/chat-application/pulls/comments{/number}"}, "commits": {"href": "https://api.github.com/repos/sparkstrand/chat-application/pulls/43/commits"}, "statuses": {"href": "https://api.github.com/repos/sparkstrand/chat-application/statuses/6e98e18387a60cdacff9de11bd7cca072f74c145"}}, "author_association": "COLLABORATOR", "auto_merge": null, "active_lock_reason": null, "merged": true, "mergeable": null, "rebaseable": null, "mergeable_state": "unknown", "merged_by": {"login": "Abdulgithub0", "id": 117740814, "node_id": "U_kgDOBwSVDg", "avatar_url": "https://avatars.githubusercontent.com/u/117740814?v=4", "gravatar_id": "", "url": "https://api.github.com/users/Abdulgithub0", "html_url": "https://github.com/Abdulgithub0", "followers_url": "https://api.github.com/users/Abdulgithub0/followers", "following_url": "https://api.github.com/users/Abdulgithub0/following{/other_user}", "gists_url": "https://api.github.com/users/Abdulgithub0/gists{/gist_id}", "starred_url": "https://api.github.com/users/Abdulgithub0/starred{/owner}{/repo}", "subscriptions_url": "https://api.github.com/users/Abdulgithub0/subscriptions", "organizations_url": "https://api.github.com/users/Abdulgithub0/orgs", "repos_url": "https://api.github.com/users/Abdulgithub0/repos", "events_url": "https://api.github.com/users/Abdulgithub0/events{/privacy}", "received_events_url": "https://api.github.com/users/Abdulgithub0/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "comments": 3, "review_comments": 0, "maintainer_can_modify": false, "commits": 1, "additions": 2, "deletions": 2, "changed_files": 2}