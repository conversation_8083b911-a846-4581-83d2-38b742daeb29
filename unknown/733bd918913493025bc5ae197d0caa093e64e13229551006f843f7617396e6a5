import { z } from 'zod';
import { Database, DBLogger } from '../../utils';
import { ApplicationType, IApplication } from './application.types';

export class Application extends Database<"Application"> {
  private model = "application" as const;

  createSchema = z.object({
    name: z.string(),
    companyId: z.string(),
    type: z.enum([
      "Customer_Support", "Education", 
      "Team_Collaboration", "Healthcare_Teleconsultation", 
      "Ecommerce_Live_Shopping", "Community"]),
  });

  updateSchema = z.object({
    name: z.string().optional(),
    usersCount: z.number().optional(),
    guestsCount: z.number().optional(),
  });

  async createApplication(data: z.infer<typeof this.createSchema>): Promise<IApplication> {
    try {
      const parsedData = this.validateCreate(data);

      return await this.prisma.application.create({
        data: {
          name: parsedData.name,
          company: {
            connect: {
              id: parsedData.companyId
            }
          },
          type: parsedData.type
        }
      });
    } catch (error) {
      DBLogger.error(`Application creation failed, operationContext: ApplicationModel.createApplication, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationById(id: string): Promise<IApplication | null> {
    try {
      this.validateId(id);
      return await this.prisma.application.findUnique({where: { id }});
      
    } catch (error) {
      DBLogger.error(`Get application failed, operationContext: ApplicationModel.getApplicationById, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationByIdAndType(id: string, type: ApplicationType): Promise<IApplication | null> {
    try {
      this.validateId(id);
      return await this.prisma.application.findUnique({where: {id_type: { id, type }}});
    } catch (error) {
      DBLogger.error(`Get application failed, operationContext: ApplicationModel.getApplicationByIdAndType, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationByCompanyIdAndType(companyId: string, type: ApplicationType): Promise<IApplication | null> {
    try { 
      this.validateId(companyId);
      return await this.prisma.application.findUnique({where: {companyId_type: { companyId, type }}});
    } catch (error) {
      DBLogger.error(`Get application failed, operationContext: ApplicationModel.getApplicationByCompanyIdAndType, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationByCompanyIdAndName(companyId: string, name: string): Promise<IApplication | null> {
    try {
      if(!companyId || !name) {
        throw new Error("Validation failed: Missing required fields.");
      }
      this.validateId(companyId);
      return await this.prisma.application.findUnique({where: {companyId_name: { companyId, name }}});
      
    } catch (error) {
      DBLogger.error(`Get application failed, operationContext: ApplicationModel.getApplicationByNameAndCompanyId, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationByIdAndName(id: string, name: string): Promise<IApplication | null> {
    try {
      if(!name || !id) {
        throw new Error("Validation failed: Missing required fields.");
      }
      this.validateId(id);
      const application =  await this.prisma.application.findUnique({where: { id }});
      if(application && application.name === name) {
        return application;
      }

      return null;
    } catch (error) {
      DBLogger.error(`Get application failed, operationContext: ApplicationModel.getApplicationByIdAndName, message: ${error.message || error}`);
      throw error;
    }
  }

  async getApplicationsByCompanyId(companyId: string): Promise<IApplication[]> {
    try {
      this.validateId(companyId);
      return await this.prisma.application.findMany({where: { companyId }});
    } catch (error) {
      DBLogger.error(`Get applications failed, operationContext: ApplicationModel.getApplicationsByCompanyId, message: ${error.message || error}`);
      throw error;
    }
  }

  async updateApplication(id: string, data: z.infer<typeof this.updateSchema>): Promise<IApplication> {
    try {
      this.validateId(id);
      const parsedData = this.validateUpdate(data);
      return await this.prisma.application.update({where: { id }, data: parsedData});
      
    } catch (error) {
      DBLogger.error(`Application update failed, operationContext: ApplicationModel.updateApplication, message: ${error.message || error}`);
      throw error;
    }
  }

  async deleteApplication(id: string): Promise<null> {
    try {
      this.validateId(id)
      await this.prisma.application.delete({where: { id }});
      return null;
    } catch (error) {
      DBLogger.error(`Application deletion failed, operationContext: ApplicationModel.deleteApplication, message: ${error.message || error}`);
      throw error;
    }
  }
}