import { File, IFile, IMessageFile, IUpdateMessageFile, ICreateFile, IUpdateFile, } from "../../models";
import { IResponse } from "../../utils";

export class FileService {
  constructor(private readonly fileModel: File) {}

  async handleGuestUpload(data: ICreateFile[]): Promise<IResponse<IFile[] | null>> {
    try {
        
      const result = await this.fileModel.uploadGuestFile(data);
      return this.fileModel.formatResponse<IFile[]>({...this.fileModel.successParams, data: result, message: 'File created successfully'});
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.fileModel.formatResponse<null>({...this.fileModel.badRequestParams, message: error.message});
      }
      return this.fileModel.formatResponse<null>({...this.fileModel.internalServerErrorParams, message: error.message});
    }
  }

  async updateMessageFile(id: string, data: IUpdateMessageFile): Promise<IResponse<IMessageFile | null>> {
    try {
      const result = await this.fileModel.updateMessageFile(id, data);
      return this.fileModel.formatResponse<IMessageFile>({...this.fileModel.successParams, data: result, message: 'File updated successfully'});
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.fileModel.formatResponse<null>({...this.fileModel.badRequestParams, message: error.message});
      }
      return this.fileModel.formatResponse<null>({...this.fileModel.internalServerErrorParams, message: error.message});
    }
  }

  async deleteFile(id: string): Promise<IResponse<void>> {
    try {
      await this.fileModel.deleteFile(id);
      return this.fileModel.formatResponse<void>({...this.fileModel.successParams, message: 'File deleted successfully'});
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.fileModel.formatResponse<null>({...this.fileModel.badRequestParams, message: error.message});
      }
      return this.fileModel.formatResponse<null>({...this.fileModel.internalServerErrorParams, message: error.message});
    }
  }

  async createFile(data: ICreateFile[]): Promise<IResponse<IFile[] | null>> {
    try {
      const result = await this.fileModel.createFile(data);
      return this.fileModel.formatResponse<IFile[]>({...this.fileModel.successParams, data: result, message: 'File created successfully'});
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.fileModel.formatResponse<null>({...this.fileModel.badRequestParams, message: error.message});
      }
      return this.fileModel.formatResponse<null>({...this.fileModel.internalServerErrorParams, message: error.message});
    }
  }

  async updateFile(id: string, data: IUpdateFile): Promise<IResponse<IFile | null>> {
    try {
      const result = await this.fileModel.updateFile(id, data);
      return this.fileModel.formatResponse<IFile>({...this.fileModel.successParams, data: result, message: 'File updated successfully'});
    } catch (error: any) {
      if(error.message && error.message.includes('Validation failed')) {
        return this.fileModel.formatResponse<null>({...this.fileModel.badRequestParams, message: error.message});
      }
      return this.fileModel.formatResponse<null>({...this.fileModel.internalServerErrorParams, message: error.message});
    }
  }

}