import { z } from "zod";
import { Database, DBLogger, AuthService } from "../../utils";
import { IAccess<PERSON>ey, IUpdateAccess<PERSON>ey, ICreateAccessKey } from "./accesskey.types";
import { ICompany } from "../company/company.types";

export class AccessKey extends Database<"AccessKey"> {
  private model = "accessKey" as const;

  createSchema = z.object({
    name: z
      .string()
      .min(3, "Name must be at least 3 characters long")
      .max(255, "Name must not exceed 255 characters"),
    status: z.enum(["Active", "Revoked", "Expired"]).default("Active"),
    expiresAt: z.date().min(new Date(), "Expiration date must be in the future"),
    creatorId: z.string().min(1, "creatorId is required"),
    companyId: z.string().min(1, "companyId is required"),
  });

  updateSchema = z.object({
    name: z
      .string()
      .min(3, "Name must be at least 3 characters long")
      .max(255, "Name must not exceed 255 characters")
      .optional(),
    status: z.enum(["Active", "Revoked", "Expired"]).optional(),
    expiresAt: z.date().min(new Date(), "Expiration date must be in the future").optional(),
  });

  constructor() {
    super();
  }

  private async validateUniqueName(companyId: string, name: string, excludeId?: string): Promise<void> {
    const existingKey = await this.findFirst({
      model: this.model,
      where: { companyId, name, ...(excludeId && { id: { not: excludeId } }) },
    });
    if (existingKey) {
      throw new Error("Access key name already exists for this company");
    }
  }

  async createAccessKey(data: ICreateAccessKey): Promise<IAccessKey> {
    try {
      const validatedData = this.validateCreate(data) as ICreateAccessKey;
      await this.validateUniqueName(validatedData.companyId, validatedData.name);

      const apiKey = AuthService.generateKey();
      const apiKeySecret = AuthService.generateKey();
      const hashSecret = AuthService.hashKey(apiKeySecret);

      const result = await this.create({
        model: this.model,
        data: {
          ...validatedData,
          apiKey,
          apiKeySecret: hashSecret,
        },
      }) as IAccessKey;

      return { ...result, apiKeySecret };
    } catch (error) {
      DBLogger.error(`Access key creation failed, operationContext: AccessKey.createAccessKey, message: ${error.message || error}`);
      if (error?.code === "P2002") {
        throw new Error("Validation failed: access key name already exists");
      }
      throw error;
    }
  }

  async updateAccessKey(apiKey: string, apiKeySecret: string, data: IUpdateAccessKey): Promise<IAccessKey> {
    try {
      const key = await this.validateApiKeyAndSecret(apiKey, apiKeySecret);
      if(!key) {
        throw new Error("Validation failed: invalid keys");
      }
      const validatedData = this.validateUpdate(data) as IUpdateAccessKey;
      if (validatedData?.name) {
        await this.validateUniqueName(key.companyId, validatedData.name, key.id);
      }
      if(validatedData?.status && !['Active', 'Revoked, Expired'].includes(validatedData?.status)){
        throw new Error("Validation failed: Status must be one of: Active, Revoked, Expired");
      }

      const result = await this.update({
        model: this.model,
        where: { id: key.id },
        data: validatedData,
      }) as IAccessKey;

      return { ...result, apiKeySecret: undefined };
    } catch (error) {
      DBLogger.error(`Access key update failed, operationContext: AccessKey.updateAccessKey, message: ${error.message || error}`);
      throw error;
    }
  }

  async revokeAccessKey(apiKey: string, apiKeySecret: string): Promise<boolean> {
    try {
      const key = await this.validateApiKeyAndSecret(apiKey, apiKeySecret);
      if(!key) {
        throw new Error("Validation failed: invalid keys");
      }
      
      await this.update({
        model: this.model,
        where: { id: key.id },
        data: {
          status: "Revoked",
          expiresAt: new Date(),
        },
      });
      return true;
    } catch (error) {
      DBLogger.error(`Access key revocation failed, operationContext: AccessKey.revokeAccessKey, message: ${error.message || error}`);
      throw error;
    }
  }
  

  async deleteAccessKey(apiKey: string, apiKeySecret: string): Promise<void> {
    try {
      const key = await this.validateApiKeyAndSecret(apiKey, apiKeySecret);
      if(!key) {
        throw new Error("Validation failed: invalid keys");
      }

      await this.delete({
        model: this.model,
        where: { id: key.id },
      });
      return;
    } catch (error) {
      DBLogger.error(`Access key deletion failed, operationContext: AccessKey.deleteAccessKey, message: ${error.message || error}`);
      throw error;
    }
  }

  async validateApiKeyAndSecret(apiKey: string, apiKeySecret: string): Promise<IAccessKey | null> {
    try {
      const accessKey = await this.findUnique({
        model: this.model,
        where: { apiKey }
      }) as IAccessKey | null;

      if (!accessKey) { return null; }

      const hashedSecret = AuthService.hashKey(apiKeySecret);
      if (accessKey.apiKeySecret !== hashedSecret) {
        return null;
      }

      await this.update({
        model: this.model,
        where: { id: accessKey.id },
        data: { lastRequestAt: new Date() },
      });

      return { ...accessKey, apiKeySecret: undefined };
    } catch (error) {
      DBLogger.error(`API key validation failed, operationContext: AccessKey.validateApiKeyAndSecret, message: ${error.message || error}`);
      return null;
    }
  }

  async getCompanyByApiKey(apiKey: string): Promise<ICompany | null> {
    try {
      const result = await this.prisma.accessKey.findUnique({
        where: { apiKey },
        select: { Company: true },
      });

      return result?.Company || null;

    } catch (error) {
      DBLogger.error(`Get company by API key failed, operationContext: AccessKey.getCompanyByApiKey, message: ${error.message || error}`);
      return null;
    }
  }
}
