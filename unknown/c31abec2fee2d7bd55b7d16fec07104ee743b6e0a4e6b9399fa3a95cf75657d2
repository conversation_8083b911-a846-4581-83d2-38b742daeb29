# @sparkstrand/chat-api-client Documentation


## Overview

The @sparkstrand/chat-api-client SDK provides a simple way to integrate real-time chat functionality into other sparkstrand applications. It supports both browser-based (React) and server-side (Node.js) environments, allowing you to build interactive chat features like messaging, room management, and typing indicators.

The SDK is designed to work with the Sparkstrand Chat API, which uses Socket.IO for real-time communication and a guest authentication flow for secure access.

## Key Features

* **Browser Client**: Integrate chat into React apps with a useSocket hook for managing socket connections and state.
* **Server Client**: Perform server-side tasks like sending messages or managing rooms on behalf of users.
* **Shared Types**: TypeScript types for supported events, messages, rooms, and users ensure consistency across environments.
* **Authentication**: Supports a guest authentication flow with *clientToken* (browser) and *serverToken* (server).

## Installation

Install the SDK using Yarn or npm:

```bash
yarn add @sparkstrand/chat-api-client
```

or

```bash
npm install @sparkstrand/chat-api-client
```

For React applications, ensure you have react installed as a peer dependency:

```bash
yarn add react
```

## Authentication Flow

The SDK uses a  guest authentication flow to connect to the Sparkstrand Chat API:

### Initiation Phase:
1. Call the `api/v1/guests/initiate` endpoint with your API key, API secret, and metadata (including externalId).
2. Receive a *clientToken* (short-lived, for browser) and *serverToken* (long-lived, for server).

### Connection Phase (Browser Only):
1. Call `api/v1/guests/connect` with the *clientToken* to set a *sparkstrand_token* cookie.

### Socket.IO Connection:
* The browser client uses the *sparkstrand_token* cookie for authentication.
* The server client uses the *serverToken* in the auth object.

> **Note:** You'll need to expose an endpoint in your backend to call `api/v1/guests/initiate` and return the tokens.

## Usage

The SDK provides four main modules:

* **frontend**: Browser client (SocketClient) for React apps.
* **backend**: Server client (SocketClient) for Node.js.
* **hooks**: React hook (useSocket) for managing socket state in the browser.
* **types**: Shared TypeScript types (SocketEvent, IMessage, IRoom, IUser, etc.).

## Browser (React) Example

This example shows how to integrate the SDK into a React app to build a chat interface.

### Prerequisites

* A backend endpoint (e.g., `/api/auth/initiate`) that calls the chat server's `api/v1/guests/initiate` to obtain a *clientToken*.
* The chat server running at https://chat-application-h0xp.onrender.com (on prod) with `api/v1/guests/initiate` and `api/v1/guests/connect` endpoints.

### Example Code

#### Backend Endpoint (server/src/routes/auth.ts)

Create an endpoint to fetch the clientToken.

```typescript
import { Router } from 'express';
import axios from 'axios';

const router = Router();

router.post('/initiate', async (req, res) => {
  try {
    const { externalId } = req.body;
    if (!externalId) {
      return res.status(400).json({ error: 'externalId is required' });
    }

    const response = await axios.post('https://chat-application-h0xp.onrender.com/api/v1/guests/initiate', {
      apiKey: process.env.API_KEY,
      apiSecret: process.env.API_SECRET,
      metadata: { externalId }
    });

    const { clientToken } = response.data;

    res.json({ clientToken });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
```

#### React Component (client/src/App.tsx)

Use the useSocket hook to connect to the chat server and render a custom chat interface according to your preference.

```tsx
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useSocket } from '@sparkstrand/chat-api-client/hooks';
import { UserStatus } from '@sparkstrand/chat-api-client/types';

const App: React.FC = () => {
  const [clientToken, setClientToken] = useState('');
  const [externalId] = useState('user123');
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const { socket, state } = useSocket('https://chat-application-h0xp.onrender.com', clientToken);

  useEffect(() => {
    const initiateAuth = async () => {
      setIsAuthenticating(true);
      try {
        const response = await axios.post('http://localhost:4000/api/auth/initiate', { externalId });
        setClientToken(response.data.clientToken);
      } catch (error: any) {
        console.error('Authentication failed:', error.message);
      } finally {
        setIsAuthenticating(false);
      }
    };
    if (!clientToken) {
      initiateAuth();
    }
  }, [externalId]);

  if (isAuthenticating || !clientToken || !state.user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-4 bg-gray-200">
        <span>Status: {state.isConnected ? 'Connected' : 'Disconnected'}</span>
        <button onClick={() => socket.setUserStatus(UserStatus.ONLINE)}>Online</button>
        <button onClick={() => socket.setUserStatus(UserStatus.AWAY)}>Away</button>
        <button onClick={() => socket.setUserStatus(UserStatus.OFFLINE)}>Offline</button>
      </div>
      <div className="flex">
        <div className="w-64 bg-gray-100 p-4">
          <h2>Rooms</h2>
          {state.rooms.map((room) => (
            <div key={room.id}>
              <span>{room.name}</span>
              <button onClick={() => socket.joinRoom(room.id)}>Join</button>
            </div>
          ))}
        </div>
        <div className="flex-1 p-4">
          {state.user.currentRoomId && (
            <>
              <h2>{state.rooms.find(r => r.id === state.user.currentRoomId)?.name}</h2>
              {state.messages
                .filter(m => m.roomId === state.user.currentRoomId)
                .map((message) => (
                  <div key={message.id}>
                    <strong>{message.from?.id === state.user.id ? 'You' : message.from?.id}</strong>: {message.text}
                  </div>
                ))}
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  const input = e.currentTarget.querySelector('input');
                  if (input && input.value) {
                    socket.sendMessage({
                      roomId: state.user.currentRoomId!,
                      text: input.value,
                      from: { id: state.user.id, type: 'guest' }
                    });
                    input.value = '';
                  }
                }}
              >
                <input type="text" placeholder="Type a message..." />
                <button type="submit">Send</button>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default App;
```

#### Setup:

1. Start your backend server (e.g., at http://localhost:4000).
2. Ensure the sparkstrand chat server is running by pinging https://chat-application-h0xp.onrender.com/api/v1/health
3. Run the React app (e.g., with vite or create-react-app).

## Server (Node.js) Example

This example shows how to use the server client to send messages or manage rooms programmatically.

### Example Code

#### Example 1: Token-based Authentication (Existing Approach)

```typescript
import express from 'express';
import axios from 'axios';
import { ChatBackendClient, SocketEvent } from '@sparkstrand/chat-api-client/backend';
import { IMessage } from '@sparkstrand/chat-api-client/types';

const app = express();
app.use(express.json());

app.post('/api/auth/initiate', async (req, res) => {
  try {
    const { externalId } = req.body;
    const response = await axios.post('https://chat-application-h0xp.onrender.com/api/v1/guests/initiate', {
      apiKey: process.env.API_KEY,
      apiSecret: process.env.API_SECRET,
      metadata: { externalId }
    });
    res.json(response.data);
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
});

async function initializeSocketServer() {
  const response = await axios.post('http://localhost:4000/api/auth/initiate', {
    externalId: 'user123'
  });
  const { serverToken } = response.data;

  const client = new ChatBackendClient({
    url: "https://chat-application-h0xp.onrender.com",
    id: "guest-123",
    token: serverToken,
    debug: true
  });

  client.on(SocketEvent.AUTHENTICATED, (data) => {
    console.log('Authenticated:', data);
    const message: IMessage = {
      roomId: 'room1',
      text: 'Welcome from server!',
      from: { id: 'server', type: 'agent' }
    };
    client.sendMessage(message);
  });

  client.on(SocketEvent.NEW_MESSAGE, (message: IMessage) => {
    console.log('New message:', message);
  });

  client.connect();
}

app.listen(4000, () => {
  console.log('Server running on http://localhost:4000');
  initializeSocketServer();
});
```

#### Example 2: API Key/Secret Authentication (New Approach for First-Party Clients)

```typescript
import { ChatBackendClient, SocketEvent } from '@sparkstrand/chat-api-client/backend';
import { IMessage } from '@sparkstrand/chat-api-client/types';

async function initializeWithApiKey() {
  // Create client with API key and secret
  const client = new ChatBackendClient({
    url: "https://chat-application-h0xp.onrender.com",
    id: "guest-123",
    apiKey: process.env.API_KEY,
    apiKeySecret: process.env.API_SECRET,
    debug: true,
    autoConnect: false // Don't connect until we have a token
  });

  // Set up event listeners
  client.on(SocketEvent.AUTHENTICATED, (data) => {
    console.log('Authenticated:', data);

    // Send a message
    const message: IMessage = {
      roomId: 'room1',
      text: 'Hello from API key auth!',
      from: { id: 'guest-123', type: 'guest' }
    };
    client.sendMessage(message);
  });

  client.on(SocketEvent.NEW_MESSAGE, (message: IMessage) => {
    console.log('New message:', message);
  });

  try {
    // Initiate connection to get tokens
    const initResult = await client.initiateConnection({
      externalId: 'user123', // Your platform's user ID
      username: 'John Doe',
      email: '<EMAIL>'
    });

    console.log('Initiation successful');
    console.log('Client token (for browser):', initResult.data?.clientToken);
    console.log('Server token (for server):', initResult.data?.serverToken);

    // The token is automatically set in the client and it will connect

    // Use REST client for API calls
    const rest = client.Rest();

    // Get guest info
    const guestInfo = await rest.getGuestInfo();
    console.log('Guest info:', guestInfo);

    // Create a room using Room client
    const room = client.Room();
    const newRoom = await room.createRoom({
      name: 'New Room',
      membersId: ['guest-123', 'guest-456']
    });
    console.log('Room created:', newRoom);

  } catch (error) {
    console.error('Failed to initiate connection:', error);
  }
}

initializeWithApiKey().catch(console.error);
```

#### Setup:

1. Create a `.env` file with `API_KEY` and `API_SECRET`.
2. Install dependencies: `yarn add express axios @sparkstrand/chat-api-client`.
3. Run the server: `node dist/index.js` (after building with tsc).

## API Reference

### Browser Client (SocketClient)

```typescript
import { SocketClient } from '@sparkstrand/chat-api-client/frontend';
```

#### Constructor:

```typescript
const socket = new SocketClient({
  url: "https://chat-application-h0xp.onrender.com",
  debug: true, // by default it's false
  autoConnect: false // by default it's true
});
```

#### Methods:

* `connectGuest(clientToken: string)`: Authenticate with clientToken and connect.
* `joinRoom(roomId: string)`: Join a room.
* `sendMessage(message: IMessage)`: Send a message.
* `sendTypingIndicator(roomId: string)`: Indicate typing.
* `setUserStatus(status: UserStatus)`: Set user status (e.g., UserStatus.ONLINE).
* `disconnect()`: Disconnect the socket.

### React Hook (useSocket)

```typescript
import { useSocket } from '@sparkstrand/chat-api-client/hooks';
```

#### Usage:

```typescript
const { socket, state } = useSocket('https://chat-application-h0xp.onrender.com', clientToken);
```

#### Returns:

* `socket`: The SocketClient instance.
* `state`: Object with isConnected, user, rooms, messages, typingUsers, error, eventHistory.

### Server Client (ChatBackendClient)

```typescript
import { ChatBackendClient } from '@sparkstrand/chat-api-client/backend';
```

#### Constructor:

```typescript
// Token-based authentication (existing approach)
const client = new ChatBackendClient({
  url: "https://chat-application-h0xp.onrender.com",
  id: "guest-123",
  token: "your-auth-token", // Token from previous authentication
  debug: true
});

// OR

// API key/secret authentication (new approach for first-party clients)
const client = new ChatBackendClient({
  url: "https://chat-application-h0xp.onrender.com",
  id: "guest-123",
  apiKey: "your-api-key",
  apiKeySecret: "your-api-key-secret",
  debug: true,
  autoConnect: false // Don't connect until we have a token
});
```

#### Socket Methods:

* `setToken(serverToken: string)`: Set the serverToken for authentication.
* `connect()`: Connect to the server.
* `sendMessage(message: IMessage)`: Send a message.
* `joinRoom(roomId: string)`: Join a room.
* `disconnect()`: Disconnect the socket.

#### REST API Methods:

* `setApiCredentials(apiKey: string, apiKeySecret: string)`: Set API key and secret for first-party authentication.
* `initiateConnection(metadata: Record<string, any>)`: Initiate a guest connection using API key/secret.
* `loginGuest(id: string)`: Login a guest with their ID using API key.
* `Rest()`: Get the REST client for making API calls.
* `Room()`: Get the Room client for room management.

### Types

```typescript
import { SocketEvent, IMessage, IRoom, IUser, UserStatus } from '@sparkstrand/chat-api-client/types';
```

* `SocketEvent`: Enum of socket events (e.g., NEW_MESSAGE, ROOM_JOINED).
* `IMessage`: Message interface (roomId, text, from, etc.).
* `IRoom`: Room interface (id, name, type, etc.).
* `IUser`: User interface (id, type, status, etc.).
* `UserStatus`: Enum of user statuses (ONLINE, AWAY, OFFLINE).

## Troubleshooting

### Authentication Errors:
* Ensure `API_KEY` and `API_SECRET` are correct in your backend.
* Verify the chat server is running and accessible.

### Connection Issues:
* Check that the `url` in SocketClient matches the chat server.
* Enable `debug: true` to log socket events.

### React Hook Not Updating:
* Ensure `clientToken` is set before calling useSocket.
* Check for errors in `state.error`.

## Support

For issues, open a ticket at [github.com/sparkstrand/chat-application/issues](https://github.com/sparkstrand/chat-application/issues) and tag me on the PR [Abdul](@Abdulgithub0) or email [Bello](mailto:<EMAIL>) or [Bolu](mailto:<EMAIL>)
