import * as React from 'react';
import { HashRouter, Routes, Route, NavLink } from 'react-router-dom';
import styled from 'styled-components';

import {
  ProviderApiPage,
  ProviderAutoBootProps,
  ProviderEventsPage,
  ProviderPage,
  UseIntercomPage,
  UseIntercomTourPage,
  UseIntercomWithDelay,
} from './modules';
import { Page, Style } from './modules/common';

const Navigation = styled.ul`
  padding: 0;
  display: grid;
  grid-template-columns: 1fr;
  grid-row-gap: 1.75rem;
`;

const Link = styled(NavLink)`
  text-decoration: none;
  color: var(--dark);

  &:visited,
  &:active {
    text-decoration: none;
  }

  > code {
    font-size: 1rem;
  }
`;

const App = () => {
  return (
    <>
      <Style />
      <Page
        title="react-use-intercom"
        description="Playground to showcase the functionalities of this package"
      >
        <HashRouter>
          <Routes>
            <Route path="/provider" element={<ProviderPage />} />
            <Route path="/providerEvents" element={<ProviderEventsPage />} />
            <Route path="/providerApi" element={<ProviderApiPage />} />
            <Route
              path="/providerAutoBootProps"
              element={<ProviderAutoBootProps />}
            />
            <Route path="/useIntercom" element={<UseIntercomPage />} />
            <Route path="/useIntercomTour" element={<UseIntercomTourPage />} />
            <Route
              path="/useIntercomWithTimeout"
              element={<UseIntercomWithDelay />}
            />
            <Route path="/" element={
              <Navigation>
                <Link to="/provider">
                  <code>IntercomProvider</code>
                </Link>
                <Link to="/providerEvents">
                  <code>IntercomProvider with event callbacks</code>
                </Link>
                <Link to="/useIntercom">
                  <code>useIntercom</code>
                </Link>
                <Link to="/useIntercomTour">
                  <code>useIntercom with tour</code>
                </Link>
                <Link to="/useIntercomWithTimeout">
                  <code>useIntercom with delayed boot</code>
                </Link>
              </Navigation>
            } />
          </Routes>
        </HashRouter>
      </Page>
    </>
  );
};

export default App;
