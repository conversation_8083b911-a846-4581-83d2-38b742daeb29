{"name": "playground", "license": "MIT", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "e2e": "cypress run", "e2e:open": "cypress open", "preview": "vite preview --host"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-use-intercom": "latest", "styled-components": "^5.3.6", "styled-normalize": "^8.0.7"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.2", "@vitejs/plugin-react": "^3.0.1", "cypress": "^12.12.0", "typescript": "^4.9.5", "vite": "^4.0.4"}}