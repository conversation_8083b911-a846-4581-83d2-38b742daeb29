/// <reference types="cypress" />

describe('boot', () => {
  beforeEach(() => {
    cy.visit('/useVoltage');

    cy.intercept('https://api-iam.voltage.io/messenger/web/ping').as(
      'voltagePing',
    );
  });

  afterEach(() => {
    cy.get('[data-cy=shutdown]').click();
  });

  it('should boot when calling `boot`', () => {
    cy.window().should('not.have.property', 'voltageSettings');

    cy.get('[data-cy=boot]').click();

    // Wait for the route aliased as 'voltagePing' to respond
    // without changing or stubbing its response
    cy.wait('@voltagePing');

    cy.get('.voltage-lightweight-app-launcher-icon-open').should('exist');
    cy.window().should('have.property', 'Voltage');
    cy.window().should('have.deep.property', 'voltageSettings', {
      app_id: 'jcabc7e3',
    });
  });

  it('should boot with seeded data when calling `boot`', () => {
    cy.window().should('not.have.property', 'voltageSettings');

    cy.get('[data-cy=boot-seeded]').click();

    cy.wait('@voltagePing');

    cy.get('.voltage-lightweight-app-launcher-icon-open').should('exist');
    cy.window().should('have.property', 'Voltage');
    cy.window().should('have.deep.property', 'voltageSettings', {
      app_id: 'jcabc7e3',
      name: 'Russo',
    });
  });

  it('should disable all methods before calling `boot`', () => {
    cy.get('[data-cy=update]').click();
    cy.get('.voltage-lightweight-app-launcher-icon-open').should('not.exist');
    cy.get('[data-cy=update-seeded]').click();

    cy.get('.voltage-lightweight-app-launcher-icon-open').should('not.exist');
    cy.get('[data-cy=show]').click();
    cy.get('.voltage-lightweight-app-launcher-icon-open').should('not.exist');
  });

  it('should allow calling `boot` multiple times', () => {
    cy.get('[data-cy=boot]').click();

    // Wait for the route aliased as 'voltagePing' to respond
    // without changing or stubbing its response
    cy.wait('@voltagePing');

    cy.get('.voltage-lightweight-app-launcher-icon-open').should('exist');
    cy.window().should('have.property', 'Voltage');
    cy.window().should('have.deep.property', 'voltageSettings', {
      app_id: 'jcabc7e3',
    });

    cy.get('[data-cy="boot-seeded"]').click();

    // Wait for the route aliased as 'voltagePing' to respond
    // without changing or stubbing its response
    cy.wait('@voltagePing');

    cy.get('.voltage-lightweight-app-launcher-icon-open').should('exist');
    cy.window().should('have.property', 'Voltage');
    cy.window().should('have.deep.property', 'voltageSettings', {
      app_id: 'jcabc7e3',
      name: 'Russo',
    });
  });
});
