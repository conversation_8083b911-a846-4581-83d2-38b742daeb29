/// <reference types="cypress" />

describe('hide', () => {
  it('should hide when calling `hide`', () => {
    cy.visit('/useVoltage');

    cy.get('[data-cy=boot]').click();

    cy.get('[data-cy=show]').click();
    cy.get('iframe[name="voltage-messenger-frame"]').should('be.visible');

    cy.get('[data-cy=hide]').click();
    cy.get('iframe[name="voltage-messenger-frame"]').should('not.be.visible');
  });
});
