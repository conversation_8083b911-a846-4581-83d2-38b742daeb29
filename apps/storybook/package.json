{"name": "storybook", "version": "0.1.0", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build"}, "dependencies": {"@sparkstrand/chat": "workspace:^", "next": "15.2.4", "postcss": "^8.5.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@chromatic-com/storybook": "^3", "@eslint/eslintrc": "^3", "@storybook/addon-essentials": "^8.6.11", "@storybook/addon-onboarding": "^8.6.11", "@storybook/blocks": "^8.6.11", "@storybook/experimental-addon-test": "^8.6.11", "@storybook/experimental-nextjs-vite": "8.6.11", "@storybook/react": "^8.6.11", "@storybook/test": "^8.6.11", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.0.9", "@vitest/coverage-v8": "^3.0.9", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-plugin-storybook": "^0.12.0", "playwright": "^1.51.1", "storybook": "^8.6.11", "tailwindcss": "^4.0.17", "typescript": "^5", "vitest": "^3.0.9"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}