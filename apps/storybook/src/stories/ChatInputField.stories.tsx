import React from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { ChatInputField } from "@sparkstrand/chat";

const meta: Meta<typeof ChatInputField> = {
  title: "Components/ChatInputField",
  component: ChatInputField,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

// Story type
type Story = StoryObj<typeof ChatInputField>;

// Default story
export const Default: Story = {
  render: () => <ChatInputField />,
};

// Story with emoji picker open
export const WithEmojiPicker: Story = {
  render: () => {
    const [showEmojiPicker, setShowEmojiPicker] = React.useState(true);
    return <ChatInputField />;
  },
};

// Story with recording active
export const RecordingActive: Story = {
  render: () => {
    const [recording, setRecording] = React.useState(true);
    return <ChatInputField />;
  },
};
