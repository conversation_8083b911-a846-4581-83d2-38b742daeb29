import { <PERSON>a, StoryObj } from "@storybook/react";
import { ChatList } from "@sparkstrand/chat";
import { EventData } from "../../../../packages/chat/src/mockData/Data";

export default {
  title: "Components/ChatList",
  component: ChatList,
  args: {
    title: "Upcoming Events",
    length: EventData.length,
    eventText: "Event Location",
  },
} as Meta<typeof ChatList>;

export const Default: StoryObj<typeof ChatList> = {};
