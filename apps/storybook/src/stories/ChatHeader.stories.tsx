import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import {ChatHeader} from "@sparkstrand/chat";

const meta: Meta<typeof ChatHeader> = {
  title: "Components/ChatHeader",
  component: ChatHeader,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

// Story type
type Story = StoryObj<typeof ChatHeader>;

// Default story
export const Default: Story = {
  args: {
    title: "Chat Room",
    username: "<PERSON> Do<PERSON>",
    userImage: "https://placehold.co/50x50",
    typingUsers: [],
  },
};

// Story with typing users
export const TypingUsers: Story = {
  args: {
    title: "Chat Room",
    username: "<PERSON>",
    userImage: "https://placehold.co/50x50",
    typingUsers: ["<PERSON>", "<PERSON>"],
  },
};

// Story with no user image
export const NoUserImage: Story = {
  args: {
    title: "Chat Room",
    username: "<PERSON>",
    typingUsers: ["<PERSON>"],
  },
};
