import type { Meta, StoryObj } from "@storybook/react";
import { ChatTabMenu } from "@sparkstrand/chat";

const meta: Meta<typeof ChatTabMenu> = {
  title: "Components/ChatTabMenu",
  component: ChatTabMenu,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#ffffff" },
        { name: "dark", value: "#1e293b" },
      ],
    },
  },
  args: {
    pastLabel: "Past Events",
    upcomingLabel: "Upcoming Events",
    joinedLabel: "Joined Events",
  },
};

export default meta;

type Story = StoryObj<typeof ChatTabMenu>;

export const Default: Story = {
  args: {
    pastLabel: "Past Events",
    upcomingLabel: "Upcoming Events",
    joinedLabel: "Joined Events",
  },
};

export const CustomLabels: Story = {
  args: {
    pastLabel: "Previous Chats",
    upcomingLabel: "Future Chats",
    joinedLabel: "My Chats",
  },
};
