import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { TopBar } from "@sparkstrand/chat";

const meta: Meta<typeof TopBar> = {
  title: "Components/TopBar",
  component: TopBar,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  argTypes: {
    username: { control: "text" },
    buttonText: { control: "text" },
    hasNotification: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof TopBar>;

// Default state using default props
export const Default: Story = {};

// Custom username
export const CustomUsername: Story = {
  args: {
    username: "<PERSON>",
  },
};

// Custom button text
export const CustomButtonText: Story = {
  args: {
    buttonText: "Add New Chat",
  },
};

// Without notification
export const WithoutNotification: Story = {
  args: {
    hasNotification: false,
  },
};

// Interactive example
export const Interactive: Story = {
  args: {
    username: "<PERSON>",
    buttonText: "Create Ticket",
    hasNotification: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "This example demonstrates the interactive behavior of the TopBar component. The notification dot will disappear when clicked, and actions are logged to the console.",
      },
    },
  },
};
