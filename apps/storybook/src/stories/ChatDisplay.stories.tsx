
import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {Chat<PERSON>ontainer} from "@sparkstrand/chat";

type Message = {
  profile: string;
  message: string;
  time: string;
  user: string;
  msgColorScheme: string;
};

type MessagesByDay = {
  day: string;
  messages: Message[];
}[];

const meta: Meta<typeof ChatContainer> = {
  title: "Components/ChatContainer",
  component: ChatContainer,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#ffffff" },
        { name: "dark", value: "#1e293b" },
      ],
    },
  },
  args: {
    messages: [], 
  },
};

export default meta;

type Story = StoryObj<typeof ChatContainer>;

 const sampleMessages: MessagesByDay = [
   {
     day: "Today",
     messages: [
       {
         profile: "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "Hello there!",
         time: "2025-03-26T12:00:00.000Z",
         user: "You",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-man-with-glasses-illustration_1308-174706.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "How's it going?",
         time: "2025-03-26T12:05:00.000Z",
         user: "Jane Smith",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-girl-with-pigtails_1308-176684.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "I'm doing well, thanks!",
         time: "2025-03-26T12:10:00.000Z",
         user: "Alice Brown",
         msgColorScheme: "rgb(22 101 52)",
       },
     ],
   },
   {
     day: "Yesterday",
     messages: [
       {
         profile: "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "What about you?",
         time: "2025-03-25T12:15:00.000Z", // Changed to yesterday's date
         user: "You",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-girl-with-pigtails_1308-176684.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "All good here!",
         time: "2025-03-25T12:20:00.000Z",
         user: "Charlie Green",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-man-with-glasses-illustration_1308-174706.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "Nice to hear that!",
         time: "2025-03-25T12:25:00.000Z",
         user: "David Lee",
         msgColorScheme: "rgb(22 101 52)",
       },
     ],
   },
   {
     day: "March 24, 2025", // Specific date for older messages
     messages: [
       {
         profile: "https://img.freepik.com/free-vector/young-girl-with-pigtails_1308-176684.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "Did you watch the game?",
         time: "2025-03-24T12:30:00.000Z",
         user: "Emma Wilson",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-man-with-glasses-illustration_1308-174706.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "Yes! It was amazing!",
         time: "2025-03-24T12:35:00.000Z",
         user: "Frank Carter",
         msgColorScheme: "rgb(22 101 52)",
       },
     ],
   },
   {
     day: "March 23, 2025",
     messages: [
       {
         profile: "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "Which team were you supporting?",
         time: "2025-03-23T12:40:00.000Z",
         user: "You",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-vector/young-man-with-glasses-illustration_1308-174706.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "The home team, of course!",
         time: "2025-03-23T12:45:00.000Z",
         user: "Harry Evans",
         msgColorScheme: "rgb(22 101 52)",
       },
     ],
   },
   {
     day: "March 22, 2025",
     messages: [
       {
         profile: "https://img.freepik.com/free-vector/young-girl-with-pigtails_1308-176684.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "They played really well!",
         time: "2025-03-22T12:50:00.000Z",
         user: "Isla Parker",
         msgColorScheme: "rgb(22 101 52)",
       },
       {
         profile: "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg?ga=GA1.1.2009282242.1740508839&semt=ais_hybrid",
         message: "I agree!",
         time: "2025-03-22T12:55:00.000Z",
         user: "You",
         msgColorScheme: "rgb(22 101 52)",
       },
     ],
   },
 ];

export const Default: Story = {
  args: {
    messages: sampleMessages,
  },
};


