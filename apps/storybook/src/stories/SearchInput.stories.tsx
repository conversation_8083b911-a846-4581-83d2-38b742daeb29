import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { SearchInput } from "@sparkstrand/chat";

const meta: Meta<typeof SearchInput> = {
  title: "Components/SearchInput",
  component: SearchInput,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

// Story type
type Story = StoryObj<typeof SearchInput>;

// Default story
export const Default: Story = {
  args: {
    placeholder: "Search for events, chats...",
    onSearch: (query) => alert(`Searching for: ${query}`),
  },
};

// Story with a custom placeholder
export const CustomPlaceholder: Story = {
  args: {
    placeholder: "Search for users or messages...",
    onSearch: (query) => alert(`Searching for: ${query}`),
  },
};

export const NoAction: Story = {
  args: {
    placeholder: "Search without action...",
    onSearch: () => alert("No action defined!"),
  },
};
