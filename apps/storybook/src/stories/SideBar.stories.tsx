import React from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { SideBar } from "@sparkstrand/chat";
import {
  FaCog,
  FaSignOutAlt,
  FaCalendarAlt,
  FaHeadset,
  FaUser,
  FaEnvelope,
} from "react-icons/fa";

const meta: Meta<typeof SideBar> = {
  title: "Components/SideBar",
  component: SideBar,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof SideBar>;

// Default state using default props
export const Default: Story = {};

// Custom state with user-provided props
export const CustomProfile: Story = {
  args: {
    profileImageSrc: "https://randomuser.me/api/portraits/women/44.jpg",
    userName: "<PERSON>",
    userId: "User ID: SJ2023",
  },
};

// Custom navigation and footer buttons
export const CustomButtons: Story = {
  args: {
    navButtons: [
      { label: "Dashboard", icon: <FaUser /> },
      { label: "Messages", icon: <FaEnvelope /> },
      { label: "Events", icon: <FaCalendarAlt /> },
    ],
    footerButtons: [
      { label: "Settings", icon: <FaCog /> },
      { label: "Logout", icon: <FaSignOutAlt /> },
    ],
  },
};

// Responsive view demonstration
export const Responsive: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

// With click handlers
export const WithClickHandlers: Story = {
  args: {
    navButtons: [
      {
        label: "Events",
        icon: <FaCalendarAlt />,
        onClick: () => alert("Events clicked"),
      },
      {
        label: "Support",
        icon: <FaHeadset />,
        onClick: () => alert("Support clicked"),
      },
    ],
    footerButtons: [
      {
        label: "Settings",
        icon: <FaCog />,
        onClick: () => alert("Settings clicked"),
      },
      {
        label: "Logout",
        icon: <FaSignOutAlt />,
        onClick: () => alert("Logout clicked"),
      },
    ],
  },
};
