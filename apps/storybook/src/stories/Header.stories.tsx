import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Header from "@sparkstrand/chat";

const meta: Meta<typeof Header> = {
  title: "Components/Header", // Storybook sidebar path
  component: Header,
  tags: ["autodocs"], // Enables automatic documentation
 
};

export default meta;

// Story type
type Story = StoryObj<typeof Header>;

// Default story
export const Default: Story = {
  render: () => <Header />,
};

// You can add more story variants here
export const WithCustomText: Story = {
  render: () => <h1>Custom Header Text</h1>,
};