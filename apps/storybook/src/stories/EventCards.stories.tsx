import { Meta, StoryObj } from "@storybook/react";
import { EventCards } from "@sparkstrand/chat";
import { EventData } from "../../../../packages/chat/src/mockData/Data";


export default {
  title: "Components/EventCards",
  component: EventCards,
  args: {
    eventText: "Event Location",
    date: EventData[0].date,
    title: EventData[0].title,
    attendeeCount: EventData[0].attendeeCount,
    attendeeImages: EventData[0].attendees,
    eventThumbnail: EventData[0].image,
    attendeesText: EventData[0].attendeesText,
    viewEventText: EventData[0].viewEventText,
  },
} as Meta<typeof EventCards>;

export const Default: StoryObj<typeof EventCards> = {};
