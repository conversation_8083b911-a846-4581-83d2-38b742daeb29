import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { ChatSideBar } from "@sparkstrand/chat";
import {
  MemberType,
  ImageType,
  DocumentType,
  ChatSidebarData,
} from "../../../../packages/chat/src/mockData/chatSidebarData";

const defaultData: ChatSidebarData = {
  roomInfo: {
    title: "VolleyBall Friends",
    description:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem asperiores sequi hic dignissimos rerum fugiat mollitia eum ea tempora beatae.",
    attendeesCount: 26,
    roomImage:
      "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
  },
  notificationsEnabled: false,
  media: {
    images: [
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.outdoordepot.ph/cdn/shop/products/image_3.png?v=1665712037",
      },
      {
        src: "https://www.sportyexpats.fr/_next/image?url=%2Fimages%2Fabout.png&w=1920&q=75",
      },
    ],
    documents: [
      {
        id: "3",
        url: "https://propaddy2.s3.eu-north-1.amazonaws.com/uploads/BulkPropertySheet.xlsx",
        name: "Financial Statements.xlsx",
        fileType: "xlsx",
      },
    ],
  },
  members: [
    {
      id: "1",
      name: "Levi Rcgh",
      status: "online",
      role: "moderator",
    },
    ...Array(6).fill({
      id: "2",
      name: "Member Name",
      status: "online",
    }),
  ],
};

const meta: Meta<typeof ChatSideBar> = {
  title: "Components/ChatSideBar",
  component: ChatSideBar,
  tags: ["autodocs"],
  parameters: {
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#ffffff" },
        { name: "dark", value: "#1e293b" },
      ],
    },
  },
  args: {
    data: defaultData,
    content: {
      eventInfo: "Event Room Info",
      room: "room",
      attendies: "attendies",
      description: "Description",
      notifications: "Notifications",
      medias: "Media",
      files: "Files",
      member: "Members",
      userType: "Moderator",
    },
  },
};

export default meta;

type Story = StoryObj<typeof ChatSideBar>;

export const Default: Story = {
  // Uses the default args from meta
};

export const NoMediaFiles: Story = {
  args: {
    data: {
      ...defaultData,
      media: {
        images: [],
        documents: [],
      },
    },
    content: {
      eventInfo: "Event Room Info",
      room: "room",
      attendies: "attendies",
      description: "Description",
      notifications: "Notifications",
      medias: "Media",
      files: "Files",
      member: "Members",
      userType: "Moderator",
    },
  },
};

export const ManyMembers: Story = {
  args: {
    data: {
      ...defaultData,
      members: [
        ...defaultData.members,
        ...Array(10).fill({
          id: "extra-member",
          name: "Additional Member",
          status: "online",
          role: "member",
        }),
      ],
    },
    content: {
      eventInfo: "Event Room Info",
      room: "room",
      attendies: "attendies",
      description: "Description",
      notifications: "Notifications",
      medias: "Media",
      files: "Files",
      member: "Members",
      userType: "Moderator",
    },
  },
};
