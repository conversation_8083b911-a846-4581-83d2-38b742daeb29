export async function getClientToken(externalId: string): Promise<string> {
  const reqBody = {
    apiKey: process.env.API_KEY as string,
    apiKeySecret: process.env.API_SECRET as string,
    metaData: { externalId, name: "<PERSON>otes<PERSON>" }
  };


  const response = await fetch('https://chat-application-h0xp.onrender.com/api/v1/guests/initiate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(reqBody)
  });

  const responseData = await response.json(); 
  console.log('Response JSON:', responseData);

  if (!responseData.success) { 
    throw new Error(responseData.message || 'Failed to get client token');
  }

  const clientToken = responseData.data?.clientToken;
  if (!clientToken) {
    throw new Error('Client token not found in response');
  }

  return clientToken;
}
