import { getClientToken } from "@/utils/initChatForUser";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest)  {
  const { externalId } = await req.json();
  console.log(externalId);

  const clientToken = await getClientToken(externalId);
  if(!clientToken){
    return NextResponse.json({ error: 'Failed to get client token' }, { status: 500 });
  }
  return NextResponse.json({ clientToken }, { status: 200 });
}