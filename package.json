{"name": "voltage", "private": true, "version": "1.0.0", "scripts": {"build": "turbo build", "dev": "turbo dev", "typecheck": "turbo typecheck", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "publish-packages": "turbo run build lint test && changeset version && changeset publish"}, "devDependencies": {"prettier": "^3.2.5", "tsup": "^8.2.4", "turbo": "^2.4.4", "typescript": "^5.4.5"}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.7.0", "workspaces": ["packages/*", "apps/*"], "dependencies": {"@changesets/cli": "^2.27.7"}, "repository": {"type": "git", "url": "https://github.com/sparkstrand/chat-application"}}