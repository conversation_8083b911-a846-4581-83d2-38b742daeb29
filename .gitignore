# dependencies
node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
storybook-static

#.yarn/

.vscode/

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Ignore .env
.env

# Ignore junit output
packages/api/test-results

# Ignore yarn
.yarn/install-state.gz

# Ignore npmrc
.npmrc

# Ignore failure.txt
failure.txt

# Ignore inspiration.md
inspiration.md


